﻿using HT.Blazor.Models.Form;
using HT.Blazor.Models.Field;
using HT.Portal.Components.Base;
using HT.Shared.Models.Route;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace HT.Portal.Pages.Route.View.Sections.DriverRoute;

public class DriverRouteBase : ComponentServiceBase
{
    [Parameter] public ViewRoute ViewRoute { get; set; }

    public bool CreateOrderDialogOpen { get; set; }
    public bool AddWillRingsDialogOpen { get; set; }
    public bool AddWillRingsLoading { get; set; }

    public async Task DrawMap()
    {
        List<string> addressesList =
        [
            $"{this.ViewRoute.RouteModel.StartAddress.Latitude},{this.ViewRoute.RouteModel.StartAddress.Longitude}"
        ];

        foreach (var item in this.ViewRoute.RouteModel.RouteLines.OrderBy(o => o.StopNumber))
        {
            addressesList.Add(item.DeliveryAddressLatitude + "," + item.DeliveryAddressLongitude);
        }

        addressesList.Add($"{this.ViewRoute.RouteModel.EndAddress.Latitude},{this.ViewRoute.RouteModel.EndAddress.Longitude}");

        try
        {
            await JsRuntime.InvokeVoidAsync("initMapWithAddresses", addressesList);
        }
        catch (Exception)
        {
            await this.ShowErrorNotification("Failed to draw map.");
        }
    }


    public bool AddOrderOpen { get; set; }

    public void AddOrderClick()
    {
        this.AddOrderOpen = true;
    }


    public bool StartAddressEdit { get; set; }
    public bool EditAddressOpen { get; set; }

    public void EditAddressClick(bool startAddressEdit)
    {
        this.StartAddressEdit = startAddressEdit;
        this.EditAddressOpen = true;
    }

    public async Task AddOrderToRoute(Guid orderId)
    {
        this.AddOrderOpen = false;
        this.StateHasChanged();

        _ = await this.ApiService.PostJsonAsync<FormResponseModel<bool>>($"/Route/AddOrderToRoute?routeId={ViewRoute.Id}&orderId={orderId}");

        await ViewRoute.GetRouteModel();
        await DrawMap();
    }

    public async Task DeleteOrderOnRoute(RouteLineModel routeLineModel)
    {
        _ = await this.ApiService.PostJsonAsync<FormResponseModel<bool>>($"/Route/DeleteOrderOnRoute?routeLineId={routeLineModel.Id}");

        await ViewRoute.GetRouteModel();
        await DrawMap();
    }

    public async Task DeleteVisitOnRoute(RouteLineModel routeLineModel)
    {
        _ = await this.ApiService.PostJsonAsync<FormResponseModel<bool>>($"/Route/DeleteVisitOnRoute?routeLineId={routeLineModel.Id}");

        await ViewRoute.GetRouteModel();
        await DrawMap();
    }

    public async Task MoveRouteLineUp(RouteLineModel routeLineModel)
    {
        this.MoveItem(routeLineModel, "up");

        _ = await this.ApiService.PostJsonAsync<FormResponseModel<bool>>($"/Route/UpdateRouteLinesOrder", ViewRoute.RouteModel);

        await ViewRoute.GetRouteModel();
        await DrawMap();
    }

    public async Task MoveRouteLineDown(RouteLineModel routeLineModel)
    {
        this.MoveItem(routeLineModel, "down");

        _ = await this.ApiService.PostJsonAsync<FormResponseModel<bool>>($"/Route/UpdateRouteLinesOrder", ViewRoute.RouteModel);

        await ViewRoute.GetRouteModel();
        await DrawMap();
    }

    public void MoveItem(RouteLineModel itemToMove, string direction)
    {
        int indexToMove = ViewRoute.RouteModel.RouteLines.FindIndex(item => item.Equals(itemToMove));

        if (direction == "up" && indexToMove > 0)
        {
            var previousItem = ViewRoute.RouteModel.RouteLines[indexToMove - 1];
            ViewRoute.RouteModel.RouteLines[indexToMove - 1] = itemToMove;
            ViewRoute.RouteModel.RouteLines[indexToMove] = previousItem;
        }
        else if (direction == "down" && indexToMove >= 0 && indexToMove < ViewRoute.RouteModel.RouteLines.Count - 1)
        {
            var nextItem = ViewRoute.RouteModel.RouteLines[indexToMove + 1];
            ViewRoute.RouteModel.RouteLines[indexToMove + 1] = itemToMove;
            ViewRoute.RouteModel.RouteLines[indexToMove] = nextItem;
        }

        int count = 1;
        foreach (var item in ViewRoute.RouteModel.RouteLines)
        {
            item.StopNumber = count;
            count++;
        }
    }

    public void CreateOrderClick()
    {
        this.CreateOrderDialogOpen = true;
    }

    public void AddWillRingsClick()
    {
        if (!this.AddWillRingsLoading)
        {
            this.AddWillRingsDialogOpen = true;
        }
    }

    public async Task AddWillRingsToRoute(List<Guid> selectedTagIds)
    {
        this.AddWillRingsDialogOpen = false;
        this.AddWillRingsLoading = true;
        this.StateHasChanged();

        var response = await this.ApiService.PostJsonAsync<FormResponseModel<int>>($"/Route/AddWillRingsToRoute", new { RouteId = ViewRoute.Id, TagIds = selectedTagIds });

        if (response.Success)
        {
            await this.ShowSuccessNotification($"Added {response.Data} Will Ring customers to route");
            await ViewRoute.GetRouteModel();
            await DrawMap();
        }
        else
        {
            var errorMessage = response.Errors.Any() ? string.Join(", ", response.Errors.Select(e => e.Message)) : "Failed to add Will Ring customers";
            await this.ShowErrorNotification(errorMessage);
        }

        this.AddWillRingsLoading = false;
        this.StateHasChanged();
    }



    #region Edit Order

    public bool ShowEditOrderDialogOpen { get; set; }

    public Guid? EditOrderRouteLineId { get; set; }

    public void ShowEditOrderDialogClick(Guid routeLineId)
    {
        this.EditOrderRouteLineId = routeLineId;
        this.ShowEditOrderDialogOpen = true;
        this.StateHasChanged();
    }

    public void CloseEditOrderDialogClick()
    {
        this.EditOrderRouteLineId = null;
        this.ShowEditOrderDialogOpen = false;
        this.StateHasChanged();
    }

    #endregion
}
