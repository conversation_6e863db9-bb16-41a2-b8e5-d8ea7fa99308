﻿@inherits DriverRouteBase

<section class="bs np">
    <div class="subtitle route-subtitle">
        <div>Orders on Route</div>
        <div>
            <div class="optimisation-status">Optimisation: @((RouteOptimisedStatus)this.ViewRoute.RouteModel.RouteOptimisedStatus)</div>
            <div class="button inline small" @onclick="this.ViewRoute.OptimiseRouteClick">Optimise Route</div>
            <div class="button inline small" @onclick="this.ViewRoute.MoveOrderClick">Move Orders</div>
            <div class="button inline small" @onclick="AddOrderClick">Add Existing Order to Route</div>
            <div class="button inline small" @onclick="CreateOrderClick">Create New Order & Add to Route</div>
            <div class="button inline small @(AddWillRingsLoading ? "loading" : "")" @onclick="AddWillRingsClick">
                @if (AddWillRingsLoading)
                {
                    <div class="lds-ring" style="width: 16px; height: 16px; margin: 0 8px 0 0; display: inline-block;">
                        <div style="width: 12px; height: 12px; margin: 2px; border-width: 2px;"></div>
                    </div>
                }
                Add Will Rings
            </div>
        </div>
        
    </div>

    <div class="route-container">
        <div class="stop-list">
            <div class="disabled">
                <div></div>
                <div></div>
                <div><div class="stop-number start-stop">Start</div></div>
                <div>
                    @this.ViewRoute.RouteModel.StartAddress.FullAddress
                </div>
                @* <div>09:30</div> *@
                <div class="stop-type"><RouteIcon /><div>Start</div></div>
                <div><span class="button tiny inline" @onclick="(() => EditAddressClick(true))">Edit</span></div>
                <div></div>
            </div>
            @foreach (var routeLine in this.ViewRoute.RouteModel.RouteLines.OrderBy(o => o.StopNumber))
            {
                <div class="@(routeLine.VisitId != null ? "visit" : "")">
                    <div class="route-check">
                        <CheckboxField @bind-Value="@routeLine.Checked" />
                    </div>
                    <div class="move-route">
                        <div class="move-route-up" @onclick="(() => MoveRouteLineUp(routeLine))"></div>
                        <div class="move-route-down" @onclick="(() => MoveRouteLineDown(routeLine))"></div>
                    </div>
                    <div>
                        <div class="stop-number">@routeLine.StopNumber</div>
                    </div>
                    <div class="stop-address">
                        <div class="route-address">
                            @if (!string.IsNullOrEmpty(routeLine?.AddressName))
                            {
                                @routeLine.AddressName<br />
                            }
                            @routeLine.DeliveryAddressLine1
                            <br />
                            @routeLine.DeliveryAddressPostcode
                        </div>

                        @if (routeLine.CustomerDeliveryNotes != null)
                        {
                            <div>Customer Notes: @routeLine.CustomerDeliveryNotes </div>
                        }
                        @if (routeLine.OrderDeliveryNotes != null)
                        {
                            <div>Order Notes: @routeLine.OrderDeliveryNotes </div>
                        }
                        @if (routeLine.RoundNotes != null)
                        {
                            <div>Round Notes: @routeLine.RoundNotes </div>
                        }
                    </div>
                    <div class="route-order">
                        @foreach (var product in routeLine.Products)
                        {
                            <div class="route-order-line">
                                <div>@product.Name</div>
                                <div>@product.QuantityTotal</div>
                            </div>
                        }
                    </div>
                    @* <div class="stop-type"><OrderIcon /><div>Drop</div></div> *@

                    @if (routeLine.OrderId != null)
                    {
                        <div class="order-dropdown-container" @onclick="(() => this.ShowEditOrderDialogClick(routeLine.Id))">

                            <div class="button-text">
                                Edit
                                @* Order #@routeLine.OrderNumber *@
                            </div>
                            <div class="order-dropdown">

                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Product Name</th>
                                            <th>Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var product in routeLine.Products)
                                        {
                                            <tr>
                                                <td>@product.Name</td>
                                                <td>@product.QuantityTotal</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="remove" @onclick="() => DeleteOrderOnRoute(routeLine)">
                            <CrossIcon />
                        </div>
                    }
                    else
                    {
                        <div></div>
                        <div class="remove" @onclick="() => DeleteVisitOnRoute(routeLine)">
                            <CrossIcon />
                        </div>
                    }

                    
                </div>
            }
            <div class="disabled">
                <div></div>
                <div></div>
                <div><div class="stop-number end-stop">End</div></div>
                <div>
                    @this.ViewRoute.RouteModel.EndAddress.FullAddress
                </div>
                @* <div>17:30</div> *@
                <div class="stop-type"><RouteIcon /><div>End</div></div>
                <div><span class="button tiny inline" @onclick="(() => EditAddressClick(false))">Edit</span></div>
                <div></div>
            </div>
        </div>
        <div class="route-map">
            <div id="map" style="height:600px; width:100%"></div>
        </div>
    </div>
</section>

<DialogControl @bind-IsOpen="AddOrderOpen" Title="Add Order" Width="600px">
    <HT.Portal.Pages.Route.View.Dialogs.AddOrder.AddOrder ViewRouteBase="this.ViewRoute" />
</DialogControl>


<DialogControl @bind-IsOpen="ShowEditOrderDialogOpen" Title="Edit Order" Width="1100px">
    <HT.Portal.Pages.Route.View.Sections.DriverReturned.EditOrder.EditOrder DriverRoute="this" />
</DialogControl>

<DialogControl @bind-IsOpen="EditAddressOpen" Title="Edit Address" Width="600px">
    <HT.Portal.Pages.Route.View.Sections.DriverRoute.EditAddressDialog.EditAddressDialog ViewRouteBase="this.ViewRoute" />
</DialogControl>

<DialogControl @bind-IsOpen="CreateOrderDialogOpen" Title="Create Order" Width="1000px">
    <HT.Portal.Pages.Order.Create.CreateOrder RouteId="this.ViewRoute.RouteModel.Id"/>
</DialogControl>

<DialogControl @bind-IsOpen="AddWillRingsDialogOpen" Title="Add Will Rings" Width="600px">
    <HT.Portal.Pages.Route.View.Dialogs.AddWillRings.AddWillRings DriverRoute="this" />
</DialogControl>