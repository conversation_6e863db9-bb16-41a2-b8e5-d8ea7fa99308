using HT.Blazor.Models.Field;
using HT.Portal.Components.Base;
using HT.Portal.Pages.Route.View.Sections.DriverRoute;
using Microsoft.AspNetCore.Components;

namespace HT.Portal.Pages.Route.View.Dialogs.AddWillRings;

public class AddWillRingsBase : ComponentServiceBase
{
    [Parameter] public DriverRouteBase DriverRoute { get; set; }
    
    public List<Guid> SelectedTags { get; set; } = new();
    public List<DropdownListItem> CustomerTagsDropdownList { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        this.CustomerTagsDropdownList = await this.ApiService.PostJsonAsync<List<DropdownListItem>>("/CustomerTag/ListCustomerTagsDropdown");
    }

    public async Task SubmitClick()
    {
        if (this.SelectedTags?.Count > 0)
        {
            await this.DriverRoute.AddWillRingsToRoute(this.SelectedTags);
        }
    }

    public void CancelClick()
    {
        this.DriverRoute.AddWillRingsDialogOpen = false;
    }
}