﻿@page "/"
@model HT.Portal.Pages._HostModel
@using HT.Portal
@using HT.Portal.Services;
@using Microsoft.AspNetCore.Components.Web
@namespace HT.Portal.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@inject CurrentUserService CurrentUserService

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <base href="~/" />
    <title>Heavy Tech</title>

    @* Favicons *@
    <link rel="icon" type="image/png" href="favicon.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
    <meta name="msapplication-TileColor" content="#da532c">

    @* Select2 *@
    <script src="_content/HT.Blazor/jquery/jquery-3.3.1.min.js?@Program.Version"></script>
    <script src="_content/HT.Blazor/select2/select2.min.js?@Program.Version"></script>
    <script src="_content/HT.Blazor/javascript.js?@Program.Version"></script>

    @* Oh Snap *@
    <link href="~/js/ohsnap/ohsnap.css?@Program.Version" rel="stylesheet" />
    <script src="~/js/ohsnap/ohsnap.min.js?@Program.Version"></script>
    <script src="~/js/ht.js?@Program.Version"></script>

    <script src="~/js/signature.js?@Program.Version"></script>

@*     <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-855FKZ7V6K"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-855FKZ7V6K');
    </script> *@

    @* Fonts *@
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />

    <script>
        window.exportAsFile = function (data, filename) {
            var link = document.createElement('a');
            link.download = filename;
            
            // Determine MIME type based on file extension
            var mimeType = 'application/octet-stream'; // Default
            if (filename.endsWith('.csv')) {
                mimeType = 'text/csv';
            } else if (filename.endsWith('.xlsx')) {
                mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            } else if (filename.endsWith('.zip')) {
                mimeType = 'application/zip';
            }
            
            link.href = 'data:' + mimeType + ';base64,' + data;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        };
    </script>

    @* Server Render *@
    <component type="typeof(HeadOutlet)" render-mode="Server" />
</head>
<body>

    <app>
        <component type="typeof(App)" render-mode="Server" />
    </app>

    <div id="ohsnap"></div>

    <div id="blazor-error-ui" style="display: none">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

@*     <!--Start of Tawk.to Script-->
    <script type="text/javascript">
        var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();
        window.Tawk_API.visitor = {
            email: '@(CurrentUserService.UserEmail)'
        };
        (function () {
            var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
            s1.async = true;
            s1.src = 'https://embed.tawk.to/63d6c782c2f1ac1e20303a62/1gnvfml7g';
            s1.charset = 'UTF-8';
            s1.setAttribute('crossorigin', '*');
            s0.parentNode.insertBefore(s1, s0);
        })();
    </script>
    <!--End of Tawk.to Script--> *@

    <script src="_framework/blazor.server.js?@Program.Version"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCz8-Hg-y1_Xg6WPvJvwYBAwjF3NHC713k&callback=mapLoaded" async defer></script>
    <script src="~/js/googlemaps.js?@Program.Version"></script>
</body>
</html>
