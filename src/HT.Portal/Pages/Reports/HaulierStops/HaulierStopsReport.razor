@using HT.Shared.Models.HaulierRoute
@inherits HaulierStopsReportBase
@page "/reports/HaulierStops"

<header>
    <div class="title"><ActivityIcon /><a href="/reports">Reports</a>Stops</div>

    <div>
        @if (SelectedStops.Any(x => x.ProofOfDeliveryId != null))
        {
            <div class="button primary small @(IsExportingPdfs ? "loading" : "")" @onclick="ExportSelectedProofOfDeliveries">
                @if (IsExportingPdfs)
                {
                    <div class="lds-ring" style="width: 16px; height: 16px; margin: 0 8px 0 0; display: inline-block;">
                        <div style="width: 12px; height: 12px; margin: 2px; border-width: 2px;"></div>
                    </div>
                }
                <InvoiceIcon />Export Proof of Deliveries (@SelectedStops.Count(x => x.ProofOfDeliveryId != null))
            </div>
        }
        <div class="button tertiary small" @onclick="ClickExport"><InvoiceIcon />Export CSV</div>
    </div>
</header>


<section class="list-section ">

    @if (this.GridOptions != null)
    {
        <Grid @ref="Grid"
              GridOptions="GridOptions"
              GetDataAsync="GetGridDataAsync"
              ClickGridItemAsync="ClickGridItemAsync"
              GetExportDataMapping="GetExportDataMapping"
              EnableRowSelection="true"
              OnSelectedItemsChanged="OnSelectedItemsChangedCallback"
              IsRowSelectable="@IsStopSelectable">

            <HeaderTemplate>
                <th>Status</th>
                <th>Date</th>
                <th>Order No.</th>
                <th>Stop No.</th>
                <th>Customer</th>
                <th>Contract Name</th>
                <th>Product</th>
            </HeaderTemplate>

            <ResultsTemplate>
                <td><div class="status-pill @((HaulierStopStatus)context.Status)">@((HaulierStopStatus)context.Status)</div></td>
                <td>@context.RouteDate.ToShortDateString()</td>
                <td>@context.OrderNumber</td>
                <td>@context.StopNumberString</td>
                <td>@context.CustomerName</td>
                <td>@context.ContractName</td>
                <td>@context.CustomerProduct</td>
            </ResultsTemplate>

        </Grid>
    }
    else
    {
        <Loading />
    }

</section>