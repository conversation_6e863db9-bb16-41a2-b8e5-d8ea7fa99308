﻿using HT.Blazor.Controls.Grid;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Grid.Enums;
using HT.Blazor.Models.Grid;
using HT.Portal.Components.Base;
using System.Text;
using System.Text.Json;
using HT.Shared.Models.HaulierRoute;
using Microsoft.JSInterop;
using Microsoft.AspNetCore.Components;

namespace HT.Portal.Pages.Reports.HaulierStops;

public class HaulierStopsReportBase : ComponentServiceBase
{
    [Parameter] public Guid? ContractId { get; set; }

    public Grid<HaulierStopListModel> Grid { get; set; }
    public GridOptionsModel GridOptions { get; set; }
    
    // Multi-select and PDF export functionality
    public List<HaulierStopListModel> SelectedStops { get; set; } = new();
    public bool IsExportingPdfs { get; set; }

    // Selection management properties
    public HashSet<Guid> SelectedProofOfDeliveryIds { get; set; } = new HashSet<Guid>();
    public bool IsSelectAllChecked { get; set; } = false;
    public List<HaulierStopListModel> CurrentGridItems { get; set; } = new List<HaulierStopListModel>();

    protected override async Task OnInitializedAsync()
    {
        this.GridOptions = new GridOptionsModel
        {
            SortableItems =
        [
            new GridSortableItemModel
            {
                DisplayName = "Route Date",
                PropertyName = "RouteDate"
            },
        ],
            Filters =
            [
                new GridFilterItemModel
                {
                    DisplayName = "Status",
                    PropertyName = "Status",
                    PropertyType = (int)PropertyType.IntType,
                    Values =
                    [
                        new DropdownListItem
                        {
                            Id = "1",
                            Name = "Open",
                            Selected = true
                        },
                        new DropdownListItem
                        {
                            Id = "2",
                            Name = "Completed",
                            Selected = true
                        },
                        new DropdownListItem
                        {
                            Id = "3",
                            Name = "Invoiced",
                            Selected = true
                        },
                        new DropdownListItem
                        {
                            Id = "4",
                            Name = "Deleted",
                        },
                    ]
                },
                new GridFilterItemModel
                {
                    DisplayName = "Stop Date Range",
                    PropertyName = "RouteDate",
                    PropertyType = (int)PropertyType.DateTime
                }
            ],
            SearchableItems = [
                
                new GridSearchItemModel {
                    DisplayName = "Customer",
                    PropertyName = "CustomerName"
                },
                new GridSearchItemModel {
                    DisplayName = "Product",
                    PropertyName = "CustomerProduct"
                },
                new GridSearchItemModel {
                    DisplayName = "Contract Name",
                    PropertyName = "ContractName"
                },
                new GridSearchItemModel {
                    DisplayName = "Order No",
                    PropertyName = "OrderNumber"
                },
                new GridSearchItemModel {
                    DisplayName = "Stop No",
                    PropertyName = "StopNumberString"
                },
            ]
        };
    }

    public async Task<GridResultModel<HaulierStopListModel>> GetGridDataAsync(GridParametersModel gridParametersModel)
    {
        if (this.ContractId != null)
        {
            gridParametersModel.ParameterId = this.ContractId.Value;
        }

        var result = await this.ApiService.PostJsonAsync<GridResultModel<HaulierStopListModel>>($"/Report/ListHaulierStops", gridParametersModel);

        // Update current grid items for selection management
        if (result?.Results != null)
        {
            OnGridDataLoaded(result.Results);
        }

        return result;
    }

    public async Task ClickGridItemAsync(HaulierStopListModel listItemModel)
    {
        if (listItemModel.ProofOfDeliveryId != null)
        {
            string url = $"/ProofOfDeliveryPrint/{listItemModel.ProofOfDeliveryId.Value}";
            await JsRuntime.InvokeVoidAsync("open", url, "_blank");
        }
    }

    public async Task ClickExport()
    {
        await this.Grid.ExportToCSV();
    }

    public GridExportModel GetExportDataMapping(IEnumerable<HaulierStopListModel> results)
    {
        var columnHeaders = new List<string> {
            "Status",
            "Date",
            "Order No",
            "Customer",
            "Contract Name",
            "Product"
        };

        var sb = new StringBuilder();

        foreach (var item in results)
        {
            sb.AppendLine(string.Join(",", new List<string>
            {
                item.Status.ToString().Replace(",",""),
                item.RouteDate.ToString(),
                item.OrderNumber.Replace(",",""),
                item.CustomerName.Replace(",",""),
                item.ContractName.Replace(",",""),
                item.CustomerProduct.Replace(",","")
            }));

        }

        return new GridExportModel
        {
            FileName = "stops-report.csv",
            Headers = columnHeaders,
            Data = sb,
        };
    }

    // Selection management methods
    public void OnItemSelectionChanged(HaulierStopListModel item, bool isSelected)
    {
        if (item.ProofOfDeliveryId == null) return;

        if (isSelected)
        {
            SelectedProofOfDeliveryIds.Add(item.ProofOfDeliveryId.Value);
        }
        else
        {
            SelectedProofOfDeliveryIds.Remove(item.ProofOfDeliveryId.Value);
        }

        UpdateSelectAllState();
        StateHasChanged();
    }

    public void OnSelectAllChanged(bool isSelected)
    {
        IsSelectAllChecked = isSelected;
        
        if (isSelected)
        {
            // Select all items that have ProofOfDeliveryId
            foreach (var item in CurrentGridItems.Where(x => x.ProofOfDeliveryId != null))
            {
                SelectedProofOfDeliveryIds.Add(item.ProofOfDeliveryId.Value);
            }
        }
        else
        {
            // Deselect all items
            SelectedProofOfDeliveryIds.Clear();
        }

        StateHasChanged();
    }

    public bool IsItemSelected(HaulierStopListModel item)
    {
        return item.ProofOfDeliveryId != null && SelectedProofOfDeliveryIds.Contains(item.ProofOfDeliveryId.Value);
    }

    public bool CanSelectItem(HaulierStopListModel item)
    {
        return item.ProofOfDeliveryId != null;
    }

    private void UpdateSelectAllState()
    {
        var selectableItems = CurrentGridItems.Where(x => x.ProofOfDeliveryId != null).ToList();
        if (selectableItems.Any())
        {
            var selectedCount = selectableItems.Count(x => SelectedProofOfDeliveryIds.Contains(x.ProofOfDeliveryId.Value));
            IsSelectAllChecked = selectedCount == selectableItems.Count;
        }
        else
        {
            IsSelectAllChecked = false;
        }
    }

    public async Task ExportSelectedPdfs()
    {
        if (!SelectedProofOfDeliveryIds.Any())
        {
            return;
        }

        this.Loading(true);

        try
        {
            var exportModel = new ProofOfDeliveryBulkExportModel
            {
                ProofOfDeliveryIds = SelectedProofOfDeliveryIds.ToList()
            };

            // Call the API and get the ZIP file as base64
            var result = await this.ApiService.PostJsonAsync<BulkExportResult>($"/ProofOfDelivery/BulkExportPdf", exportModel);
            
            if (result != null && !string.IsNullOrEmpty(result.FileBase64))
            {
                string fileName = $"ProofOfDelivery-Bulk-{DateTime.Now:yyyyMMdd-HHmmss}.zip";
                await JsRuntime.InvokeVoidAsync("exportAsFile", result.FileBase64, fileName);
            }
        }
        finally
        {
            this.Loading(false);
        }
    }

    public void OnGridDataLoaded(IEnumerable<HaulierStopListModel> items)
    {
        CurrentGridItems = items.ToList();
        UpdateSelectAllState();
        StateHasChanged();
    }

    // New Grid integration methods
    public void OnSelectedItemsChanged(List<HaulierStopListModel> selectedItems)
    {
        SelectedStops = selectedItems;
        // Update the IDs collection for backward compatibility
        SelectedProofOfDeliveryIds = selectedItems
            .Where(x => x.ProofOfDeliveryId != null)
            .Select(x => x.ProofOfDeliveryId.Value)
            .ToHashSet();
        StateHasChanged();
    }

    public EventCallback<List<HaulierStopListModel>> OnSelectedItemsChangedCallback => 
        EventCallback.Factory.Create<List<HaulierStopListModel>>(this, OnSelectedItemsChanged);

    public bool IsStopSelectable(HaulierStopListModel stop)
    {
        return stop.ProofOfDeliveryId != null;
    }

    public async Task ExportSelectedProofOfDeliveries()
    {
        if (!SelectedStops.Any(x => x.ProofOfDeliveryId != null))
        {
            await this.ShowErrorNotification("No items with Proof of Delivery selected");
            return;
        }

        IsExportingPdfs = true;
        StateHasChanged();

        try
        {
            var proofOfDeliveryIds = SelectedStops
                .Where(x => x.ProofOfDeliveryId != null)
                .Select(x => x.ProofOfDeliveryId.Value)
                .ToList();

            var exportModel = new ProofOfDeliveryBulkExportModel
            {
                ProofOfDeliveryIds = proofOfDeliveryIds
            };

            var result = await this.ApiService.PostJsonAsync<BulkExportResult>($"/ProofOfDelivery/BulkExportPdf", exportModel);
            
            if (result != null && result.Success && !string.IsNullOrEmpty(result.FileBase64))
            {
                await JsRuntime.InvokeVoidAsync("downloadFileFromBytes", result.FileName, result.FileBase64, "application/zip");
                await this.ShowSuccessNotification($"Successfully exported {proofOfDeliveryIds.Count} Proof of Delivery documents");
            }
            else
            {
                await this.ShowErrorNotification(result?.ErrorMessage ?? "Failed to export PDFs");
            }
        }
        catch (Exception ex)
        {
            await this.ShowErrorNotification($"Error exporting PDFs: {ex.Message}");
        }
        finally
        {
            IsExportingPdfs = false;
            StateHasChanged();
        }
    }
}
