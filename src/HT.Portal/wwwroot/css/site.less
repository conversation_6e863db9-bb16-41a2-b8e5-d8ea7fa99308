@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.cdnfonts.com/css/uk-number-plate');

@dark-mode: 		~'body.dark-mode &';

@screen-xs-min:     480px;
@screen-sm-min:     481px;
@screen-md-min:     992px;
@screen-lg-min:     1280px;

@screen-xs-max:     (@screen-sm-min - 1);
@screen-sm-max:     (@screen-md-min - 1);
@screen-md-max:     (@screen-lg-min - 1);


@mobile:            ~"only screen and (max-width: @{screen-xs-min})";
@tablet:            ~"only screen and (min-width: @{screen-sm-min})";
@tablet-strict:     ~"only screen and (min-width: @{screen-sm-min}) and (max-width: @{screen-sm-max})";
@desktop:           ~"only screen and (min-width: @{screen-md-min})";
@desktop-strict:    ~"only screen and (min-width: @{screen-md-min}) and (max-width: @{screen-md-max})";
@widescreen:        ~"only screen and (min-width: @{screen-lg-min})";
@mobile-landscape:  ~"only screen and (orientation: landscape) and (max-width: @{screen-sm-max})";
@ipad:  ~"only screen and (min-width: 768px) and (max-width: 1024px)";


html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
	display: block;
}
body {
	line-height: 1;
}
ol, ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
:root {
    --primary:  #272534; //rgb(8, 56, 40); //#E51137;
    --secondary: #FFE500;
    // --highlight: hsl(231,74%,90%);
    // --highlight2: hsl(231,74%,97%);
    --highlight: #f8f8f8;
    --highlight2: #eee;

    // --accent-color: var(--primary);

    --bg: #FAFAFA;
    --bg2: #f1f1f1;
    --yellow: #FFE500;
    --yellow2: #fff066;
    --lightyellow: #fffef0;
    --white: #ffffff;
    --black: #111111;
    --grey: #8d97a4;

    --orange: #e18700;

    --brown: #521a00;
    --brown-secondary: #922e00;

    --grey: #41454b;
    --grey-secondary: #7f8691;

    --blue: #052a63;
    --blue-secondary: #0042ac;

    --disabled: #dddee6;

    --green: #07bf50;

    --red: #db1731;

    --green-fade: fadeout(#07bf50, 90%);

    --red-fade: fadeout(#db1731, 90%);

    --purple: #6f42c1;
    --purple-fade: fadeout(#6f42c1, 90%);

    --primary-fade: hsl(214.74deg 82.61% 31.57% / 17%);

    --secondary-fade: hsl(23.36deg 93.39% 47.45% / 18%);

    --border: #d5d7e9;

    --shadow:0px 4px 20px rgb(205 212 220 / 50%); //, inset 0px 0px 5px rgba(0, 0, 0, 0.05);

    --shadow2: 0px 4px 10px rgba(0, 0, 0, 0.05), inset 0px 0px 5px rgba(0, 0, 0, 0.05);

    --shadowhighlight: 0 2px 1px 0px var(--highlight), inset 0 -1px 1px 0px var(--highlight);

    --br: 14px;

    --p: 20px;

    --width: 1200px;
    
    --transition: 0.2s all ease-out;
}


.mb {
    margin-bottom: var(--p);
}

html .o--Trigger--trigger .Open_svg__w-fill-dark, html .o--Trigger--trigger .Close_svg__w-fill-dark {
    fill: var(--primary);
}
html .o--Trigger--trigger:hover {
    .Open_svg__w-fill-dark, .Close_svg__w-fill-dark {
        fill: var(--secondary);
    }
}

html .o--Chat--chat .o--Chat--header, .o--Widget--widget .o--ChatMessage--chatMessage.o--ChatMessage--in .o--ChatMessage--body .o--ChatMessage--content {
    background: var(--primary);
}

.o--Widget--widget .o--ChatMessage--chatMessage.o--ChatMessage--displayMode-light.o--ChatMessage--out .o--ChatMessage--avatar *, .o--Widget--widget .o--ChatMessage--chatMessage.o--ChatMessage--displayMode-light.o--ChatMessage--out .o--ChatMessage--body .o--ChatMessage--content *, .o--Widget--widget .o--ChatMessage--chatMessage.o--ChatMessage--displayMode-auto.o--ChatMessage--out .o--ChatMessage--avatar *, .o--Widget--widget .o--ChatMessage--chatMessage.o--ChatMessage--displayMode-auto.o--ChatMessage--out .o--ChatMessage--body .o--ChatMessage--content * {
    color: var(--primary);
}

.o--Widget--widget a, .o--Widget--widget a *, .o--Widget--widget a:active {
    color: var(--secondary);
}

.force-right {
    margin-left: auto;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

html {
    font-size: 14px;
}

.dif {
    display: inline-flex;
    &.button {
        display: inline-flex;
    }
}

aside {
    background: var(--yellow);
    border-radius: var(--br);
    padding: var(--p);
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
    display: none;
}

section {
    padding: calc(var(--p) * 1);
    // border: 1px solid var(--border);
    border-radius: calc(var(--br) / 2); 
    margin-bottom: var(--p);
    &.add-haulier-stop {
        display: grid;
        gap: 20px;
        grid-template-columns: 1fr 1fr 1fr;
        section.bs {
            margin-bottom: 0;
        }
        .select2 {
            max-width: 322px;
        }
    }
    &:last-child {
        margin-bottom: 0;
    }
    &.npb {
        padding-bottom: 0;
    }
    &.npt {
        padding-top: 0;
    }
    @media @mobile {
        padding: calc(var(--p) / 2);
        overflow: auto;
    }
    &>.g {
        &:last-child {
            padding-bottom: 0;
            margin-bottom: 0;
        }
    }
    // &.eticket-contract {
    //     display: grid;
    //     grid-template-columns: 1.5fr 1fr;
    // }
    &.eticket-site {
        display: flex;
        gap: 20px;
        justify-content: space-between;
        // min-width: 310px;
        padding: 0px;
        // margin-bottom: 0;
        @media @mobile {
            flex-direction: column;
        }
        &>section.bs {
            flex-grow: 1;
            margin-bottom: 0;
        }
        &>.g {
            // grid-template-columns: 1fr;
            margin-bottom: 0;
            // flex-grow: 1;
            &:last-child {
                background: var(--white);
                padding: var(--p);
                border: 1px solid var(--border);
                border-radius: calc(var(--br) / 2);
                gap: 20px;
                grid-template-columns: 120px .5fr .5fr;
                min-width: 330px;
                min-height: 320px;
                .b {
                    &.entryexittime {
                        grid-column: 2 / 4;
                    }
                }
                .b.check.force-left {
                    justify-content: flex-start;
                }
                .surcharge-dropdown, .product-select {
                    grid-column: 1 / -1;
                }
                // box-shadow: var(--shadow);
                @media @mobile {
                    grid-template-columns: 1fr;
                }
                input[type="number"] {
                    font-size: 18px;
                    font-weight: 600;
                    // width: 160px;
                }
            }
        }
    }
    &.comment-section {
        textarea {
            width: 100%;
            height: 300px;
        }
        &.small {
            textarea {
                height: 100px;
            }
        }
    }
    &.bs {
        border: 1px solid var(--border);
        background: #fff;
        // box-shadow: var(--shadow);
        .subtitle {
            font-size: 14px;
            min-height: auto;
            border-radius: calc(var(--br) / 2);
            padding-bottom: 10px;
            width: auto;
            display: inline-flex;
            color: var(--grey);
            width: 100%;
            display: flex;
            justify-content: space-between;
            // .sub-subtitle {
            //     .b {
            //         display: flex;
            //         flex-direction: row-reverse;
            //         align-items: center;
            //         width: 300px;
            //     }
            // }
            &>div {
                align-items: center;
                display: inline-flex;
                gap: 10px;
                &:last-child {
                    justify-content: flex-end;
                }
            }
            svg {
                height: 15px;
                width: 15px;
                margin-right: 5px;
                path {
                    fill: var(--grey);
                }
            }
            .button {
                svg path {
                    fill: var(--white);
                }
            }
            &.route-subtitle {
                justify-content: flex-start;
                gap: 10px;
                div:last-child {
                    justify-content: flex-start;
                }
                .optimisation-status {
                    margin-left: 0px;
                    color: var(--grey);
                    font-weight: 500;
                    font-size: 12px;
                    @media @mobile {
                        display: none;
                        & + div {
                            display: none;
                        }
                    }
                }
                .button:last-child {
                    margin-left: auto;
                }
            }
            // padding: 0px;
            // background: var(--bg);
        }
    }
    &.np {
        padding: 5px;
        &>section {
            margin-bottom: 0;
        }
        .subtitle {
            // background: var(--primary);
            color: var(--primary);
            font-size: 16px;
            border-bottom: 1px solid var(--border);
            box-sizing: border-box;
            padding: calc(var(--p) / 3);
            border-radius: calc(var(--br) / 2.3) calc(var(--br) / 2.3) 0 0;
            margin-bottom: 0;
            &.editing {
                background: var(--secondary);
                color: var(--white);
                .button {
                    color: var(--white);
                    border-color: var(--white);
                    box-shadow: none;
                }
            }
            &>div:first-child {
                padding: calc(var(--p) / 2);
            }
            .button.small {
                height: 34px;
                padding: calc(var(--p) / 2) calc(var(--p) / 2);
            }
            // .button.secondary {
            //     color: #fff;
            //     border-color: #fff;
            // }
        }
    }
    &.p0 {
        padding: 0;
    }
}

.pa {
    padding: 10px;
}

a.forgot {
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    margin-left: auto;
    text-decoration: underline;
    color: #999;
}

.remove {
    cursor: pointer;
    svg {
        path {
            fill: var(--grey);
        }
    }
    &:hover {
        svg {
            path {
                fill: var(--red);
            }
        }
    }
}

.weighbridge-photos {
    display: grid;
    grid-template-columns: 1fr 1fr;
    margin-left: auto;
    gap: 20px;
    // width: 100%;
    @media @mobile {
        flex-direction: column;
    }
    &>.site-name {
        grid-column: 1 / -1;
    }
    &>div:not(.b) {
        border: 1px solid var(--border);
        border-radius: var(--br);
        display: flex;
        flex-direction: column;
        padding: 5px;
        background: var(--bg);
        &>div {
            padding: 5px 5px 7px 5px;
            font-weight: 600;
        }
        img {
            height: 100%;
            width: 350px;
            max-width: 100%;
            object-fit: cover;
            border-radius: calc(var(--br) / 1.5);
            display: flex;
        }
    }
}

header {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--p);
    height: 50px;
    align-items: center;
    @media @mobile {
        height: auto;
        flex-wrap: wrap;
        gap: 10px;
    }
    .button {
        padding: calc(var(--p) / 1.2);
        &>svg {
            margin-right: 10px;
            height: 13px;
            width: 20px;
            transform: scale(1.5);
            path {
                fill: var(--white);
            }
        }
        &.tertiary {
            &>svg {
                path {
                    fill: var(--primary);
                }
            }
        }
    }
    &>div {
        display: flex;
        align-items: center;
        &:last-child {
            width: max-content;
            gap: 10px;
        }
        @media @mobile {
            flex-wrap: wrap;
        }
        .button {
            // margin-left: calc(var(--p) / 2);
            @media @mobile {
                white-space: nowrap;
            }
        }
    }
    .title {
        align-items: center;
        color: var(--primary);
        &>svg {
            margin-right: calc(var(--p) / 2);
            height: 26px;
            width: 26px;
            path {
                fill: var(--primary)
            }
        }
        &>a {
            // padding: var(--p);
            // border-radius: var(--br);
            // font-size: 16px;
            font-weight: 400;
            // box-shadow: var(--shadow2);
            // border: 1px solid var(--border);
            // text-align: center;
            margin-right: calc(var(--p) * 1.5);
            position: relative;
            &:after {
                content: '>';
                position: absolute;
                right: -20px;
                top: 0px;
                color: var(--border);
            }
            &:hover {
                // background: var(--primary);
                color: var(--purple);
                text-decoration: underline;
                // box-shadow: var(--shadowhighlight);
            }
        }
    }
}

.posrel {
    position: relative;
}

.subtitle {
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: var(--p);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
    & + .grid-flex {
        // border: 1px solid var(--border);
        border-radius: calc(var(--br) / 2);
    }
    .buttons {
        display: flex;
        align-items: center;
        gap: 20px;
    }
    .buttons-container {
        padding: 0;
    }
}

h1:focus {
    outline: none;
}

a {
    color: #0071c1;
}

p {
    line-height: 120%;
    margin-bottom: var(--p);
}

.button { //button
    background: var(--primary);
    color: var(--white);
    padding: calc(var(--p) / 1.5) var(--p);
    border-radius: calc(var(--br) / 2);
    border: none;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 0 0 rgba(0,0,0,.05), 0 1px 0 0 rgba(0,0,0,.1); //, inset 0 2px 0px 0 rgba(255,255,255,.2);
    border: 1px solid rgba(0,0,0,0.1);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    @media @mobile {
        font-size: .9rem;
    }
    
    &.inline-button {
        display: inline-flex;
    }
    &.drop-button {
        position: relative;
        padding-right: 30px;
        &:after {
            content: '';
            position: absolute;
            right: 15px;
            top: 19px;
            height: 6px;
            width: 6px;
            border-bottom: 1px solid var(--primary);
            border-right: 1px solid var(--primary);
            transform: rotate(45deg);
        } 
        &.small {
            padding-right: 30px;
            &:after {
                top: 13px;
            }
            &:hover {
                .drop-button-dropdown {
                    top: 36px;
                }
            }
        }
        .drop-button-dropdown {
            display: none;
        }
        &:hover {
            cursor: pointer;
            .drop-button-dropdown {
                display: block;
                top: 45px;
                right: 0px;
                position: absolute;
                width: 200px;
                border: 1px solid var(--border);
                border-radius: calc(var(--br) / 2);
                background: #fff;;
                ul {
                    li {
                        padding: 10px;
                        border-bottom: 1px solid var(--border);
                        &:last-child {
                            border-bottom: none;
                        }
                        &:hover {
                            background: var(--bg);
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }
    // &.aw {
    //     width: 200px;
    //     flex-grow: 0;
    // }
    &.drop {
        position: relative;
        ul {
            top: 35px;
            left: 0px;
            display: none;
            position: absolute;
            width: 200px;
            background: var(--white);
            border-radius: var(--br);
            box-shadow: var(--shadow2);
            border: 1px solid var(--border);
            li {
                border-bottom: 1px solid var(--border);
                &:last-child {
                    border-bottom: none;
                }
                &>div {
                    padding: calc(var(--p) / 2);
                }
            }
        }
        &:hover {
            ul {
                display: block;
                li {
                    div {
                        color: var(--primary);
                        &:hover {
                            color: var(--secondary);
                        }
                    }
                }
            }
        }
    }
    &:hover {
        box-shadow: inset 100px 100px 0 0 rgba(0,0,0,0.1);
    }
    &.secondary-solid {
        background: #0f5ac1;
        &:hover {
            background: #0f5ac1;
            box-shadow: inset 100px 100px 0 0 rgba(0,0,0,0.1);
        }
    }
    &.secondary {
        color: var(--primary);
        border: 2px solid var(--primary);
        background: transparent;
        &:hover {
            // border-color: var(--secondary);
            // color: var(--secondary);
            box-shadow: inset 0 0 2px 0 var(--primary);
        }
    }
    &.tertiary {
        color: var(--primary);
        background: transparent;
        &:hover {
            background: var(--highlight);
            // border-color: var(--secondary);
            // text-decoration: underline;
            box-shadow: 0 2px 0 0 rgba(0,0,0,.05), 0 1px 0 0 rgba(0,0,0,.4);
        }
    }
    &.inline {
        color: var(--primary);
        background: transparent;
        box-shadow: none;
        &:hover {
            background: var(--highlight);
        }
    }
    &.small {
        padding: calc(var(--p) / 2) calc(var(--p) / 1.5);
        display: inline-flex;
        font-size: 14px;
        height: 40px;
    }
    &.tiny {
        padding: calc(var(--p) / 2) calc(var(--p) / 2);
        display: inline-flex;
        font-size: 12px;
        height: 23px;
    }
    &>svg {
        margin-right: 10px;
        path {
            fill: var(--white);
        }
    }
    &.secondary {
        &>svg {
        // margin-right: 0px;
            path {
                fill: var(--primary);
            }
        }
    }
    &.warning {
        background: var(--orange);
        color: white;
    }
    &.generate-button {
        // width: 268px;
        // font-size: 14px;
        // padding: 10px 15px;
    }
    &.delete {
        background: var(--red-fade);
        color: var(--red);
        font-size: 12px;
        padding: 10px;
        height: 30px;
    }
    &.delete-button {
        background: var(--red);
    }
    &.secondary-button {
        background: var(--secondary);
        color: white;
        &:hover {
            background: orange;
        }
    }
    &>svg {
        width: 15px;
        height: 15px;
    }
    &.save {
        background: var(--green);
    }
}

.content {
    padding-top: 1.1rem;
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid red;
}

.validation-message {
    color: red;
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

.blazor-error-boundary::after {
    content: "An error has occurred."
}

body {
    font-family: 'Poppins', Helvetica, Arial, sans-serif;
    @media @mobile {
        // padding-top: 80px;
    }
    // &:before {
    //     content: '';
    //     background: var(--primary);
    //     height: 120px;
    //     width: 100vw;
    //     position: absolute;
    //     top: 0px;
    //     left: 0px;
    //     z-index: -1;
    // }
    &.login-body {
        display: flex;
        align-items: center;
        justify-content: center;
        // height: 100vh;
        width: 100vw;
        background: var(--bg);
    }
}

a {
    text-decoration: none;
    color: var(--black);
    &.active {
        color: var(--primary);
    }
}

.page {
    display: flex;
    height: 100vh;
    // padding: var(--p);
    // gap: var(--p);
    box-sizing: border-box;
    @media @mobile {
        flex-direction: column;
        padding: 0px;
        height: auto;
    }
}

.sidebar {
    background: var(--bg);
    padding: calc(var(--p) * 2) var(--p) var(--p);
    // box-shadow: var(--shadow);
    width: 230px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
    flex-shrink: 0;
    flex-grow: 0;
    height: 100vh;
    box-sizing: border-box;
    overflow: auto;
    @media @mobile {
        height: 80px;
        box-sizing: border-box;
        padding: var(--p);
        position: sticky;
        top: 0px;
        left: 0px;
        border-radius: 0px;
        width: 100%;
        justify-content: space-between;
        display: flex;
        flex-direction: row;
        z-index: 99;
        overflow: visible;
    }
    .logo {
        width: 170px;
        @media @mobile {
            height: 70px;
            object-fit: contain;
            object-position: left;
        }
    }
    nav {
        display: flex;
        flex-direction: column;
        width: 100%;
        flex-grow: 1;
        &.desktop-menu {
            display: block;
            @media @mobile {
                display: none;
            }
        }
        ul {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            height: 100%;
            // @media @mobile {
            //     position: relative;
            //     z-index: 9999;
            // }
            li {
                margin-bottom: 1px;
                &.nav-header {
                    font-size: 10px;
                    color: var(--grey);
                    margin-top: 10px;
                    // border-top: 1px solid var(--border);
                    // background: #eee;
                    border-radius: var(--br);
                    padding: 5px 10px;
                    margin-bottom: 4px;
                }
                a {
                    padding: 7px 12px;
                    display: flex;
                    font-weight: 500;
                    font-size: .9rem;
                    align-items: center;
                    border-radius: calc(var(--br) / 2);
                    transition: var(--transition);
                    svg {
                        height: 20px;
                        width: 20px;
                        margin-right: 10px;
                        transform: translateY(0px);
                        path {
                            fill: var(--grey);
                        }
                    }
                    &.active {
                        background: var(--highlight2);
                        font-weight: 600;
                        &:hover {
                            background: var(--highlight2);
                        }
                        svg path {
                            fill: var(--primary);
                        }
                    }
                    &:hover {
                        background: var(--highlight2);
                        svg path {
                            fill: var(--primary);
                        }
                    }
                }
            }
        }
        &.mobile-menu-container {
            position: absolute;
            top: 65px;
            right: 0px;
            width: 200px;
            height: auto;
            background: var(--white);
            box-shadow: var(--shadow);
            z-index: 999;
            ul {
                li {
                    a {
                        padding: calc(var(--p) / 2) var(--p);
                    }
                }
            }
        }
    }
}

.mobile-menu {
    height: 50px;
    width: 30px;
    z-index: 999;
    display: none;
    @media @mobile {
        display: block;
    }
    &:hover+ul {
        display: block;
        position: absolute;
        right: 0px;
        top: 60px;
        background: var(--white);
        box-shadow: var(--shadow);
        &>li>a {
            padding: var(--p) calc(var(--p) * 1.5);
        }
    }
    &>div {
        display: block;
        width: 30px;
        height: 4px;
        background: var(--primary);
        position: relative;
        transform: translateY(23px);
        &:before, &:after {
            content: '';
            height: 4px;
            top: 10px;
            position: absolute;
            width: 100%;
            display: block;
            background: var(--primary);
        }
        &:after {
            top: -10px;
        }
    }
}



main {
    background: var(--bg);
    padding: calc(var(--p) * 1) calc(var(--p) * 1);
    flex-grow: 1;
    overflow: auto;
    @media @mobile {
        padding: var(--p);
        border-radius: 0px;
        z-index: 0;
        box-sizing: border-box;
    }
    &>header+section.list-section {
        min-height: calc(100vh - 150px);
        padding: 0;
        display: flex;
        flex-direction: column;
        &.plus-dash {
            flex-direction: column;
            gap: 20px;
        }
        .grid-header {
            // border-bottom: 1px solid var(--border);
            border-radius: calc(var(--br) / 2) calc(var(--br) / 2) 0 0;
        }
        &:last-child {
            margin-bottom: 0;
        }
        table.table {
            margin: 20px;
            width: calc(100% - 40px);
            @media @mobile {
                // width: max-content;
                margin: 0;
                width: 100%;
            }
            &+.no-results {
                margin: 20px;
                width: calc(100% - 40px);
            }
        }
    }
}

.title {
    font-weight: 600;
    font-size: 1.4rem;
    @media @mobile {
        font-size: 1.1rem;
    }
}

@keyframes fadeFlash {
    0% { background: var(--white); box-shadow: 0 0 0px 10px var(--white), 0 0 0 8px var(--white);}
    50% { background: var(--fade-red); box-shadow: 0 0 0 10px var(--white), 0 0 0px 12px var(--red);}
    100% { background: var(--white); box-shadow: 0 0 0 8px var(--white), 0 0 0px 10px var(--white);}
}

.stat {
    display: flex;
    flex-direction: column;
    gap: 5px;
    &.stat-button > div:first-child {
        font-size: inherit;
    }
    &>div {
        &:first-child {
            font-size: 1.6rem;
            color: var(--primary);
        }
    }
    &.red {
        // background: var(--red-fade);
        border-radius: calc(var(--br) / 2);
        // box-shadow: 0 0 0px 10px var(--red-fade);
        animation: fadeFlash 2s infinite;
        div:first-child {
            color: var(--red);
        }
    } 
    &.important {
        div:first-child {
            font-weight: 600;
        }
    }
}

.ancillary-info {
    // border-top: 1px solid var(--border);
   font-weight: 600;
//    color: var(--primary);
    color: var(--secondary);
//    padding-top: 10p/x;
//    margin-top: 5px;
   text-align: right;
   font-size: 20px;
   margin-left: auto;
}

.dashboard {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--p);
    margin-bottom: 10px;
    @media @mobile {
        grid-template-columns: 1fr;
        margin-bottom: 0;
    }
    &.single {
        grid-template-columns: 1fr;
    }
    &>.widget {
        border: 1px solid var(--border);
        border-radius: var(--br);
        // padding: var(--p) var(--p) calc(var(--p) * 2 ) var(--p);
        // gap: var(--p);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .divider {
            width: 1px;
            background: var(--border);
            &:last-child {
                display: none;
            }
        }
        &.onstop-summary {
            grid-column: 1 / -1;
        }
        &.triple-widget {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-column: 1 / -1;
            &>div {
                border-right: 1px solid var(--border);
                &:last-child {
                    border-right: none;
                }
            }
        }
        &.quadruple-widget {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            grid-column: 1 / -1;
            &>div {
                border-right: 1px solid var(--border);
                &:last-child {
                    border-right: none;
                }
            }
        }
        &.summary {
            display: grid;
            grid-template-columns: .75fr 1px .5fr 1px 1fr;
            grid-column: 1 / -1;
            // padding: var(--p);
            background: var(--bg);
            flex-direction: row;
            &.all-sites {
                grid-template-columns: .75fr 1px .5fr 1px;
                @media @mobile {
                    grid-template-columns: 1fr;
                } 
            }
            @media @mobile {
                grid-template-columns: 1fr;
            }
            &.single {
                grid-template-columns: 1fr;
            }
            &.triple {
                grid-template-columns: 1fr 1px 1fr 1px 1fr;
            }
            &.double {
                grid-template-columns: 1fr 1px 1fr;
                @media @mobile {
                    grid-template-columns: 1fr;
                }
            }
            // }
            // .notvisited-summary {
            //     border-left: 1px solid var(--border);
            //     border-right: 1px solid var(--border);
            // }
            // .customer-summary {
            //     border-left: 1px solid var(--border);
            //     @media @mobile {
            //         border-left: 0px;
            //     }
            // }
            // .contracts-summary {
            //     border-right: 1px solid var(--border);
            //     @media @mobile {
            //         border-rigt: 0px;
            //     }
            // }
            // .subtitle {
            //     border-radius: calc(var(--p) / 1);
            // }
            // .summary-rows {
            //     &>div {
            //         padding: calc(var(--p) / 2) calc(var(--p) / 1);
            //         height: 30px;
            //         border-top: 1px solid var(--border);
            //         display: flex;
            //         align-items: center;
            //         gap: 10px;
            //     }
            // }
            .summary-rows.onstop-rows {
                padding: var(--p);
                gap: 5px;
                display: flex;
                flex-wrap: wrap;
                .onstop {
                    margin-left: 0;
                    padding: calc(var(--p) / 2) var(--p);
                    margin-bottom: 10px;
                }
                &>div {
                    display: inline-flex;
                }
            }
            &>div.pie-summary {
                padding: var(--p);
                display: flex;
                align-items: center;
            }
            .total-summary {
                // border-right: 1px solid var(--border);
                // border-left: 1px solid var(--border);
                display: grid;
                grid-template-columns: 1fr 1fr;
                flex-direction: row;
                flex-wrap: wrap;
                background: var(--bg2);
                // justify-content: space-evenly;
                .stat-row {
                    border-bottom: none;
                    &:last-child {
                        border-bottom: none;
                    }
                }
                @media @mobile {
                    border-right: none;
                    width: 100%;
                }
            }
            .stats {
                grid-template-columns: 1fr;
                padding: 0px;
                .stat {
                    &>div {
                        &:first-child {
                            font-size: 2rem;
                        }
                    }
                }
            }
        }
        .title {
            background: var(--highlight);
            padding: var(--p);
            border-radius: calc(var(--br) / 1) calc(var(--br) / 1) 0 0;
            border-bottom: 1px solid var(--border);
        }
        .subtitle {
            margin-bottom: 0;
            padding: 0 var(--p);
            background: var(--bg);
            font-size: 1rem;
            border-bottom: 1px solid var(--border);
            @media @mobile {
                border-top: 1px solid var(--border);
            }
            &:first-child {
                border-radius: var(--br) var(--br) 0 0;
                @media @mobile {
                    border-radius: 0px;
                }
            }
        }
        &.full {
            grid-column: 1 / -1;
            background: var(--bg);
        }
    }
}

.onstop-rows {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    padding: 20px;
    .onstop {
        padding: 10px;
        border-radius: var(--br);
        background: var(--red-fade);
        color: var(--red);
        font-weight: 600;
        margin-bottom: 10px;
    }
}


.dash-rows {
    .dash-row {
        display: flex;
        gap: 10px;
        align-items: center;
        border-bottom: 1px solid var(--border);
        padding: calc(var(--p) / 2) var(--p);
        &.hoverable {
            cursor: pointer;
            &:hover {
                background: #f3f3f3;
            }
        }
        &.dash-row-internal-transfer-header {
            display: grid;
            background: hsla(160deg, 75%, 12.5%, 20%);
            grid-template-columns: 1.5fr 1fr 1fr .5fr 1fr;
            font-weight: 600;
        }
        &.dash-row-internal-transfer {
            display: grid;
            background: hsla(160deg, 75%, 12.5%, 10%);
            grid-template-columns: 1.5fr 1fr 1fr .5fr 1fr;
        }
        @media @mobile {
            flex-wrap: wrap;
            .count {
                order: 2;
            }
            .total {
                flex-grow: 1;
                order: 3;
            }
            .tons {
                flex-grow: 1;
                order: 2;
            }
            .wagon-tons {
                order: 3;
            }
            .chart-container {
                order: 3;
                justify-content: flex-start;
            }
            .wagons {
                order: 3;
            }
            .name {
                order: 1;
                width: 100%;
            }
        }
        &.visitor-list {
            display: grid;
            grid-template-columns: 130px 100px 1fr;
        }
        .total {
            font-weight: 600;
            width: 75px;
            text-align: right;
            flex-shrink: 0;
        }
        .name {
            flex-grow: 1;
            font-size: 13px;
            font-weight: 600;
            .onstop {
                margin-top: 5px;
                margin-left: 0;
                font-weight: 600;
                font-size: 10px;
            }
        }
        .status-pill {
            width: 65px;
            justify-content: center;
            font-weight: 600;
        }
        .ewc-code {
            font-weight: 600;
            color: #fff;
            width: 42px;
            justify-content: center;
            display: flex;
            border-radius: var(--br);
            background: var(--secondary);
            padding: calc(var(--p) / 4) calc(var(--p) / 2.5);
            font-size: 10px;
            flex-shrink: 0;
            flex-grow: 0;
        }
        .tons {
            font-style: italic;
            width: 103px;
            font-size: 12px;
            flex-shrink: 0;
            text-align: right;
        }
        .last-payment {
            width: 200px;
            font-size: 12px;
            flex-shrink: 0;
            text-align: right;
            font-style: italic;
        }
        .area {
            width: 200px;
            font-size: 12px;
            flex-shrink: 0;
            text-align: right;
        }
        .wagons {
            // font-style: italic;
            // width: 103px;
            font-size: 10px;
            // color: var(--primary);
            flex-shrink: 0;
            text-align: right;
        }
        .wagon-tons {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        .count {
            width: 70px;
            text-align: center;
            justify-content: center;
            border-radius: calc(var(--br) / 3);
            width: 38px;
            justify-content: center;
            background: var(--primary);
            display: inline-flex;
            padding: 5px;
            flex-shrink: 0;
            color: var(--white);
            font-size: 14px;
            // text-align: center;
            font-weight: 600;
            // &._2 {width: calc(var(--p) * 2)}
            // &._3 {width: calc(var(--p) * 3)}
            // &._4 {width: calc(var(--p) * 4)}
            // &._5 {width: calc(var(--p) * 5)}
            // &._6 {width: calc(var(--p) * 6)}
            // &._7 {width: calc(var(--p) * 7)}
            // &._8 {width: calc(var(--p) * 8)}
        }
        &:last-child {
            border-bottom: none;
        }
    }
    .subtitle {
        grid-column: 1 / -1;
    }
}




.dashboard {
    .stat-row {
        display: flex;
        gap: 10px;
        padding: var(--p) 26px;
        // justify-content: center;
        align-items: center;
        // border-bottom: 1px solid var(--border);
        @media @mobile {
            padding: 15px;
            display: grid;
            grid-template-columns: 50px 1fr;
            gap: 10px;
        }
        &.stat-list-header {
            border-bottom: 1px solid var(--border);
            padding-right: 20px;
        }
        &.small {
            .stat-icon {
                svg {
                    height: 20px;
                }
                
            }
            .stats .stat {
                &>div:first-child {
                    font-size: 1.4rem;
                }
            }
        }
        &:last-child {
            border-bottom: none;
        }
        .stat-icon {
            svg {
                width: 50px;
                height: 30px;
                path {
                    fill: var(--primary);
                }
            }
        }
        .stats {
            padding: 0 var(--p);
            display: grid;
            // grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            &.double {
                grid-template-columns: 1fr 1fr;
                @media @mobile {
                    grid-template-columns: 1fr;
                }
            }
            &.single {
                grid-template-columns: 1fr;
            }
            &.important {
                .stat>div:first-child {
                    font-weight: 600;
                }
                .stat>div:last-child {
                    font-size: 12px;
                    line-height: 120%;
                }
            }
        }
    }
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}


html body table.quickgrid[theme="default"] {
    tbody {
        tr {
            td {
                padding: 10px;
            }
        }
    }
}

.flex {
    display: flex;
    gap: 20px;
    width: 100%;
    &.aic {
        align-items: center;
    }
    &.delivery-flex {
        margin-top: var(--p);
        gap: 40px;
        &>div {
            flex-grow: 1;
        }
    }
    &.totals-flex {
        &>div {
            flex-grow: 1;
        }
        textarea {
            height: 120px;
        }
    }
}


.panel {
    width: 50%;
    background: var(--bg);
    border-radius: var(--br);
    border: 1px solid var(--border);
    height: 130px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    flex-grow: 1;
    padding: 0 calc(var(--p) * 2);
    .b {
        width: 100%;
    }
}

.g-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    .gg-3-1 {
        grid-template-columns: min-content 1fr;
    }
}

.new-job-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 40px;
    border: 1px solid var(--border);
    border-radius: var(--br);
    align-items: center;
    justify-content: center;
    .button {
        width: 300px;
        padding: 30px 0;
    }
    &>* {
        width: 300px;
    }
}

.g {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 30px;
    row-gap: 20px;
    max-width: 800px;
    height: fit-content;
    box-sizing: border-box;
    // border-bottom: 1px solid var(--border);
    margin-bottom: var(--p);
    padding-bottom: var(--p);
    &.g3 {
        grid-template-columns: 1fr 1fr 1fr;
    }
    &.full-width {
        max-width: 100%;
    }
    &.g-address {
        grid-template-columns: 1fr;
        width: 375px;
        row-gap: 5px;
        .b {
            &:nth-child(2), &:nth-child(3), &:nth-child(4) {
                label {
                    display: none;
                }
            }
            &:last-child {
                margin-top: 10px;
            }
        }
    }
    &.g-order-top {
        grid-template-columns: 80px 120px 1fr  130px 120px;
        max-width: 100%;
        column-gap: 30px;
        .b {
            .button {
                margin-top: 22px;
            }
        }
    }
    &.g-route-top {
        grid-template-columns: 120px 120px 1fr 1fr 200px 110px;
        max-width: 100%;
        column-gap: 30px;
    }
    &.g-haulierroute-top {
        grid-template-columns: 120px 1fr 1.5fr 200px;
        max-width: 100%;
        column-gap: 30px;
        @media @mobile {
            grid-template-columns: 1fr;
        }
    }
    &.nm {
        margin: 0px;
    }
    &.np {
        padding: 0px;
    }
    &.single {
        // display: grid;
        grid-template-columns: 1fr;
    }
    .double {
        display: grid;
        gap: 50px;
        grid-template-columns: 1fr 1fr;
    }
    &.g-invoice {
        grid-template-columns: 1fr 1fr 1fr;
        .b {
            label+div {
                font-size: 20px;
                font-weight: 600;
            }
        }
    }
    @media @mobile {
        grid-template-columns: 1fr;
    }

    &>div:not(.b):not(.suggestions):not(.product-switch):not(.b-product-container):not(.search-address):not(.daterangepicker-visibility-hidden) {
        display: flex;
        flex-direction: column;
        gap: 20px;
        &.dual-inputs {
            flex-direction: row;
        }
    }
}

.g-container {
    
}

.b-product-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 30px;
    grid-column: 1 / 4;
    flex-direction: row;
    // &>*:first-child {
    //     flex-grow: 1;
    // }
    // &>*:last-child {
    //     width: 60px;
    // }
    .button {
        margin-top: 24px;
        white-space: nowrap;
    }
}

.b { //b
    display: grid;
    grid-template-columns: 1fr;
    align-items: flex-start;
    gap: 10px;
    &[disabled] {
        opacity: .5;
    }
    label {
        font-weight: 500;
        font-size: .9rem;
    }
    &.check {
        padding-top: 20px;
        display: flex;
        align-items: center;
        flex-direction: row-reverse;
        justify-content: flex-end;
        &.pn {
            padding: 0px;
        }
    }
    &.search {
        margin-bottom: var(--p);
    }
    input, select, textarea {
        font-family: 'Poppins', sans-serif;
        &[readonly] {
            background: var(--bg);
        }
    }
    &.multiselect {
        margin-bottom: var(--p);
        ul {
            li {
                display: flex;
                align-items: center;
                gap: 10px;
                label {
                    padding-top: 3px;
                }
            }
        }
    }
    &.reg-plate {
        input {
            background: var(--yellow);
            text-align: center;
            font-family: 'UKNumberPlate', sans-serif;
            text-transform: uppercase;
            font-size: 34px;
            border: none;
            border-radius: 10px;
        }
    }
    .display {
        font-weight: 600;
        font-size: 0.9rem;
        border: 1px solid var(--border);
        background: var(--bg);
        border-radius: calc(var(--br) / 4);
        padding: calc(var(--p) / 1.6);
        height: 47px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    &.b-delivery-notes {
        .display, textarea {
            height: 97px;
            width: 100%;
            // min-width: 300px;
            align-items: flex-start;
            background: var(--lightyellow);
            margin-bottom: 0px;
            &[readonly] {
                border: none;
                resize: none;
                border: 1px dashed var(--border);
                &:focus-visible {
                    outline: none;
                    box-shadow: none;
                }
            }
        }
    }
    &.stretch {
        grid-column: 1 / -1;
    }
}



.small-reg-plate {
    background: var(--yellow);
    text-align: center;
    font-family: 'UKNumberPlate', sans-serif;
    text-transform: uppercase;
    padding: 5px 5px;
    border-radius: calc(var(--br) / 2);
    // font-size: 34px;
    &.is-zero {
        background: var(--red);
        color: white;
    }
}

input, select, textarea {
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
    border: 1px solid var(--border);
    border-radius: calc(var(--br) / 4);
    padding: calc(var(--p) / 1.6);
    font-size: .9rem;
    box-sizing: border-box;
    width: 100%;
    font-family: 'Poppins', sans-serif;
    &:focus-visible {
        outline: 1px solid var(--primary);
        box-shadow: 0 1px 2px rgb(0 0 0 / 5%), 0 0 3px 0 var(--primary);
    }
}

select.select2[aria-hidden="true"] {
    display: none;
}

input[type="checkbox"] {
    height: 20px;
    width: 20px;
    border-color: var(--border);
}

.select2-container--default .select2-selection--single {
    border-color: var(--border);
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
    border-radius: calc(var(--br) / 4);
    height: 46px;
}
.select2-selection__rendered {
    height: 44px;
    padding-top: calc(var(--p) / 2) ;
    padding-left: calc(var(--p) / 1.4);
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 44px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary);
}

.select2-container--default.select2-container--disabled .select2-selection--single {
    background: var(--bg);
}

.rz-multiselect, .rz-dropdown {
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
    border: 1px solid var(--border);
}

.rz-paginator-element {
    box-sizing: border-box;
}

.rz-paginator {
    border-top: 1px solid var(--border);
}

.dialog-overlay {
    background: rgba(0,0,0,0.5);
    position: absolute;
    top: 0px;
    left: 0px;
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    .dialog {
        background: var(--white);
        width: 600px;
        min-height: 200px;
        max-height: 100vh;
        border-radius: var(--br);
        box-shadow: var(--shadow);
        display: flex;
        overflow: auto;
        flex-direction: column;
        .will-rings-buttons {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            .button {
                margin-left: 10px;
            }
        }
        header {
            padding: var(--p);
            font-weight: 600;
        }
        main {
            box-shadow: none;
            flex-grow: 1;
            padding: var(--p);
        }
        footer {
            display: flex;
            justify-content: flex-end;
            // padding: var(--p);
        }
        .g {
            grid-template-columns: 1fr;
            &.double {
                grid-template-columns: 1fr 1fr;
            }
            &.vehicle-override-row {
                grid-template-columns: 1fr 1fr 70px;
                align-items: flex-end;
                gap: 10px;
                .button {
                    transform: translateY(-3px);
                    font-size: 12px;
                }
            }
        }
    }
}

.rz-grid-table td {
    vertical-align: middle;
}

.login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 100px;
    @media @mobile {
        width: 100%;
        padding: var(--p);
        box-sizing: border-box;
    }
    img {
        width: 215px;
        margin: 0 auto 20px auto;
    }
    form {
        display: flex;
        flex-direction: column;
        // box-shadow: var(--shadow);
        padding: calc(var(--p) * 3);
        border-radius: var(--br);
        // background: var(--primary);
        // border: 1px solid var(--border);
        gap: 30px;
        width: 480px;
        box-sizing: border-box;
        @media @mobile {
            max-width: 100%;
            width: 100%;
            padding: calc(var(--p) * 2);
        }
        h1 {
            font-size: 20px;
            font-weight: 600;
            text-align: center;
            // color: var(--white);
        }
        .b label {
            // color: var(--white);
            display: flex;
            justify-content: space-between;
        }
        input[type="submit"] {
            .button;
            margin-left: 0;
            background: var(--secondary);
            color: var(--primary);
            &:hover {
                // background: var(--secondary)
            }
        }
        .validation {
            background: var(--red);
            color: var(--white);
            padding: 20px;
            text-align: center;
            border-radius: calc(var(--br) / 2);
        }
    }
}

.dialog-overlay {
    .dialog {
        background: var(--bg);
        header {
            padding: var(--p) calc(var(--p) * 2);
            margin-bottom: 0;
            border-bottom: 1px solid var(--border);
            .close {
                transform: scale(1.5);
                cursor: pointer;
                &:hover {
                    color: var(--secondary);
                }
            }
            
        }
        &>div {
            padding: calc(var(--p) * 2);
            flex-grow: 1;
            @media @mobile {
                padding: var(--p);
            }
            section {
                background: var(--white);
            }
        }
        footer {
            // padding: var(--p) calc(var(--p) * 2) ;
        }
        .card.form-card {
            display: flex;
            flex-direction: column-reverse;
        }
        button, .button {
            // display: inline-flex;
            margin-left: auto;
        }
        .add-line-row {
            justify-content: space-between;
            padding: 10px;
            border-radius: 0px;
            button, .button {
                margin-left: 0px;
                &.small.dif {
                    order: 2;
                }
            }
        }
        // form {
        //     button, .button {
        //         margin-left: auto;
        //     }
        // }
    }
}

.buttons-container {
    display: flex;
    justify-content: flex-end;
    padding: var(--p);
}




.link-cards {
    display: grid;
    // grid-template-columns: 200px 200px 200px;
    grid-template-columns: 1fr;
    gap: 20px;
    border: 1px solid var(--border);
    border-radius: var(--br);
    @media @mobile {
        grid-template-columns: 1fr;
    }
    // margin-bottom: 20px;
    .link-card {
        padding: var(--p);
        // border-radius: var(--br);
        font-size: 16px;
        font-weight: 600;
        // box-shadow: var(--shadow2);
        // border: 1px solid var(--border);
        // text-align: center;
        border-bottom: 1px solid var(--border);
        &:hover {
            background: var(--primary);
            color: var(--white);
            box-shadow: var(--shadowhighlight);
        }
    }
}

.container  {
    .tabs .container .tab.selected {
        transform: translateY(0px);
    }
}
.tabs {
    &>.container {
        display: flex;
        gap: 20px;
        
        @media @mobile {
            display: grid;
            gap: 10px;
            margin-bottom: 10px;
            grid-template-columns: 1fr 1fr;
            .tab {
                background: var(--bg);
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                white-space: nowrap;
                border-radius: var(--br);
                box-shadow: 0 2px 0 0 rgb(0 0 0 / 5%), inset 0 2px 5px 0 rgb(255 255 255 / 20%);
                border: 1px solid rgba(0, 0, 0, 0.1);
                &.selected {
                    // border: none;
                    background: var(--secondary);
                    color:var(--white);
                    border-color: var(--primary);
                    &:hover {
                        background: var(--secondary);
                    color:var(--white);
                    }
                }
            }
        }
    }
    .tab {
        // border-radius: calc(var(--br) / 1.5) calc(var(--br) / 1.5) 0 0;
        border-left: 1px solid transparent;
        border-right: 1px solid transparent;
        border-top: 1px solid transparent;
        padding: calc(var(--p) / 1) calc(var(--p) * 0);
        transition: var(--transition);
        &:hover {
            // background: var(--highlight2);
            cursor: pointer;
            color: var(--primary);
        }
        // &.selected {
        //     font-weight: 600;
        //     color: var(--primary);
        //     border-left: 1px solid var(--border);
        //     border-right: 1px solid var(--border);
        //     border-top: 1px solid var(--border);
        //     box-shadow: 0 1px 0 0 var(--white);
        //     &:hover {
        //         background: transparent;
        //         cursor: default;
        //     }
        // }
        &.selected {
            font-weight: 600;
            color: var(--primary);
            // border-left: 1px solid var(--border);
            // border-right: 1px solid var(--border);
            // border-top: 1px solid var(--border);
            box-shadow: inset 0 -3px 0 0 var(--primary);
            &:hover {
                background: transparent;
                cursor: default;
            }
        }
    }
    & + .container {
        border-radius: 0 var(--br);
        border: 1px solid var(--border);
        padding: var(--p);
        min-height: calc(100vh - 290px);
        display: flex;
        flex-direction: column;
        @media @mobile {
            border-radius: var(--br);
            padding: 0px;
            border: none;
        }
        &>.title {
            padding: 20px 0;
            @media @mobile {
                padding: var(--p);
            }
        }
        &>.subtitle {
            padding-left: calc(var(--p) / 1.5);
            // background: var(--highlight2);
            // border-radius: calc(var(--br) / 2);
            // padding: calc(var(--p) / 1.5);
        }
        &>section {
            border: none;
            height: 100%;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: calc(var(--p) / 2);
        }
    }
    @media @mobile {
        overflow: auto;
        .tab {
            padding: var(--p);
        }
    }
}

.vertical-tabs {
    display: flex;
    gap: 10px;
    @media @mobile {
        flex-direction: column;
    }
    .tabs {
        @media @mobile {
            width: max-content;
        }
        .container {
            display: flex;
            gap: 5px;
            flex-direction: column;
            @media @mobile {
                flex-direction: row;
            }
            .tab {
                width: 175px;
                border-radius: 0px;
                border: none;
                // border-radius: calc(var(--br) / 2);
                border: 1px solid transparent;
                // padding-left: var(--p);
                font-weight: 500;
                padding: 15px var(--p);
                // border-bottom: 1px solid var(--border);
                @media @mobile {
                    width: auto;
                }
                &.selected {
                    background: var(--bg);
                    font-weight: 700;
                    // border: 1px solid var(--border);
                    color: var(--secondary);
                    box-shadow: inset -3px  0 0 0 var(--primary);
                }
            }
        }
    }
    &>.container {
        flex-grow: 1;
        border: none;
        border-left: 1px solid var(--border);
        border-radius: 0px;
        padding-top: 4px;
        @media @mobile {
            border-left: none;
        }
    }
}

.lds-ring {
    display: block;
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto;
  }
  .lds-ring div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border: 8px solid var(--primary);
    border-radius: 50%;
    animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: var(--primary) transparent transparent transparent;
  }
  .lds-ring div:nth-child(1) {
    animation-delay: -0.45s;
  }
  .lds-ring div:nth-child(2) {
    animation-delay: -0.3s;
  }
  .lds-ring div:nth-child(3) {
    animation-delay: -0.15s;
  }
  @keyframes lds-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  .loading-message {
    display: block;
    text-align: center;
    margin-top: var(--p);
  }

  .zero-warning {
    color: var(--red);
    // text-align: center;
    font-size: 10px;
    font-weight: 600;
    margin-top: 5px;
  }

table.table { //table
    width: 100%;
    &.table-smaller {
        font-size: 0.9rem;
    }
    .drop {
        z-index: 1;
    }
    thead {
        tr {
            border-bottom: 2px solid var(--border);
            th {
                padding: calc(var(--p) / 2);
                text-align: left;       
                font-weight: 600;
                &.text-right {
                    text-align: right;
                }
            }
        }
    }
    tbody {
        tr {
            border-bottom: 1px solid var(--border);
            td {
                padding: calc(var(--p) / 1.5) calc(var(--p) / 2);
                height: 23px;
                vertical-align: middle;
                .status-pill {
                    margin-right: 2px;
                }
                
                &.Void {
                    background: #eee;
                    text-decoration: line-through;
                    & ~ td {
                        background: #eee;
                        text-decoration: line-through;
                        .small-reg-plate {
                            background: #ccc;
                            text-decoration: none;
                            color: black;
                        }
                        .zero-warning {
                            display: none;
                        }
                        .status-pill {
                            text-decoration: none;
                            color: black;
                        }
                    }
                }
            }
            &:last-child {
                border-bottom: none;
            }
            &:hover {
                background: var(--bg);
                cursor: pointer;
                color: var(--primary);
                box-shadow: inset 0px 3px 0 0 var(--white), inset 0px -2px 0 0 var(--white);
            }
            &.nohov:hover {
                background: transparent;
                cursor: default;
                color: #000;
            }
            &.highlight {
                background: lightyellow;
            }
        }
    }
    tfoot {
        tr {
            border-top: 2px solid var(--border);
            td {
                padding: var(--p) calc(var(--p) / 2);
                font-weight: 600;
            }
        }
    }
    @media @mobile {
        thead {
            display: none;
        }
        tbody tr {
            display: flex;
            flex-wrap: wrap;
            td {
                height: auto;
            }
        }
    }
    .vate-rate-column {
        .b input {
            width: 65px;
        }
    }
    &.bordered {
        border: 1px solid var(--border);
        margin-bottom: 20px;
        border-radius: calc(var(--br) / 2);
    }
    &.nohov-table {
        tbody {
            tr {
                &:hover {
                    background: transparent;
                    cursor: default;
                    color: #000;
                }
            }
        }
    }
}

.nohov-table {
    table.table tbody tr:hover {
        background: transparent;
        cursor: default;
    }
}
.grid-flex {
    display: flex;
    min-height: calc(100% - 71px);
    flex-grow: 1;
    border: 1px solid var(--border);
    // box-shadow: var(--shadow);
    border-radius: var(--br);
    flex-grow: 1;
    background: var(--white);
    @media @mobile {
        flex-direction: column;
    }
    .right-sidebar {
        width: 250px;
        border-left: 1px solid var(--border);
        padding: var(--p);
        background: var(--bg);
        border-radius: 0 calc(var(--br) / 1) calc(var(--br) / 1) 0;
    }
    .adv-filters {
        width: 250px;
        border-right: 1px solid var(--border);
        padding: var(--p);
        background: var(--bg);
        border-radius: calc(var(--br) / 1) 0 0 calc(var(--br) / 1);
        &:empty {
            display: none;
        }
        @media @mobile {
            // width: 100%;
            // background-repeat: none;
            display: none;
        }
        .button {
            margin-bottom: calc(var(--p) * 2);
        }
    }
    .grid-main {
        width: 100%;
    }
}

.mobile-filters {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100vw;
    height: 100vh;
    z-index: 10;
    background: var(--bg);
    box-sizing: border-box;
    padding: var(--p);
    padding-top: 100px;
    .title {
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .button.small.secondary {
        margin-bottom: 20px;
        background: var(--primary);
        color: var(--white);
        width: 100%;
        height: 50px;
    }
}

.button.show-mobile-filters {
    display: none;
    @media @mobile {
        display: block;
        margin: 5px;
        // width: calc(100vw - 50px);
        text-align: center;
    }
}

.grid-header {
    display: flex;
    height: 60px;
    background: var(--bg);
    border-radius: 0 calc(var(--br) / 2) 0 0;
    margin-bottom: var(--p);
    border-bottom: 1px solid var(--border);
    @media @mobile {
        // width: max-content;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 0;
        height: auto;
    }
    .sort-filter {
        display: flex;
        padding-left: var(--p);
        align-items: center;
        gap: 10px;
        .button.drop.small.secondary svg {
            margin-right: 0px;
        }
        .top-sorting {
            display: flex;
            align-items: center;
            gap: 10px;
            .button.small {
                &>svg {
                    margin-right: 0;
                    path {
                        fill: var(--primary);
                    }
                }
            }
        }
        svg {
            height: 15px;
            width: 15px;
        }
    }
    .paginator {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-left: auto;
        padding-right: var(--p);
        @media @mobile {
            padding-bottom: 10px;
        }
        &>select {
            height: 30px;
            padding: 0px;
            @media @mobile {
                display: none;
            }
        }
        .results-total {
            white-space: nowrap;
            @media @mobile {
                display: none;
            }
        }
        .paginator-range-actions {
            display: flex;
            align-items: center;
            gap: 10px;
            .paginator-range {
                background: var(--highlight);
                font-size: .8rem;
                padding: 5px 8px;
                border-radius: var(--br);
                white-space: nowrap;
                @media @mobile {
                    display: none;
                }
            }
        }
        .paginator-range-buttons {
            display: flex;
            gap: 5px;
            button {
                .button;
                box-shadow: none;
                border: none;
                background: transparent;
                // border: 2px solid var(--secondary);
                color: var(--primary);
                padding: calc(var(--p) / 2);
                flex-grow: 0;
                height: 30px;
                &.selected {
                    background: var(--secondary);
                    color: var(--white);
                }
                &:hover {
                    color: var(--white);
                }
                &[disabled] {
                    background: transparent;
                    color: var(--grey);
                    box-shadow: none;
                    border: none;
                    cursor: default;
                }
            }
        }
    }
    .sort-filter {
        display: flex;
        flex-grow: 1;
    }
}

// .add-area {
//     .button {
//         display: inline-flex;
//     }
//     .filter-area {
//         background: var(--bg);
//         padding: var(--p);
//         display: grid;
//         gap: var(--p);
//         grid-template-columns: 1fr 1fr .5fr .5fr;
//         border-radius: calc(var(--p) / 2);
//         margin-top: var(--p);
//     }
// }

.mission-control {
    padding: var(--p);
    background: var(--secondary);
    // height: 100vh;
    width: 100%;
    box-sizing: border-box;
    main {
        background: var(--bg);
        border: none;
    }
    // h1 {
    //     font-weight: 600;
    //     color: var(--white);
    //     font-size: 30px;
    //     text-align: center;
    //     padding: calc(var(--p) * 3) 0;
    // }
    @media @mobile {
        .grid-header {
            display: none;
        }
    }
}

// .site-price-grid-container {
//     border-radius: var(--br);
//     border: 1px solid var(--border);
//     width: 500px;
// }

// .site-price-grid {
//     display: grid;
//     grid-template-columns: 60px 1fr 200px;
//     align-items: center;
//     border-bottom: 1px solid var(--border);
//     &>div {
//         padding: var(--p);
//     }
//     &.head {
//         border-bottom: 2px solid var(--border);
//         background: var(--bg);
//         font-weight: 600;
//         overflow: hidden;
//         border-radius: var(--br) var(--br) 0 0 ;
//     }
//     input[type="checkbox"] {
//         margin-left: 20px;
//     }
// }

.check-text {
    display: flex;
    .b:first-child {
        flex-grow: 1;
    }
    .b.check {
        display: flex;
        flex-direction: row-reverse;
        padding-top: 20px;
        padding-left: 20px;
        label {
            width: 80px;
        }
    }
}

.onstop {
    background: var(--red);
    color: var(--white);
    padding: 5px 10px;
    border-radius: 100px;
    display: inline-flex;
    margin-left: 10px;
    &.big {
        margin-left: 0;
        padding: var(--p);
        width: 100%;
        box-sizing: border-box;
        text-align: center;
        justify-content: center;
    }
}

.onstop-text {
    font-weight: 600;
    color: var(--red);
    display: inline-flex;
}

// .onstop-summary {
//     border-left: 1px solid var(--border);
//     border-right: 1px solid var(--border);
//     height: 100%;
//     width: 100%;
// }

strong {
    font-weight: 600;
}

.status-pill {
    background: #333;
    color: var(--white);
    padding: 5px 10px;
    border-radius: 100px;
    display: inline-flex;
    font-size: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 100px;
    width: fit-content;

    // margin-left: 10px;
    &.Invoice {background: var(--grey);}
    &.Saved {background: var(--primary);}
    &.Closed  {background: var(--secondary);}
    &.Void  {background: #ccc;}
    &.Late {background: var(--red;)}

    &.Completed {background: var(--green);}
    &.Confirmed, &.Finalised {background: var(--primary);}
    &.Cancelled {background: var(--red);}
    &.Draft, &.Open {background: var(--grey);}

    &.Rounds {background: var(--secondary);}
    &.Retail {background: var(--primary);}

    &.status-pill-small {
        padding: 2px 5px 0 6px;
        font-size: 8px;
    }
}

.print-eticket {
    background: var(--white);
    section {
        border: none;
        padding-bottom: 0px;
        .b {
            display: grid;
            grid-template-columns: 300px 1fr;
            margin-bottom: 10px;
            label {
                &+div {
                    font-weight: 600;
                }
            }
        }
    }

}

.filter-button {
    position: relative;
    .filter-dropdown {
        position: absolute;
        top: 50px;
        left: 0px;
        background: var(--white);
        border: 1px solid var(--border);
        box-shadow: var(--shadow2);
        border-radius: var(--br);
        padding: calc(var(--p) / 2);
        width: 200px;
        .title {
            font-size: 16px;
            margin-bottom: 5px;
            display: block;
        }
        ul {
            li {
                display: flex;
                gap: 5px;
                align-items: center;
            }
        }
    }
}

.select-etickets {
    .select-eticket-table {
        border: 1px solid var(--border);
        border-radius: var(--br);
        margin: var(--p) 0;
        .select-eticket-row {
            border-bottom: 1px solid var(--border);
            display: grid;
            gap: 10px;
            align-items: center;
            grid-template-columns: 40px 1fr 1fr 1fr;
            &>* {
                padding: calc(var(--p) / 2);
            }
            &:last-child {
                border-bottom: none;
            }
        }
    }
    
    .no-closed {
        color: var(--red);
        border: 1px solid var(--border);
        border-radius: var(--br);
        padding: var(--p);
        margin-top: var(--p);
        font-weight: 600;
    }
}

.no-results {
    // border-radius: var(--br);
    // background: var(--bg);
    display: flex;
    height: 100px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 400;
    color: var(--grey);
    margin-top: var(--p);
    // border-bottom: 1px solid var(--border);
    // box-shadow: inset 0 -20px 0 0 var(--white);
}

.product-switch {
    display: flex;
    gap: 10px; 
    &>div {
        border-radius: var(--br);
        border: 1px solid var(--border);
        padding: 0px;
        flex-grow: 1;
        // width: 90px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: 0.05s all ease-out;
        gap: 10px;
        svg {
            height: 17px;
            width: 17px;
        }
        &.selected {
            background: var(--primary);
            color: var(--white);
            border-color: var(--primary);
            font-weight: 600;
            cursor: default;
            svg {
                path {
                    fill: white;
                }
            }
            &:hover {
                border-color: var(--primary);
                background: var(--primary);
            }
        }
        &:hover {
            background: var(--primary);
            color: var(--white);
            border-color: var(--primary);
            svg {
                path {
                    fill: white;
                }
            }
        }
    }
}

.site-name {
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.tons-to {
    background: var(--bg);
    padding: var(--p);
    font-size: 1.4rem;
    border-radius: var(--br);
    margin-bottom: calc(var(--p) * 2);
    span {
        font-size: 2rem;
        font-weight: 600;
    }
}

.approval-flex {
    border-radius: var(--br);
    // border: 1px solid var(--border);
    display: flex;
    gap: 10px;
    .approval-side {
        width: 350px;
        background: var(--bg);
        border-radius: calc(var(--br) / 1) 0 0 calc(var(--br) / 1);
        // border-right: 1px solid var(--border);
        .approval-area {
            padding: var(--p);
            .invoices-left {
                border-radius: calc(var(--br) / 2);
                text-align: center;
                background: lightyellow;
                // border: 1px solid var(--border);
                padding: calc(var(--p) / 1.5);
                span {
                    font-weight: 600;
                }
            }
        }
        &>.approval-top {
            padding: var(--p) var(--p) 0 var(--p);
            text-align: center;
            box-sizing: border-box;
            .button {
                svg {
                    margin-right: 10px;
                    path {
                        fill: var(--white);
                    }
                }
            }
        }
        .grid-flex {
            flex-direction: column;
            table.table {
                
                td {
                    font-size: 12px;
                    line-height: 120%;
                    input[type="checkbox"] {
                        // margin-top: 17px;
                        // margin-right: 5px;
                        // border-color: var(--border);
                    }
                }
                th {
                    font-size: 10px;
                    input[type="checkbox"] { 
                        transform: translateY(7 px);
                    }
                }
            }
            .adv-filters {
                border-right: none;
                padding-top: 5px;
                padding-bottom: 5px;
                width: 100%;
                box-sizing: border-box;
                border-radius: var(--br);
                padding-top: 20px;
            }
            .grid-header {
                // flex-direction: column;
                display: grid;
                grid-template-columns: 1fr 1fr;
                margin-bottom: 0;
                select {
                    width: 90px;
                    padding: 5px;
                    height: 30px;
                }
                .sort-filter {
                    padding-left: 10px;
                }
                .button.tertiary.small {
                    height: 30px;
                    width: 15px;
                }
                .paginator {
                    gap: 5px;
                }
                .results-total {
                    &>span:first-child {
                        display: block;
                        background: var(--primary);
                        color: var(--white);
                        padding: 5px 10px;
                        border-radius: var(--br);
                        &:after {
                            content: ' Invs.';
                        }
                    }
                    &>span:last-child {
                        display: none;
                    }
                }
                .paginator select {
                    display: none;
                }
                .paginator-range {
                    display: none;
                }
                .paginator-range-buttons {
                    gap: 0;
                }
                .paginator-range-actions {
                    .first-page, .page-number, .last-page {
                        display: none;
                    }
                }
                .top-sorting {
                    &>label {
                        display: none;
                    }
                    & + .button {
                        display: none;
                    }
                } 
            }
        }
    }
}

.paper {
    border: 1px solid var(--border);
    box-shadow: var(--shadow);
    background: var(--white);
    width: 100%;
    flex-grow: 1;
    padding: calc(var(--p) * 3) calc(var(--p) * 2);
    box-sizing: border-box;
    line-height: 120%;
    .company-name {
        font-size: 36px;
        font-weight: 600;
        margin-bottom: calc(var(--p) * 2);
    }
    .paper-top {
        display: flex;
        width: 100%;
        margin-bottom: calc(var(--p) * 2);
        &>div {
            flex-grow: 1;
            &:last-child {
                text-align: right;
            }
        }
        .invoice-title {
            font-size: 30px;
            font-weight: 600;
            // margin-bottom: calc(var(--p) * 2);
            margin-top: calc(var(--p) * 8.5);
        }
        .company-address {
            margin-bottom: calc(var(--p) * 2);
        }
        .contact-details {
            &>div{
                margin-bottom: 5px;
                display: flex;
                align-items: center;
                &>div:first-child {
                    // font-size: 10px;
                    font-weight: 600;
                    width: 60px;
                }
            }
        }
    }
    .paper-middle {
        display: flex;
        width: 100%;
        border: 1px solid #ddd;
        padding: 20px;
        box-sizing: border-box;
        margin-bottom: 30px;
        .title {
            margin-bottom: 15px;
        }
        .paper-block:last-child {
            margin-bottom: 0;
        }
        &>div.paper-block:first-child {
            margin-bottom: 0;
        }
        &>div:last-child {
            flex-grow: 1;
            text-align: right;
        }
    }
    .invoice-to {
        border: 1px solid #ddd;
        padding: 20px;
    }
    .paper-block {
        display: flex;
        flex-direction: column;
        margin-bottom: var(--p);
        &>div:first-child {
            font-weight: 600;
        }
    }
    .paper-rows {
        table {
            width: 100%;
            th {
                background: #eee;
                padding: 5px 10px;
                text-align: left;
                font-weight: 600;
                font-size: 10px;
                &.text-right {
                    text-align: right;
                }
            }
            td {
                padding: 10px;
                font-size: 12px;
                &.text-right {
                    text-align: right;
                }
                // .button.small {
                //     padding:  5px;
                //     height: 22px;
                //     font-size: 10px;
                // }
                a {
                    text-decoration: underline;
                    &:hover {
                        background: lightyellow;
                    }
                }
            }
            tbody {
                td {
                    border-bottom: 1px solid #ddd;
                }
            }
            .tfoot-bg {
                background: #eee;
            }
            .tfoot-total {
                font-size: 20px;
                font-weight: 600;
                border-top: 1px solid #ddd;
            }
        }
    }
    .paper-footer {
        display: flex;
        gap: 60px;
        margin-top: 100px;
        .title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px !important;
        }
        .payment-details {
            &>div{
                margin-bottom: 5px;
                display: flex;
                align-items: center;
                &>div:first-child {
                    // font-size: 10px;
                    font-weight: 600;
                    width: 100px;
                }
            }
        }
        .terms-details {

        }
    }
    .paper-bottom {
        margin-top: 100px;
        p {
            margin-bottom: 10px;
        }
    }
}

.invoice-container {
    padding: var(--p);
    width: 100%;
    border: 1px solid var(--border);
    border-radius: var(--br);
    &>header {
        .title {
            font-size: 14px;
        }
    }
}

input[type="file"] {
    position: absolute;
    z-index: -1;
    top: 0px;
    left: 0px;
    font-size: 17px;
    color: #b8b8b8;
    height: 100px;

  }
.upload-wrap {
    position: relative;
}

.upload-text {
    .button;
    height: 100px;
}

//   .button {
//     display: inline-block;
//     padding: 12px 18px;
//     cursor: pointer;
//     border-radius: 5px;
//     background-color: #8ebf42;
//     font-size: 16px;
//     font-weight: bold;
//     color: #fff;
//   }

.check-list {
    .b {
        flex-direction: row-reverse;
        justify-content: flex-end;
        margin-bottom: 5px;
    }
}

.row {
    display: flex;
    padding: calc(var(--p) / 2) var(--p);
    border-bottom: 1px solid var(--border);
    gap: 14px;
    &.hover {
        cursor: pointer;
        &:hover {
            background: var(--bg);
        }
    }
    &:last-child {
        border-bottom: none;
    }
}

.date-filters {
    display: flex;
    // background: var(--bg);
    // padding: var(--p);
    margin-bottom: var(--p);
    // border: 1px solid var(--border);
    // border-radius: var(--br);
    align-items: center;
    gap: 10px;
    @media @mobile {
        flex-direction: column;
        gap: 10px;
    }
    .dates {
        display: flex;
        gap: 10px;
        @media @mobile {
            display: grid;
            width: 100%;
            // grid-template-columns: 1fr 1fr;
        }
    }
    .date-buttons {
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;
        &>.button {
            &:first-child {
                width: 40px;
            }
            &:nth-child(6) {
                @media @mobile {
                    grid-column: 2;
                }
            }
        }
        @media @mobile {
            display: grid;
            grid-template-columns: 40px 1fr 1fr 1fr 1fr;
            width: 100%;
            gap: 5px;
            &>.button {
                padding: 5px;
                font-size: 10px;
                // align-items: center;
                // justify-content: center;
                text-align: center;
                &:first-child {
                    width: 100%;
                }
            }
        }

    }
    input {
        font-family: 'Poppins', sans-serif;
        width: 200px;
        @media @mobile {
            font-size: 10px;
            width: 100%;
        }
    }
    .b {
        display: flex;
        flex-direction: row;
        @media @mobile {
            flex-direction: column;
            align-items: flex-start;
            width: 100%;
            // &:first-child {
            //     margin-bottom: 0px;
            // }
        }
        label {
            width: auto;
            margin-left: 30px;
            @media @mobile {
                margin-left: 0;
            }
        }
        input {
            width: 130px;
            @media @mobile {
                width: 140px;
                box-sizing: border-box;
            }
        }
    }
    .button.small.tertiary {
        svg {
            height: 20px;
            margin-right: 0;
            path {
                fill: var(--secondary);
                
            }
        }
    }
}

.daterangepicker {
    position: fixed;
    color: inherit;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ddd;
    width: 278px;
    max-width: none;
    padding: 0;
    margin-top: 7px;
    top: 0;
    left: 0;
    right: auto;
    z-index: 3001;
    font-family: arial;
    font-size: 15px;
    line-height: 1em;
}

.daterangepicker:before,.daterangepicker:after {
    position: absolute;
    display: inline-block;
    border-bottom-color: rgba(0,0,0,.2);
    content: '';
}

.daterangepicker:before {
    top: -7px;
    border-right: 7px solid transparent;
    border-left: 7px solid transparent;
    border-bottom: 7px solid #ccc;
}

.daterangepicker:after {
    top: -6px;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #fff;
    border-left: 6px solid transparent;
}

.daterangepicker.inline:before,.daterangepicker.inline:after {
    content: none;
}

.daterangepicker.inline {
    position: inherit;
    display: inline-block;
}

.daterangepicker.opensleft:before {
    right: 9px;
}

.daterangepicker.opensleft:after {
    right: 10px;
}

.daterangepicker.openscenter:before {
    left: 0;
    right: 0;
    width: 0;
    margin-left: auto;
    margin-right: auto;
}

.daterangepicker.openscenter:after {
    left: 0;
    right: 0;
    width: 0;
    margin-left: auto;
    margin-right: auto;
}

.daterangepicker.opensright:before {
    left: 9px;
}

.daterangepicker.opensright:after {
    left: 10px;
}

.daterangepicker.drop-up {
    margin-top: -7px;
}

.daterangepicker.drop-up:before {
    top: initial;
    bottom: -7px;
    border-bottom: initial;
    border-top: 7px solid #ccc;
}

.daterangepicker.drop-up:after {
    top: initial;
    bottom: -6px;
    border-bottom: initial;
    border-top: 6px solid #fff;
}

.daterangepicker.single .daterangepicker .ranges,.daterangepicker.single .drp-calendar {
    float: none;
}

.daterangepicker.single .drp-selected {
    display: none;
}

.daterangepicker.show-calendar .drp-calendar {
    display: block;
}

.daterangepicker.show-calendar .drp-buttons {
    display: block;
}

.daterangepicker.auto-apply .drp-buttons {
    display: none;
}

.daterangepicker .drp-calendar {
    display: none;
    max-width: 270px;
}

.daterangepicker .drp-calendar.left {
    padding: 8px 0 8px 8px;
}

.daterangepicker .drp-calendar.right {
    padding: 8px;
}

.daterangepicker .drp-calendar.single .calendar-table {
    border: none;
}

.daterangepicker .calendar-table .next span,.daterangepicker .calendar-table .prev span {
    color: #fff;
    border: solid #000;
    border-width: 0 2px 2px 0;
    border-radius: 0;
    display: inline-block;
    padding: 3px;
}

.daterangepicker .calendar-table .next span {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
}

.daterangepicker .calendar-table .prev span {
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
}

.daterangepicker .calendar-table th,.daterangepicker .calendar-table td {
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    min-width: 32px;
    width: 32px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid transparent;
    white-space: nowrap;
    cursor: pointer;
}

.daterangepicker .calendar-table {
    border: 1px solid #fff;
    border-radius: 4px;
    background-color: #fff;
}

.daterangepicker .calendar-table table {
    width: 100%;
    margin: 0;
    border-spacing: 0;
    border-collapse: collapse;
}

.daterangepicker td.available:hover,.daterangepicker th.available:hover {
    background-color: #eee;
    border-color: transparent;
    color: inherit;
}

.daterangepicker td.week,.daterangepicker th.week {
    font-size: 80%;
    color: #ccc;
}

.daterangepicker td.disabled,.daterangepicker option.disabled {
    color: #999;
    cursor: not-allowed;
    text-decoration: line-through;
}

.daterangepicker td.off,.daterangepicker td.off.in-range,.daterangepicker td.off.start-date,.daterangepicker td.off.end-date {
    background-color: #fff;
    border-color: transparent;
    color: #999;
}

.daterangepicker td.in-range {
    background-color: #ebf4f8;
    border-color: transparent;
    color: #000;
    border-radius: 0;
}

.daterangepicker td.start-date {
    border-radius: 4px 0 0 4px;
}

.daterangepicker td.end-date {
    border-radius: 0 4px 4px 0;
}

.daterangepicker td.start-date.end-date {
    border-radius: 4px;
}

.daterangepicker td.active,.daterangepicker td.active:hover {
    background-color: #357ebd;
    border-color: transparent;
    color: #fff;
}

.daterangepicker th.month {
    width: auto;
}

.daterangepicker select.monthselect,.daterangepicker select.yearselect {
    font-size: 12px;
    padding: 1px;
    height: auto;
    margin: 0;
    cursor: default;
}

.daterangepicker select.monthselect {
    margin-right: 2%;
    width: 56%;
}

.daterangepicker select.yearselect {
    width: 40%;
}

.daterangepicker select.hourselect,.daterangepicker select.minuteselect,.daterangepicker select.secondselect,.daterangepicker select.ampmselect {
    width: 50px;
    margin: 0 auto;
    background: #eee;
    border: 1px solid #eee;
    padding: 2px;
    outline: 0;
    font-size: 12px;
}

.daterangepicker .calendar-time {
    text-align: center;
    margin: 4px auto 0 auto;
    line-height: 30px;
    position: relative;
}

.daterangepicker .calendar-time select.disabled {
    color: #ccc;
    cursor: not-allowed;
}

.daterangepicker .drp-buttons {
    clear: both;
    text-align: right;
    padding: 8px;
    border-top: 1px solid #ddd;
    display: none;
    line-height: 12px;
    vertical-align: middle;
}

.daterangepicker .drp-selected {
    display: inline-block;
    font-size: 12px;
    padding-right: 8px;
}

.daterangepicker .drp-buttons .btn {
    margin-left: 8px;
    font-size: 12px;
    font-weight: bold;
    padding: 4px 8px;
}

.daterangepicker.show-ranges.single.rtl .drp-calendar.left {
    border-right: 1px solid #ddd;
}

.daterangepicker.show-ranges.single.ltr .drp-calendar.left {
    border-left: 1px solid #ddd;
}

.daterangepicker.show-ranges.rtl .drp-calendar.right {
    border-right: 1px solid #ddd;
}

.daterangepicker.show-ranges.ltr .drp-calendar.left {
    border-left: 1px solid #ddd;
}

.daterangepicker .ranges {
    float: none;
    text-align: left;
    margin: 0;
}

.daterangepicker.show-calendar .ranges {
    margin-top: 8px;
}

.daterangepicker .ranges ul {
    list-style: none;
    margin: 0 auto;
    padding: 0;
    width: 100%;
}

.daterangepicker .ranges li {
    font-size: 12px;
    padding: 8px 12px;
    cursor: pointer;
}

.daterangepicker .ranges li:hover {
    background-color: #eee;
}

.daterangepicker .ranges li.active {
    background-color: #08c;
    color: #fff;
}

@media(min-width: 564px) {
    .daterangepicker {
        width:auto;
    }

    .daterangepicker .ranges ul {
        width: 140px;
    }

    .daterangepicker.single .ranges ul {
        width: 100%;
    }

    .daterangepicker.single .drp-calendar.left {
        clear: none;
    }

    .daterangepicker.single .ranges,.daterangepicker.single .drp-calendar {
        float: left;
    }

    .daterangepicker {
        direction: ltr;
        text-align: left;
    }

    .daterangepicker .drp-calendar.left {
        clear: left;
        margin-right: 0;
    }

    .daterangepicker .drp-calendar.left .calendar-table {
        border-right: none;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .daterangepicker .drp-calendar.right {
        margin-left: 0;
    }

    .daterangepicker .drp-calendar.right .calendar-table {
        border-left: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .daterangepicker .drp-calendar.left .calendar-table {
        padding-right: 8px;
    }

    .daterangepicker .ranges,.daterangepicker .drp-calendar {
        float: left;
    }
}

@media(min-width: 730px) {
    .daterangepicker .ranges {
        width:auto;
    }

    .daterangepicker .ranges {
        float: left;
    }

    .daterangepicker.rtl .ranges {
        float: right;
    }

    .daterangepicker .drp-calendar.left {
        clear: none !important;
    }
}

.daterangepicker-visibility-hidden {
    display: none;
}


.daterangepicker td.in-range, .daterangepicker td.available:hover, .daterangepicker th.available:hover {
    background-color: var(--secondary);
}

.daterangepicker td.active, .daterangepicker td.active:hover {
    background-color: var(--primary);
}



.daterangepicker .drp-buttons .btn {
    background: var(--primary);
    color: var(--white);
    padding: calc(var(--p) / 1.5) var(--p);
    border-radius: calc(var(--br) / 2);
    border: none;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 0 0 rgba(0,0,0,.05), inset 0 2px 5px 0 rgba(255,255,255,.2);
    border: 1px solid rgba(0,0,0,0.1);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;

    padding: calc(var(--p) / 2) calc(var(--p) / 1.5);
    display: inline-flex;
    font-size: 14px;
    height: 40px;

    color: var(--primary);
    background: transparent;
    &:hover {
        background: var(--highlight);
    }

    &.btn-primary {
        background: var(--primary);
        color: var(--white);
    }
}

// .select2-results__options li:first-child {
//     height: 0px;
//     padding: 0px;
// }

.tonnage-table-container {
    overflow: auto;
    table th:first-child, table td:first-child {
        position: sticky;
        left: -1px;
        top: 0px;
        background: white;
    }
}

.contract-text {
    // font-size: 10px;
    // width: 100px;
    // height: 20px;
    font-size: 12px;
    margin-top: 5px;
    color: #333;
    font-weight: 600;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
}

.reports-list {
    display: grid;
    grid-template-columns: 1fr;
    width: 100%;
    border: 1px solid var(--border);
    border-radius: var(--br);
    a {
        display: flex;
        padding: 20px;
        border-bottom: 1px solid var(--border);
        gap: 10px;
        align-items: center;
        font-weight: 600;
        svg {
            width: 30px;
            path { 
                fill: var(--secondary);
            }
        }
        &:first-child {
            border-radius: var(--br) var(--br) 0 0;
        }
        &:last-child {
            border-bottom: none;
            border-radius: 0 0 var(--br) var(--br);
        }
        &:hover {
            background: var(--bg);
        }
    }
}

.chart-container {
    // display: grid;
    width: 80px;
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    &>* {
        border-radius: var(--br);
        background: var(--secondary);
        height: 14px;
    }
}

.coming-soon-blocks {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    li {
        a {
            background: var(--white);
            box-shadow: var(--shadow);
            padding: var(--p);
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            border: 1px solid var(--border);
            border-radius: var(--br);
            gap: 10px;
        }
        
    }
}

.initial-circle {
    border-radius: 50%;
    height: 30px;
    width: 30px;
    background: var(--primary);
    color: var(--white);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.attendance-group {
    border: 1px solid var(--border);
    margin-bottom: 10px;
    padding: calc(var(--p) / 1) var(--p) ;
    border-radius: var(--br);
    .subtitle {
        margin-bottom: 10px;
    }
    .attendance-row-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        .attendance-group--title {
            display: flex;
            gap: 10px;
            width: 200px;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
        }
        .attendance-row--month-title {
            display: grid;
            gap: 2px;
            flex-grow: 1;
            grid-template-columns: repeat(30, 1fr);
            &>* {
                height: 25px;
                background: #fff;
                display: flex;
                align-items: center;
                flex-direction: column;
                gap: 3px;
                padding: 5px;
                border-radius: calc(var(--br) / 2);
                &>* {
                    &:first-child {
                        font-weight: 600;
                        font-size: 12px;
                    }
                    &:last-child {
                        font-size: 10px;
                        color: var(--grey);
                    }
                }
            }
        }
    }  
    .attendance-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .attendance-staff {
            display: flex;
            gap: 10px;
            width: 200px;
            align-items: center;
            .attendance-staff--namejob {
                display: flex;
                flex-direction: column;
            }
            .attendance-staff--name {
                font-weight: 600;
            }
            .attendance-staff--job {
                font-size: 10px;
            }
        }
        .attendance-row--month {
            display: grid;
            gap: 2px;
            flex-grow: 1;
            grid-template-columns: repeat(30, 1fr);
            &>* {
                height: 30px;
                background: #eee;
                display: flex;
                align-items: center;
                font-size: 10px;
                justify-content: center;
                position: relative;
                border-radius: calc(var(--br) / 2);
                &:hover {
                    cursor: pointer;
                    background: #ddd;
                }
                &.selected {
                    box-shadow: inset 0 0 0 2px var(--primary);
                    .workforce-pop {
                        display: flex;
                    }
                }
                .workforce-pop {
                    position: absolute;
                    display: none;
                    background: var(--white);
                    border-radius: var(--br);
                    box-shadow: var(--shadow2);
                    z-index: 9;
                    padding: var(--p);
                    border: 2px solid var(--primary);
                    bottom: 37px;
                    width: 200px;
                    flex-direction: column;
                    gap: 20px;
                    &:after {
                        top: 100%;
                        left: 50%;
                        border: solid transparent;
                        content: "";
                        height: 0;
                        width: 0;
                        position: absolute;
                        pointer-events: none;
                        border-color: rgba(136, 183, 213, 0);
                        border-top-color: var(--primary);
                        border-width: 10px;
                        margin-left: -10px;
                    }
                    .date {
                        color: var(--primary);
                        font-size: 12px;
                        font-weight: 600;
                    }
                    .name {
                        font-size: 16px;
                        font-weight: 600;
                    }
                    .clock {
                        display: flex;
                        gap: 20px;
                        // margin-bottom: 20px;
                        &>* {
                            display: flex;
                            gap: 5px;
                            flex-direction: column;
                            font-weight: 600;
                            &>*:first-child {
                                font-size: 20px;
                            }
                            svg {
                                width: 15px;
                                height: 15px;
                                margin-left: 10px;
                            }
                            svg path {
                                fill: var(--secondary);

                            }
                        }
                    }
                    .button.small {
                        width: 100%;
                    }
                }
            }
            &>*.a-holiday {
                background: var(--primary);
                &:after {
                    content: 'H';
                    background: rgba(0,0,0,0.05);
                    border-radius: 100px;
                    padding: 3px 5px;
                    color: var(--white);
                }
            }
            &>*.a-sickness {
                background: var(--red);
                &:after {
                    content: 'S';
                    background: rgba(0,0,0,0.05);
                    border-radius: 100px;
                    padding: 3px 5px;
                    color: var(--white);
                }
            }
            &>*.bank-holiday {
                background: var(--grey);
                &:after {
                    content: 'B';
                    background: rgba(0,0,0,0.05);
                    border-radius: 100px;
                    padding: 3px 5px;
                    color: var(--white);
                } 
            }
            &>*.clocked {
                background: var(--secondary);
                &>svg {
                    width: 20px;
                    path {
                        fill: var(--white);
                    }
                }
            }
        }
    }   
}
.attendance-row:nth-child(odd){
    .attendance-row--month > * {
        background: #f8f8f8;
        &:hover {
            cursor: pointer;
            background: #ddd;
        }
    }
}

.month-spinner {
    border-radius: var(--br);
    border: 1px solid var(--border);
    display: inline-flex;
    overflow: hidden;
    margin-bottom: 10px;
    &>div {
        padding: var(--p);
        font-size: 20px;
        font-weight: 600;
        &:first-child, &:last-child {
            cursor: pointer;
            &:hover {
                background: var(--bg);
            }
        }
        &:first-child {
            border-right: 1px solid var(--border);
        }
        &:last-child {
            border-left: 1px solid var(--border);
        }
        &:nth-child(2) {
            width: 160px;
            display: flex;
            justify-content: center;
        }
    }
    
}

@media @mobile {
    .eticket-list {
        table.table {
            tbody {
                tr {
                    display: grid;
                    grid-template-columns: 1.75fr 1fr 1fr;
                    padding: 10px;
                    td {
                        font-size: 12px;
                        padding: 5px;
                        align-items: center;
                        display: flex;
                        .small-reg-plate {
                            width: 100%;
                        }   
                        &:nth-child(5) {
                            display: none;
                        }
                    }
                }
            }    
        }
    }
}

.rc-options {
    display: flex;
    border-radius: var(--br);
    border: 1px solid var(--border);
    background: var(--bg);
    padding: var(--p);
    align-items: center;
    display: inline-flex;
    gap: 10px;
    margin-bottom: var(--p);
    div {
        &.date-range {
            font-weight: 600;
            @media @mobile {
                text-align: center;
            }
        }
    }
}

.route-calendar {
    display: grid;
    border-radius: var(--br);
    height: 100%;
    flex-grow: 1;
    &.rc-week {
        grid-template-columns: repeat(7, 1fr);
        gap: 10px;
        @media @mobile {
            grid-template-columns: 1fr;
        }
        &>div {
            display: flex;
            flex-direction: column;
            gap: 10px;
            border-radius: calc(var(--br) / 2);
            height: 100%;
            @media @mobile {
                border-bottom: 1px solid var(--border);
                padding-bottom: 8px;
                border-radius: 0px;
            }
            &.empty-day {
                @media @mobile {
                    padding-bottom: 0px;
                    opacity: .3;
                }
            }
            &.current-day {
                background: var(--lightyellow);
                box-shadow: 0 0 0 5px var(--lightyellow), 0 0 0 7px var(--primary);
                border-bottom: none;
                border-radius: 10px;
                .rc-grid {
                    // background: var(--lightyellow);
                }
            }
        }
    }
    .rc-header {
        padding: 10px;
        display: flex;
        flex-direction: column;
        gap: 5px;
        .print-route-buttons {
            display: flex;
            column-gap: 10px;
            flex-wrap: wrap;
        }
        border-radius: var(--br) 0 0 0;
        .button {
            font-size: 12px;
            padding: 5px 0px;
            // width: 40px;
            font-weight: 600;
            height: 24px;
            border: none;
            box-shadow: none;
            // white-space: nowrap;
            &:hover {
                text-decoration: underline;
            }
        }
        &>* {
            &:first-child {
                color: var(--primary);
                font-size: 18px;
                font-weight: 600;
            }
        }
    }
    .rc-footer {
        padding: 5px;
        &>div {
            margin-bottom: calc(var(--p) / 2);
            .subtitle {
                font-size: 14px;
                margin-bottom: 0px;
            }
            ul {
                li {
                    margin-bottom: 5px;
                }
            }
        }
    }
    .rc-item-list {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        gap: 10px;
        .rc-item {
            border: 1px solid var(--border);
            border-radius: calc(var(--br) / 2);
            padding: 0px;
            display: flex;
            flex-direction: column;
            background: var(--white);
            font-size: 12px;
            cursor: pointer;
            &.DriverReturned {
                .rc-vehicle:after {
                    content: 'Driver Returned';
                    background: var(--yellow);
                    font-size: 10px;
                    font-weight: 600;   
                    color: black;
                    padding: 0 2px;
                    border-radius: 2px;
                    transform: translateX(5px);
                    box-shadow: 0 0 0px 2px var(--yellow);
                }
            }
            &.Completed {
                background: var(--disabled);
                .rc-vehicle{
                    background: transparent !important;
                    color: var(--primary);
                    &:after {
                        content: 'Completed';
                        // background: var(--green);
                        font-size: 10px;
                        font-weight: 600;   
                        color: black;
                        padding: 0 2px;
                        border-radius: 2px;
                        transform: translateX(5px);
                        // box-shadow: 0 0 0px 2px var(--green);
                    }
                    &[data-area="R1"],&[data-area="R2"],&[data-area="R3"],&[data-area="R4"],&[data-area="R5"],&[data-area="R6"] {
                        background: transparent;
                        color: #b41c1c;
                    }
                }
            }
            &:hover {
                cursor: pointer;
                border-color: var(--primary);
            }
            
            .rc-grid {
                display: grid;
                grid-template-columns: 1fr;
                gap: 10px;
                padding: 10px;
                .rc-info {
                    display: flex;
                    svg {
                        height: 10px;
                        width: 20px;
                        path {
                            fill: var(--grey);
                        }
                    }
                }
                .rc-area {
                    .pill-choose;
                }
            }
            .rc-vehicle {
                background: var(--primary);
                color: #fff;
                padding: 5px calc(var(--p) / 2);
                border-radius: calc(var(--br) / 2) calc(var(--br) / 2) 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                &>*:first-child {
                    font-weight: 600;
                }
                
                
                &[data-area="Route 1"] {
                    background: var(--primary);  
                }
                &[data-area="Route 2"] {
                    background: var(--secondary);  
                }
                &[data-area="Route 3"] {
                    background: #578310;  
                }
                &[data-area="R1"],&[data-area="R2"],&[data-area="R3"],&[data-area="R4"],&[data-area="R5"],&[data-area="R6"] {
                    background: #b41c1c;  
                }
                &[data-area="T1"],&[data-area="T2"],&[data-area="T3"],&[data-area="T4"],&[data-area="T5"],&[data-area="T6"] {
                    background: #666;  
                }
                &[data-area="Route 5"] {
                    background: #104b83; 
                }
                &[data-area="Transit 1"], &[data-area="Transit 2"], &[data-area="Transit 3"], &[data-area="Transit 4"], &[data-area="Transit 5"]{
                    background: var(--grey);  
                }

                &.empty-route {
                    background: white;   
                    color: var(--primary);
                    box-shadow: 0 1px 0 0 rgba(0,0,0,0.1);
                    &[data-area="R1"],&[data-area="R2"],&[data-area="R3"],&[data-area="R4"],&[data-area="R5"],&[data-area="R6"] {
                        color: #b41c1c;
                    }
                }
            }
            
        }
    }
}

.route-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    @media @mobile {
        grid-template-columns: 1fr;
    }
    .route-map {
        @media @mobile {
            display: none;
        }
        img {
            width: 100%;
            border: 1px solid var(--border);
            border-radius: calc(var(--br) / 2);
        }
    }
}

.fake-input {
    border: 1px solid var(--border);
    border-radius: calc(var(--br) / 4);
    height: 46px;
    display: flex;
    align-items: center;
    padding: 10px;
    box-sizing: border-box;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: 0.2s all ease-out;
    &:hover {
        background: var(--yellow2);
    }
}

.money-collected-flex {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    // margin-bottom: 20px;
    &>section:last-child {
        margin-bottom: 20px;
    }
}

.cash-grid {
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
    // max-width: 400px;
    width: 100%;
    margin-top: 10px;
    &>div {
        // border-bottom: 1px solid var(--border);
        padding: 5px;
        width: 100%;
        flex-direction: column;
        gap: 5px;
        display: flex;
        &:nth-child(even){
            background: var(--bg);
        }
        // padding: 5px;
        label {
            padding: 5px;
        }
        input {
            height: 24px;
        }
        &.cash-balance {
            display: grid;
            background: var(--green);
            font-weight: 600;
            color: white;
            // border-top: 2px solid var(--primary);
            padding-top: 5px;
            grid-template-columns: .5fr 1fr;
            &>div {
                padding: 5px;
            }
            &.red {
                background: var(--red);
            }
        }
    }
    .b {
        grid-template-columns: .5fr 1fr;
        align-items: center;
    }
}

.photo-required {
    border-radius: 100px;
    background: var(--primary);
    padding: 5px 10px;
    color: var(--white);
    display: inline-flex;
    align-self: flex-start;
}

.signature-required {
    border-radius: 100px;
    background: var(--secondary);
    padding: 5px 10px;
    color: var(--white);
    display: inline-flex;
    align-self: flex-start;
}

.required-notes {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    flex-wrap: wrap;
    .get-tonnage {
        // display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        display: none;
        gap: 5px;
        margin-bottom: 10px;
        &>div {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        @media @mobile {
            display: grid;
        }
        label {
            font-size: 10px;
        }
        input {
            width: 100%;
            border: 2px solid var(--secondary);
            &[readonly]{
                background: var(--bg);
                border: 2px solid #ddd;
            }
        }
    }
}

.stop-list {
    display: flex;
    flex-direction: column;
    // border-radius: calc(var(--br) / 2);
    border-right: 1px solid var(--border);
    // border: 1px solid var(--border);
    // .route-buttons {
    //     grid-template-columns: 160px 1fr;
    // }
    &.haulier-route {
        &>div {
            border-bottom: 1px solid var(--border);
            display: grid;
            grid-template-columns: 20px 20px 58px .8fr 1fr 30px;
            &.tip-stop {
                background: var(--bg);
                border-left: 10px solid #08382829;
                .route-address {
                    padding-left: 15px;
                    position: relative;
                    &:after {
                        content: "";
                        position: absolute;
                        top: -20px;
                        left: -10px;
                        border-left: 2px solid var(--border);
                        border-bottom: 2px solid var(--border);
                        height: 30px;
                        width: 15px;
                    }
                }
            }
            @media @mobile {
                grid-template-columns: 20px 58px 1fr;
            }
            div.route-notes-area {
                @media @mobile {
                    grid-column: 1 / -1;
                }
            }
        }
        .button.button-full {
            display: none;
            @media @mobile {
                display: flex;
            }
        }
        .stop-notes {
            padding: 10px;
            background: var(--lightyellow);
            border: 1px solid #dbce94;
            border-radius: calc(var(--br) / 2);
            &>div {
                font-weight: 600;
                margin-bottom: 5px;
            }
        }
        .kebab-menu {
            @media @mobile {
                display: none;
            }
        }
     
                // &:nth-child(1) {
                //     .move-route > *:first-child {
                //         pointer-events: none;
                //         opacity: 0.3;
                //         &:hover {
                //             cursor: default;
                //             background: var(--bg);
                //             color: var(--grey);
                //         }
                //     }
                // }
                // &:nth-last-child(1) {
                //     .move-route > *:last-child {
                //         pointer-events: none;
                //         opacity: 0.3;
                //         &:hover {
                //             cursor: default;
                //             background: var(--bg);
                //             color: var(--grey);
                //         }
                //     }
                // }

    }
    .route-check {
        input {
            width: 15px;
            height: 15px;
        }
    }
    &.route-finished {
        position: sticky;
        top: -20px;
        left: 0;
        background: white;
        z-index: 999;
        .b.check {
            padding-top: 0;
        }
        &>div {
            padding: 0;
            grid-template-columns: 20px 45px 1fr 190px 80px 100px 125px 100px 50px 310px;
            input {
                padding: 5px;
                margin-bottom: 5px;
            }
            .select2-container--default .select2-selection--single {
                height: 32px;
            }
            .select2-container--default .select2-selection--single .select2-selection__rendered {
                padding-top: 2px;
            }
            .select2-container--default .select2-selection--single .select2-selection__arrow {
                top: -6px;
            }
            .stop-list.route-finished > div .select2-container--default .select2-selection--single .select2-selection__rendered {
                height: 28px;
            }
            &:first-child {
                div {
                    padding: 5px 0;
                }
            }
        }
        &.and-completed {
            &>div {
                grid-template-columns: 20px 1fr 300px 80px 80px 100px 125px 100px 100px;
            }
        }
        
        .returned-options {
            div {
                display: flex;
                align-items: center;
                margin-bottom: 5px;
                label {
                    width: 200px;
                    font-weight: 600;
                }
                select, .b {
                    width: 100%;
                    flex-grow: 1;
                }
            }
        }
        .route-products {
            border: 1px solid var(--border);
            border-radius: calc(var(--br) / 2);
            // margin-bottom: 5px;
            &> div {
                padding: 10px;
                // font-size: 14px;
                border-bottom: 1px solid var(--border);
                // font-weight: 600;
                display: flex;
                justify-content: space-between;
                &:last-child {
                    border-bottom: none;
                }
            }
            &:hover {
                position: relative;
                cursor: pointer;
                background: var(--primary-fade);
                border-color: var(--primary);
                // box-shadow: 0 0 0px 2px var(--primary);
                &>div {
                    border-bottom-color: var(--primary);
                }
                &:after {
                    content: 'Edit';
                    position: absolute;
                    top: 100%;
                    left: 50%;
                    background: var(--primary-fade);
                    color: var(--primary);
                    padding: 5px 10px;
                    border-radius: 0 0 calc(var(--br) / 3) calc(var(--br) / 3);
                }
            }
        }
    }
    &>div {
        border-bottom: 1px solid var(--border);
        display: grid;
        grid-template-columns: 20px 20px 50px 1.2fr 1fr 50px 30px;
        padding: calc(var(--p) / 2);
        align-items: center;
        font-size: 12px;
        gap: 10px;
        .driver-returned-payment {
            padding: 5px;
        }
        .stop-images {
            display: flex;
            width: 100%;
            gap: 5px;
            max-width: 500px;
            flex-wrap: wrap;
            .signature-container {
                display: flex;
                flex-direction: column;
                gap: 5px;
                font-size: 9px;
                justify-content: flex-end;
                .stop-image {
                    height: 30px;
                }
            }
            a {
                display: inline-flex;
                position: relative;
                .remove {
                    // border-radius: 50%;
                    height: 15px;
                    width: 15px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    // background: var(--primary-fade);
                    position: absolute;
                    bottom: 4px;
                    right: 4px;
                    &:hover {
                        &>svg {
                            path {
                                fill: var(--red);
                            }
                        }
                    }
                    &>svg {
                        height: 14px;
                        width: 14px;
                        path {
                            fill: var(--primary);
                        }
                    }
                }
            }
            .stop-image {
                height: 50px;
                width: 70px;
                border-radius: calc(var(--br) / 2);
                background-size: cover;
                border: 1px solid var(--border);
            }
        }
        .type-time {
            line-height: 140%;
            .haulier-fee {
                // background: var(--secondary);
                border: 1px solid var(--primary);
                color: var(--primary);
                border-radius: 5px;
                padding: 2px 5px;
                font-size: 10px;
                margin-top: 5px;
                display: inline-flex;
                font-weight: 600;
                @media @mobile {
                    display: none;
                }
            }
        }
        .button.inline.small {
            border: none;
        }
        &.fake-stop {
            border-bottom: none;
            padding: 0;
        }
        &.disabled {
            background: var(--bg);
        }
        &:last-child {
            border-bottom: none;
        }
        &:first-child {
            border-radius: calc(var(--br) / 2) calc(var(--br) / 2) 0 0;
        }
        &.visit {
            background: var(--secondary-fade);
        }
        .move-route {
            @media @mobile {
                display: none;
            }
            &>* {
                background: var(--bg);
                border-radius: 50%;
                height: 20px;
                width: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                &:after {
                    content: '>';
                    font-size: 14px;
                    transform: rotate(90deg);
                }
                &.move-route-up {
                    &:after {
                        transform: rotate(-90deg);
                    }
                }
                &:hover {
                    cursor: pointer;
                    background: var(--primary);
                    color: #fff;
                }
            }
        }
        &:nth-child(2) {
            .move-route > *:first-child {
                pointer-events: none;
                opacity: 0.3;
                &:hover {
                    cursor: default;
                    background: var(--bg);
                    color: var(--grey);
                }
            }
        }
        &:nth-last-child(2) {
            .move-route > *:last-child {
                pointer-events: none;
                opacity: 0.3;
                &:hover {
                    cursor: default;
                    background: var(--bg);
                    color: var(--grey);
                }
            }
        }
        .stop-number {
            background: var(--stop-color, var(--primary));
            color: #fff;
            border-radius: 100px;
            padding: 5px 10px;
            text-align: center;
            height: 24px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            text-transform: uppercase;
            font-weight: 600;
            &.start-stop {
                background: var(--green);
                padding: 5px 0px;
                font-size: 10px;
            }
            &.end-stop {
                background: var(--red);
                padding: 5px 0px;
                font-size: 10px;
            }
        }
        &>div {
            &.stop-address {
                flex-grow: 1;
                line-height: 120%;
                .route-address {
                    font-weight: 600;
                    // margin-bottom: 5px;
                }
            }
        }
        .stop-type {
            display: flex;
            align-items: center;
            svg {
                height: 15px;
                path {
                    fill: var(--grey);
                }
            }
        }
        &:last-child {
            // border-bottom: none;
        }
    }
}

// #components-reconnect-modal {
//     display: none !important;
//     opacity: 0 !important;
//     height: 0 !important;
//     width: 0 !important;
// }
//


.choose {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 400px;
    overflow: auto;
    &>div {
        border: 1px solid var(--border);
        padding: 10px;
        gap: 10px;
        display: grid;
        grid-template-columns: 26px 1fr 1fr 1fr 1fr;
        align-items: center;
        &.selected {
            border-color: var(--primary);
            box-shadow: 0 0 2px 0 var(--primary);
            &:hover {
                border-color: var(--primary);
                cursor: default;
            }
        }
        input[type="checkbox"] {
            height: 20px;
            width: 20px;
        }
        &>div {
            text-align: left;
            &:not(:first-child) {
                flex-grow: 1;
            }
            &:nth-child(2) {
                font-weight: 600;
            }
            // &:nth-child(3) {
            //     display: flex;
            //     flex-direction: column;
            //     gap: 5px;
            //     align-items: center;
            //     div {
            //         font-size: 10px;
            //     }
            // }
            &:last-child {
                text-align: right;
                font-size: 10px;
            }
        }
        &:hover {
            border-color: var(--secondary);
            cursor: pointer;
        }
    }
    
}

.pill-choose {
    border: 1px solid var(--border);
    border-radius: 100px;
    padding: 10px;
    background: var(--bg);
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    justify-self: flex-start;
    svg {
        height: 15px;
        path {
            fill: var(--primary);
           
        }
    }
}

.totals-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 500px;
    margin-left: auto;
    &>* {
        padding: 10px;
        &:nth-child(even) {
            text-align: right;
        }
        &.total {
            font-weight: 600;
            font-size: 16px;
            border-top: 2px solid var(--border);
        }
    }
}

.add-line-row {
    display: flex;
    padding: calc(var(--p) / 2) 25px;
    background: var(--bg);
    border-top: 1px solid var(--border);
    // border-radius: 0 0 calc(var(--br) / 1.5) calc(var(--br) / 1.5);
    border-bottom: 1px solid var(--border);
    justify-content: space-between;
    .b {
        padding: 0;
        label {
            transform: translateY(2px);
        }
    }
}

.add-line-row-haulier {
    display: flex;
    padding: calc(var(--p) / 2) 25px;
    background: var(--bg);
    border-top: 1px solid var(--border);
    flex-direction: row !important;
    // border-radius: 0 0 calc(var(--br) / 1.5) calc(var(--br) / 1.5);
    border-bottom: 1px solid var(--border);
    justify-content: space-between;
    align-items: center;
    .b {
        width: 100%;
    }
    .button {
        flex-shrink: 0;
    }
}

.order-dropdown-container {
    border: 1px solid var(--border);
    border-radius: calc(var(--br) / 2);
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    position: relative;
    .button-text {
        white-space: nowrap;
        font-weight: 600;
        text-align: center;
        cursor: pointer;
    }
    .order-dropdown {
        display: none;
        position: absolute;
        top: 30px;
        right: 0px;
        background: var(--white);
        box-shadow: var(--shadow);
        width: 400px;
        padding: 5px;
        z-index: 99;
        &>div:first-child {
            padding: 10px;
            background: var(--bg);
            font-weight: 600;
        }
        table tr:hover {
            background: transparent;
        }
    }
    &:hover {
        .order-dropdown {
            display: flex;
            flex-direction: column;
        }
        // border-color: var(--primary);
        box-shadow: var(--shadow);
    }
}

.delivery-area {
    background: var(--bg);
    border-radius: calc(var(--br) / 4);
    flex-grow: 1;
    padding: 10px;
    border: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // margin-top: 22px;
    gap: 20px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    // height: 235px;
    .delivery-area-card-container {
        display: flex;
        gap: 10px;
        &>div:last-child {
            display: flex;
            flex-direction: column;
            gap: 5px;
            align-items: flex-start;
            justify-content: center;
            .button {
                 padding: 5px 10px;
                 height: 30px;
                 font-size: 14px;
            }
        }
    }
    .delivery-area-card {
        padding: 15px;
        border-radius: var(--br);
        background: var(--white);
        display: flex;
        gap: 10px;
        flex-direction: column;
        border: 1px solid var(--border);
        // width: 200px;
        &>div {
            &:first-child {
                font-size: 16px;
                font-weight: 600;
            }
        }
    }
}

.page-break {
    page-break-after: always;
}

.customer-balance {
    // width: 120px;
    display: grid;
    grid-template-columns: 200px 1fr;

    // flex-direction: column;
    gap: 10px;
    row-gap: 20px;
    // background: var(--bg);
    padding: 0;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border);
    // border-radius: calc(var(--br) / 2);
    // background: var(--green);
    // color: #fff;
    justify-content: space-between;
    &+.grid-flex {
        margin-bottom: 10px;
    }
    &.bad {
        background: var(--red);
    }
    &>div {
        &:nth-last-child(2){
            margin-bottom: 20px;
        }
        &:last-child {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--green);
            &.negative {
                color: var(--red);
            }
        }
    }
}

.latlong {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
    .b {
        display: flex;
        // flex-direction: column;
        gap: 10px;
        align-items: center;
        label {
            color: var(--grey);
        }
        input {
            padding: 0px;
            background: transparent;
            border: none;
            box-shadow: none;
            width: 100px;
            color: var(--grey);
        }
    }
}


.inline-balance {
    color: var(--white);
    padding: 5px 10px;
    border-radius: 50px;
    display: inline-flex;
    font-weight: 600;
    &.positive {
        background: var(--green);
    }
    &.negative {
        background: var(--red);
    }
}

.media-folder-list {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.media-folder {
    border: 1px solid var(--border);
    padding: var(--p);
    border-radius: var(--br);
    cursor: pointer;
    &:hover {
        border-color: var(--primary)
    }
}

.search-address {
    display: flex;
    gap: 10px;
    align-items: center;
    input {
        font-family: 'Poppins', sans-serif;
    }
}

.suggestion-title {
    color: var(--primary);
    font-weight: 600;
}

.suggestions {
    border: 1px solid var(--border);
    // padding: var(--p);
    border-radius: calc(var(--br) / 3);
    max-height: 300px;
    overflow: auto;
    grid-column: span 2;
    &>div {
        border-bottom: 1px solid var(--border);
        padding: 10px;
        &:first-child {
            border-radius: calc(var(--br) / 3) calc(var(--br) / 3) 0 0;
        }
        &:last-child {
            border-bottom: none;
            border-radius: 0 0 calc(var(--br) / 3) calc(var(--br) / 3);
        }
        &:hover {
            background: var(--primary);
            color: var(--white);
            cursor: pointer;
        }
    }
}

.markdown-preview-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
    &>div {
        border: 1px solid var(--border);
        border-radius: calc(var(--br) / 2);
        padding: var(--p);
        h1 {
            font-size: 30px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 26px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        h3 {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        h4 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        h5 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        h6 {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        p {
            line-height: 120%;
            margin-bottom: 20px;
        }
        ul, ol {
            margin-left: 20px;
            margin-bottom: 20px;
            li {
                list-style-type: disc;
                margin-bottom: 5px;
            }
        }
        a {
            color: blue;
        }
    }
}

section.tonnage-report {
    .dates {
        input {
            max-width: 200px;
        }
    }
    .subtitle {
    //     margin-bottom: 5px;
    //     margin-top: 20px;
        border-bottom: none;
    }
    .table {
        // margin-top: 10px;
        min-width: 600px;
        border: 1px solid var(--border);
        td, th {
            border: 1px solid var(--border);
            padding: 10px;
        }
        th {
            font-weight: 600;
            border-bottom: 3px solid var(--border);
            color: var(--black);
            text-align: left;
        }
        td {
            &:first-child {
                background: var(--bg);
                font-weight: 600;
            }
        }
        .totals-row {
            background: var(--bg);
            border-top: 2px solid var(--border);
            font-weight: 600;
        }
    }
}

.block-builder-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    border: 1px solid var(--border);
    border-radius: calc(var(--br) / 2);
    padding: 20px;
    background: #fff;
    .block-builder-header {
        display: flex;
        gap: 10px;
        align-items: flex-end;
        .b {
            flex-grow: 1;
        }
    }
    &>.buttons {
        display: flex;
        justify-content: flex-end;
    }
    .block-builder {
        border: 2px solid var(--primary);
        border-radius: calc(var(--br) / 2);
        padding: 20px;

    }
}

.block-chooser {
    border-radius: var(--br);
    background: var(--bg);
    border: 1px solid var(--border);
    padding: var(--p);
    margin-bottom: var(--p);
}

.basic-list {
    border: 1px solid var(--border);
    border-radius: calc(var(--br) / 2);
    margin-bottom: 20px;
    &>* {
        padding: 10px;
        border-bottom: 1px solid var(--border);
        font-weight: 600;
        &:last-child {
            border-bottom: none;
        }
    }
}

.sample-limit { 
    padding: 20px;
    border-radius: var(--br);
    color: white;
    font-weight: 600;
    &.sample-limit--warning {
        background: #e47a00;
    }
    &.sample-limit--exceeded {
        background: var(--red);
    }
} 

#components-reconnect-modal {

}

.reconnect-modal {
    background-color: rgba(0, 0, 0, 0.75);
    color: white;
    text-align: center;
    padding: 2rem;
    border-radius: 10px;
}

.reconnect-modal h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.reconnect-modal button {
    background-color: #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.inline-error {
    background: var(--red);
    color: white;
    padding: 20px;
    box-sizing: border-box;
    border-radius: var(--br);
    width: 100%;
}

.create-order-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 100px 100px;
    gap: 20px;
    margin-bottom: 20px;
    &>* {
        border: 1px solid var(--border);
        border-radius: var(--br);
        // padding: 20px;
        // height: 250px;
        overflow: auto;
        &:first-child {
            grid-row: 1 / 3;
        }
        &:nth-child(2) {
            grid-row: 1 / 2;
        }
        .customer-balance {
            border-bottom: none;
            padding: 10px;
        }
        .subtitle {
            font-size: 14px;
            padding-left: 10px;
            text-align: center;
            border-bottom: 1px solid var(--border);
            background: var(--bg);
            margin-bottom: 0;
        }
        table.table {
            thead {
                tr {
                    th {
                        padding: 5px;
                        font-size: 10px;
                        &:last-child {
                            text-align: right;
                        }
                    }
                }
            }
            tbody {
                tr {
                    td {
                        padding: 5px;
                        font-size: 12px;
                        &:last-child {
                            text-align: right;
                        }
                    }
                }
            }
        }
    }
    &.create-order-grid--header {
        margin-bottom: 20px;
        &>* {
            height: auto;
            display: flex;
            flex-direction: column;
            &>div:last-child {
                // min-height: 50px;
                padding: 10px;
                display: flex;
                align-items: center;
                height: 100%;
                gap: 5px;
                flex-wrap: wrap;
            }
        }
    }
}

.rounds-table {
    width: 100%;
    tr {
        // &.alternating-day {
        //     background: #ededed;
        // }
        // &.alternating-day2 {
        //     background: var(--primary-fade);
        // }
        background: var(--lightyellow);
        &:nth-last-child(2) {
            td {
                border-bottom: none;;
            }
        }
        &.last-occurrence {
            font-weight: bold;
            background-color: #f0f0f0;
            color: red;
        }
        
        &.old-customer {
            background: white;
        }
        &.will-ring-row {
            background: hsl(214.74deg 82.61% 31.57% / 13%);
            opacity: .8;
            .future-deliveries {
                display: table-cell;
            }
        }
        &.next-booked {
            background: var(--green-fade);
        }
        th {
            padding: 10px;
            border-bottom: 2px solid var(--border);
            text-align: left;
            font-weight: 600;
            font-size: 10px;
        }
        td {
            padding: 10px;
            vertical-align: middle;
            border-bottom: 1px solid var(--border);
            font-size: 12px;
            &.click-customer {
                &:hover {
                    cursor: pointer;
                    text-decoration: underline;
                }
            }
            &:first-child {
                font-weight: 600;
                font-size: 13px;
                line-height: 120%;
            }
            &.will-ring {
                color: var(--red);
            }
            &:nth-child(5){
                font-size: 12px;
            }
            &.booked {
                color: var(--green);
                font-weight: 600;
                &:after {
                    content: 'Booked';
                    color: var(--green);
                    background: rgba(0, 255, 115, 0.15);
                    padding: 5px 7px;
                    border-radius: 3px;
                    margin-left: 5px;
                }
            }
            .button {
                font-size: 10px;
                display: inline-flex;
                // margin-right: 5px;
                padding: 5px 2px 4px 2px;
                white-space: nowrap;
                &.button-inline {
                    padding: 0;
                }
            }
            .rounds-buttons {
                display: flex;
                gap: 10px;
            }
        }
    }
    thead {
        tr {
            background: white;
            &>th:last-child {
                width: 305px;
            }
        }
    }
}

.rounds-filters {
    padding: 20px 0;
    display: flex;
    gap: 10px;
    &+.grid-header {
        border-bottom: none;
        width: 450px;
        .sort-filter {
            padding-left: 0;
        }
        .button {
            svg {
                margin-right: 0;
            }
        }
    }
    input {
        max-width: 200px;
    }
    .rounds-filter {
        border: 2px solid var(--border);
        background: var(--white);
        padding: 10px;
        border-radius: calc(var(--br) / 2);
        display: flex;
        align-items: center;
        cursor: pointer;
        &.selected {
            background: var(--green);
            color: var(--white);
        }
        &:nth-child(n+3) {
            &:nth-child(odd) {
                border-color: var(--primary);
                &.selected {
                    background: var(--primary);
                    color: var(--white);
                }
            }
            &:nth-child(even) {
                border-color: var(--red);
                &.selected {
                    background: var(--red);
                    color: var(--white);
                }
            }
        }
    }
}

.switch {
    border-radius: 100px;
    border: 1px solid var(--border);
    padding: 4px;
    width: 50px;
    height: 26px;
    box-sizing: border-box;
    display: flex;
    cursor: pointer;
    &.small {
        height: 16px;
        width: 30px;
        padding: 2px;
        margin-top: 5px;
        margin-bottom: 5px;
        &:before {
            height: 10px;
            width: 10px;
        }
    }
    &:before {
        content: '';
        height: 16px;
        width: 16px;
        border-radius: 50%;
    }
    &.on {
        justify-content: flex-end;
        &:before {
            background: var(--green);
        }
    }
    &.off {
        justify-content: flex-start;
        &:before {
            background: var(--red);
        }
    }
}

#map {
    height: 100% !important;
    min-height: 600px !important;
}

@media print {
    .sidebar {
        display: none !important;
    }
    main {
        overflow: visible;
        &>.b {
            display: none !important;
        }
        &>header {
            display: none !important;  
        }
    }
    .grid-flex .adv-filters {
        display: none !important;
    }
    .select2-results {
        display: none !important;
    }
    .grid-header {
        display: none !important;
    }
    .grid-main table.table tbody tr td, .grid-main table.table thead tr th {
        &:nth-child(2), &:nth-child(1) {
            display: none !important;
        }
    }
}

.ledger-grid {
    margin-bottom: 20px;
    .grid-flex {
        overflow: hidden;
    }
    .grid-header {
        margin-bottom: 0;
        border-radius: var(--br) var(--br) 0 0;
    }
    table.table tbody tr td, table.table thead tr th {
        padding: 5px;
        &:nth-child(4), &:nth-child(5), &:nth-child(6), &:nth-child(7) {
            text-align: right;
        }
        &:nth-child(3) {
            font-size: 12px;
        }
    }
    table.table tbody tr:hover {
        cursor: default;
    }
    table.table tbody tr:first-child {
        font-weight: 600;
    }
    td {
        &.ledger-order {
            background-color: var(--primary-fade);
        }
        &.ledger-payment {
            background-color: var(--secondary-fade);
        }
        &.ledger-future {
            background-color: var(--green-fade);
        }
    }
    .ledger-order-line {
        display: grid;
        align-items: center;
        grid-template-columns: 1fr 75px 100px;
        &>div:last-child {
            text-align: right;
        }
    }
}

.balance-ledger-container {
    display: grid;
    gap: 20px;
    grid-template-columns: 300px 1fr;
    align-items: flex-start;
}

.customer-details-container {
    display: grid;
    gap: 20px;
    grid-template-columns: 1fr 1fr;
}

strong {
    &.inactive-status {
        color: #ccc;
        text-decoration: line-through;
    }
    &.deleted-status {
        color: var(--red);
        text-decoration: line-through;
    }
}

.route-order {
    display: grid;
    grid-template-columns: 1fr 50px;
    margin-right: 10px;
    gap: 5px;
    .route-order-line {
        display: grid;
        grid-column: 1 / -1;
        align-items: center;
        grid-template-columns: subgrid;
        &>div:last-child {
            text-align: right;
            &:before {
                content: '\2715 ';
                margin-right: 5px;
                font-size: 8px;
                color: var(--grey);
            }
        }
    }
}

.future-deliveries {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    width: 305px;
    &>div {
        border-left: 1px solid var(--border);
        padding: 5px;
        display: flex; 
        flex-direction: column;
        gap: 5px;
        align-items: center;
        width: 50px;
        &.span-columns {
            width: 100%;
            color: var(--grey);
            grid-column: 1 / -1;
        }
        &.future-sorted {
            .button {
                background: transparent !important;
                color: black;
            }
        }
        .button:nth-child(2) {
            padding: 5px 2px 4px 2px;
        }
    }
    .button {
        padding: 3px;
        font-size: 11px;
        width: 100%;
        box-sizing: border-box;
        &.button-add {
            // background: var(--green);
            // padding: 5px 3px;
        }
        &.button-miss {
            background: var(--secondary);
            // background: white;
            // color: var(--secondary);
            // border: 1px solid var(--secondary)
        }
        &.button-change {
            background: var(--bg);
            color: black;
        }
    }
}

.booked {
    background: var(--green-fade);
    color: var(--green);
    display: flex;
    width: 100%;
    padding: 4px;
    gap: 5px;
    border-radius: calc(var(--br) / 2);
    justify-content: center;
    box-sizing: border-box;
    &.booked-differently {
        background: var(--purple-fade);
        svg {
            path {
                fill: var(--purple);
            }
        }
    }
    svg {
        height: 14px;
        width: 14px;
        path {
            fill: var(--green);
        }
    }
}

.missed {
    background: var(--secondary-fade);
    color: var(--secondary);
    display: flex;
    padding: 4px;
    box-sizing: border-box;
    width: 100%;
    gap: 5px;
    font-size: 14px;
    font-weight: 600;
    border-radius: calc(var(--br) / 2);
    justify-content: center;
}

.endofday {
    border-bottom: 4px dashed var(--grey);
    &:last-child {
        display: none;
    }
}

.subtle-link {
    font-weight: 600;
    &.future-date {
        font-size: 12px;
        display: inline-flex;
        gap: 5px;
    }
    &:hover {
        cursor: pointer;
        text-decoration: underline;
    }
}

.ledger-date {
    display: inline-flex;
    gap: 5px;
}

// .address-view {
//     font-weight: 600;
//     line-height: 120%;
// }

.view-order-container {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 20px;
    margin-bottom: 20px;
    &>* {
        gap: 20px;        
    }
    @media only screen and (max-width: 1570px) {
        grid-template-columns: 1fr;
    }
    section.bs.np {
        margin-bottom: 0;
    }
}

.inline-header-info {
    font-size: 12px;
    font-weight: 400;
    color: var(--grey);
    display: flex; 
    gap: 10px;
    flex-grow: 1;
    justify-content: flex-end;
    .b {
        display: flex;
        align-items: center;
        input {
            height: 32px;
        }
    }
}

.tag-container {
    margin: 20px 0;
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.display-inline {
    line-height: 120%;
    font-weight: 600;
}

.edit-note-container {
    display: grid;
    align-items: center;
    gap: 10px;
    position: relative;
    // margin-bottom: 20px;
    // grid-template-columns: 1fr 80px;
    .button {
        bottom: 6px;
        right: 6px;
        margin-top: 10px;
        position:absolute;
        z-index: 2;
        border-color: #776204;
        color: #776204;
        font-size: 14px;
        padding: 2px 8px;
        height: 30px;
        &:hover {
            border-color: #776204;
            background: hsla(49, 93%, 24%, 10%);
            color: #776204;
        }
        &.save {
            background: #776204;
            color: white;
            &:hover {
                border-color: #4e4003;
            }
        }
    }
}

.customer-results-container {
    border: 1px solid var(--border);
    border-radius: var(--br);
    grid-template-columns: 100px 1fr 250px 1fr 300px;
    background: var(--white);
    display: grid;
    position: absolute;
    z-index: 9;
    top: 70px;
    &.haulier-results {
        grid-template-columns: 100px 1fr 1fr;
        min-width: 1000px;
    }
    .customer-result-grid {
        border-bottom: 1px solid var(--border);
        display: grid;
        grid-column: 1 / -1;
        grid-template-columns: subgrid;
        align-items: center;
        gap: 10px;
        &:last-child {
            border-bottom: none;
        }
        &:hover {
            color: var(--primary);
            cursor: pointer;
            background: var(--primary-fade);
        }
        &>div {
            padding: 10px;
            &:nth-child(2) {
                font-weight: 600;
            }
            &:last-child {
                display: flex;
                gap: 5px;
            }
        }
    }
    
}

.create-order-title {
    display: flex;
    gap: 20px;
    padding: 10px;
    align-items: center;
    &>.title {
        flex-grow: 1;
    }
}

.blue-route {
    // width: 10px;
    // background: var(--primary);
    // height: 10px;
    // border-radius: 50%;
    // display: inline-flex;
    // margin-left: 5px;
    // margin-right: 5px;
    svg {
        width: 10px;
        height: 10px;
        path {
            fill: var(--primary);
        }
    }
}
.red-route {
    // width: 10px;
    // display: inline-flex;
    // background: var(--red);
    // height: 10px;
    // border-radius: 50%;
    // margin-left: 5px;
    // margin-right: 5px;
    svg {
        width: 10px;
        height: 10px;
        path {
            fill: var(--red);
        }
    }
}

.dual-inputs {
    display: flex;
}

.info {
    background: var(--lightyellow);
    padding: 20px;
    border-radius: var(--br);
}

.product-out {
    background: #333;
    color: white;
    font-size: 10px;
    padding: 5px 10px;
    border-radius: 50px;
    display: inline-flex;
    margin-left: 5px;
    gap: 5px;
    svg {
        height: 10px;
        width: 10px;
        path {
            fill: #fff;
        }
    }
}

.kebab-menu {
    border: 1px solid var(--border);
    border-radius: calc(var(--br) / 2);
    // padding: 5px;
    position: relative;
    cursor: pointer;
    height: 21px;
    width: 21px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
        background: var(--bg);
        z-index: 999;
    }
    .kebab {
        transform: translateY(-5px) translateX(-0.5px);
        position: relative;
        height: 3px;
        width: 3px;
        background: var(--primary);
        border-radius: 50%;
        &:before {
            content: '';
            position: absolute;
            height: 3px;
            width: 3px;
            background: var(--primary);
            border-radius: 50%;
            top: 5px;
        }
        &:after {
            content: '';
            position: absolute;
            height: 3px;
            width: 3px;
            background: var(--primary);
            border-radius: 50%;
            top: 10px;
        }
    }
    ul {
        display: none;
        position: absolute;
        top: 20px;
        right: 0px;
        border: 1px solid var(--border);
        box-shadow: var(--shadow);
        width: 140px;
        background: white;
        border-radius: var(--br);
    }
    &:hover {
        ul {
            display: flex;
            flex-direction: column;
            li {
                padding: 10px;
                border-bottom: 1px solid var(--border);
                &:last-child {
                    border-bottom: none;
                    border-radius: 0 0 var(--br) var(--br);
                }
                &:first-child {
                    border-radius: var(--br) var(--br) 0 0;
                }
                &:hover {
                    background: var(--bg);
                }
            }
        }
    }
}

.generate-buttons {
    width: 100%;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-sizing: border-box;
    margin-bottom: 10px;
    &>* {
        &.button.tertiary {
            margin-left: auto;
        }
    }
}

.empty-invoice-container {
    color: var(--grey);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    gap: 30px;
    font-weight: 600;
    // font-size: 20px;
    svg {
        height: 40px;
        width: 40px;
        path {
            fill: var(--grey);
        }
    }
}

.order-summary-lines {
    border: 1px solid var(--border);
    border-radius: calc(var(--br) / 2);
    &>* {
        padding: 5px;
        border-bottom: 1px solid var(--border);
        font-weight: 600;
        // display: grid;
        // grid-template-columns: 1fr 30px 30px;
        &:last-child {
            border-bottom: none;
        }
    }
}

.round-info {
    border: 1px solid var(--border);
    border-radius: calc(var(--br) / 2);
    &>* {
        padding: 5px;
        border-bottom: 1px solid var(--border);
        display: flex;
        justify-content: space-between;
        &:last-child {
            border-bottom: none;
        }
    }
}

.button.button-inline {
    padding: 0px;
    background: transparent;
    // font-size: 12px;
    border: none;
    color: var(--primary);
    box-shadow: none;
    &:hover {
        background: white;
        color: var(--secondary);
    }
}

.waste-product-display-column {
    grid-column: 2 / 4;
}

.mobile-add-buttons {
    display: none;
    @media @mobile {
        display: flex;
        gap: 5px;
        padding: 5px 0;
        justify-content: flex-end;
        .button-full {
            padding: 5px 10px;
            text-align: center;
            height: 40px;
        }
    }
}

.waste-colours {
    .dash-rows .dash-row .count {
        background: var(--brown);
    }
    .chart-container > * {
        background: var(--brown-secondary);
    }
    .ancillary-info {
        color: var(--brown-secondary);
    }
    .stat-row .stat-icon svg path {
        fill: var(--brown);
    }
    .stat > div:first-child {
        color: var(--brown);
    }
}

.order-colours {
    .dash-rows .dash-row .count {
        background: var(--grey);
    }
    .chart-container > * {
        background: var(--grey-secondary);
    }
    .ancillary-info {
        color: var(--grey-secondary);
    }
    .stat-row .stat-icon svg path {
        fill: var(--grey);
    }
    .stat > div:first-child {
        color: var(--grey);
    }
}

.route-colours {
    .dash-rows .dash-row .count {
        background: var(--blue);
    }
    .chart-container > * {
        background: var(--blue-secondary);
    }
    .ancillary-info {
        color: var(--blue-secondary);
    }
    .stat-row .stat-icon svg path {
        fill: var(--blue);
    }
    .stat > div:first-child {
        color: var(--blue);
    }
}

.stat-row {
    background: var(--bg2);
}

.top-products-dash {
    display: grid;
    gap: 10px;
    padding: 10px;
    grid-template-columns: 1fr 1fr; 
    border-bottom: 1px solid var(--border);
    background: var(--bg2);
    .dash-row {
        border: 1px solid var(--border);
        border-radius: var(--br);
        background: var(--white);
        &:last-child {
            border-bottom: 1px solid var(--border) !important;
        }
    }
}

//temp fix for dialog on haulier driver screen

section.bs.np + .dialog-overlay {
    position: fixed;
}

.night-log {
    display: flex;
    flex-direction: column;
    gap: 10px;
    .night-button {
        border-radius: var(--br);
        padding: 50px;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        font-weight: 600;
        gap: 10px;
        flex-direction: column;
        svg {
            path {
                fill: white;
            }
        }
        &.nb-in {
            background: var(--primary);
            color: var(--white);
        }
        &.nb-out {
            background: var(--secondary);
            color: var(--white);
        }
    }
}

.night-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    .night-block {
        display: flex;
        flex-direction: column;
        gap: 10px;
        label {
            font-weight: 600;
        }
        input, select {
            height: 50px;
            font-size: 30px;
            &[readonly] {
                // background: var(--bg);
            }
            &:not([readonly]) {
                border: 2px solid var(--primary);
            }
        }
        select {
            font-size: 14px;
        }
    }
}

.notifications-area {
    display: flex;
    gap: 10px;
    padding-bottom: 20px;
    .notification {
        border-radius: var(--br);
        background: hsla(161, 76%, 13%, 0.11);
        color: var(--primary);
        padding: 10px 10px 10px 20px;
        display: flex; 
        align-items: center;
        gap: 10px;
        position: relative;
        cursor: pointer;
        .notification-icon {
            svg {
                width: 14px;
                height: 14px;
                path {
                    fill: var(--primary);
                
                }
            }
        }

        .notifications-list {
            display: none;
            width: 500px;
            border: 1px solid var(--border);
            border-radius: var(--br);
            background: var(--white);
            box-shadow: 0 0 5px 5px rgba(0,0,0,.05);
            &>div {
                display: flex;
                padding: 14px;
                justify-content: space-between;
                gap: 5px;
                border-bottom: 1px solid var(--border);
                &:last-child {
                    border-bottom: none;
                }
            }
        }
        &:hover {
            .notifications-list {
                display: flex;
                flex-direction: column;
                gap: 5px;
                position: absolute;
                top: 30px;
                right: 0px;
            }
        }
        .notification-test {
            // font-weight: 600;
            
        }
        .dismiss {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            // background: var(--primary-fade);
            align-items: center;
            display: inline-flex;
            justify-content: center;
            
            svg {
                height: 15px;
                width: 15px;
                path {
                    fill: var(--primary);
                }
            }
            &:hover {
                cursor: pointer;
                svg {
                    path {
                        fill: var(--red);
                    }
                }
            }
        }
        &.notification-danger {
            background: var(--red-fade);
            color: var(--red);
            svg {
                path {
                    fill: var(--red);
                }
            }
            .dismiss {
                // background: var(--red-fade);
                svg {
                    path {
                        fill: var(--red);
                    }
                }
            }
        }
    }
}

.signature-area {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
    justify-content: center;
    touch-action: none; /* Prevents default touch actions */
    position: relative;
    header {
        width: 300px;
    }
    canvas {
        // width: calc(100vw - 40px);
        // height: 200px;
        border-radius: var(--br);
        margin-bottom: 10px;
        border: 2px solid var(--border);
        width: 100%;
        max-width: 420px;
        height: 200px;
        touch-action: none; /* Prevents default touch actions */
    }
    &>div {
        display: flex;
        flex-direction: column;
        gap: 15px;
        width: 300px;
        align-items: center;
        justify-content: center;
        .button {
            width: 100%;
            &.tertiary {
                width: 80px;
            }
        }
    }
}

.show-tonnage {
    margin-top: 5px;
    border: 1px solid var(--border);
    padding: 5px;
    background: var(--bg);
    border-radius: calc(var(--br) / 2);
    &>div {
        display: grid;
        grid-template-columns: 1fr 1fr;
        strong {
            // display: inline-flex;
            padding-right: 5px;
            text-align: right;
        }
    }
}

.proof-of-delivery-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 0 10px;
    background: var(--secondary);
    height: 52px;
    svg path {
        fill: var(--primary);
    }   
}

.contract-job-add {
    display: flex;
    gap: 10px;
    flex-direction: row !important;
    align-items: flex-end;

    .b {
        flex-grow: 1;
    }
    .button {
        margin-bottom: 3px;
    }
    .or {
        color: var(--grey);
        padding-bottom: 15px;
    }
}

.eticket-upload {
    // width: 140px;
    .upload-wrap {
        display: flex;
        gap: 10px;
        align-items: center;
        input[type="file"] {
            height: 40px;
            box-sizing: border-box;
        }
        .upload-text {
            height: 40px;
        }
        .button {
            padding: 5px 10px;
        }
    }
}

.uploaded-docs-container {
    display: flex;
    gap: 20px;
    flex-direction: row !important;
    flex-wrap: wrap;
    .doc-container {
        display: flex;
        gap: 0px;
        position: relative;
        border: 1px solid var(--border);
        border-radius: calc(var(--br) / 2);
        align-items: flex-end;
        background: var(--bg);
        .doc-info {
            font-size: 10px;
            color: var(--grey);
            padding: 10px;
            text-align: right;
        }
    }
    a {
        border-right: 1px solid var(--border);
        height: 66px;
        width: 51px;
        border-radius: var(--br) 0 0 var(--br);
        background-size: cover;
    }
    .remove {
        // border-radius: 50%;
        height: 15px;
        width: 15px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        // background: var(--primary-fade);
        position: absolute;
        top: 5px;
        right: 5px;
        &:hover {
            &>svg {
                path {
                    fill: var(--red);
                }
            }
        }
        &>svg {
            height: 14px;
            width: 14px;
            path {
                fill: var(--primary);
            }
        }
    }
}

.has-attachment {
    
    svg {
        height: 20px;
        width: 18px;
        path {
            fill: var(--secondary);
        }
    }
}

.subsubtitle {
    background: rgba(8, 56, 40, .05);
    font-weight: 600;
    color: var(--primary);
    padding: 8px 20px;
}

.nowrap {
    white-space: nowrap;
}

.time-in-list {
    line-height: 160%;
    font-size: 12px;
    width: 85px;
}

.will-ring-notes-container {
    display: flex;
    gap: 5px;
    align-items: center;
    margin-top: 5px;
    .b {
        flex-grow: 1;
        textarea, input {
            flex-grow: 1;
            font-size: 10px;
            height: 30px;
            padding: 5px;
            max-width: 300px;
            margin-bottom: 0 !important;
        }
        label {
            display: none;
        }
    }
}

