@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiEyp8kv8JHgFVrFJA.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLGT9V1s.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLEj6V1s.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLCz7V1s.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLDD4V1s.ttf) format('truetype');
}
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v23/pxiByp8kv8JHgFVrLBT5V1s.ttf) format('truetype');
}
@font-face {
  font-family: 'UKNumberPlate';
  font-style: normal;
  font-weight: 400;
  src: local('UKNumberPlate'), url('https://fonts.cdnfonts.com/s/4916/UKNumberPlate.woff') format('woff');
}
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
body {
  line-height: 1;
}
ol,
ul {
  list-style: none;
}
blockquote,
q {
  quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
:root {
  --primary: #272534;
  --secondary: #FFE500;
  --highlight: #f8f8f8;
  --highlight2: #eee;
  --bg: #FAFAFA;
  --bg2: #f1f1f1;
  --yellow: #FFE500;
  --yellow2: #fff066;
  --lightyellow: #fffef0;
  --white: #ffffff;
  --black: #111111;
  --grey: #8d97a4;
  --orange: #e18700;
  --brown: #521a00;
  --brown-secondary: #922e00;
  --grey: #41454b;
  --grey-secondary: #7f8691;
  --blue: #052a63;
  --blue-secondary: #0042ac;
  --disabled: #dddee6;
  --green: #07bf50;
  --red: #db1731;
  --green-fade: rgba(7, 191, 80, 0.1);
  --red-fade: rgba(219, 23, 49, 0.1);
  --purple: #6f42c1;
  --purple-fade: rgba(111, 66, 193, 0.1);
  --primary-fade: hsla(214.74, 82.61%, 31.57%, 0.17);
  --secondary-fade: hsla(23.36, 93.39%, 47.45%, 0.18);
  --border: #d5d7e9;
  --shadow: 0px 4px 20px rgba(205, 212, 220, 0.5);
  --shadow2: 0px 4px 10px rgba(0, 0, 0, 0.05), inset 0px 0px 5px rgba(0, 0, 0, 0.05);
  --shadowhighlight: 0 2px 1px 0px var(--highlight), inset 0 -1px 1px 0px var(--highlight);
  --br: 14px;
  --p: 20px;
  --width: 1200px;
  --transition: 0.2s all ease-out;
}
.mb {
  margin-bottom: var(--p);
}
html .o--Trigger--trigger .Open_svg__w-fill-dark,
html .o--Trigger--trigger .Close_svg__w-fill-dark {
  fill: var(--primary);
}
html .o--Trigger--trigger:hover .Open_svg__w-fill-dark,
html .o--Trigger--trigger:hover .Close_svg__w-fill-dark {
  fill: var(--secondary);
}
html .o--Chat--chat .o--Chat--header,
.o--Widget--widget .o--ChatMessage--chatMessage.o--ChatMessage--in .o--ChatMessage--body .o--ChatMessage--content {
  background: var(--primary);
}
.o--Widget--widget .o--ChatMessage--chatMessage.o--ChatMessage--displayMode-light.o--ChatMessage--out .o--ChatMessage--avatar *,
.o--Widget--widget .o--ChatMessage--chatMessage.o--ChatMessage--displayMode-light.o--ChatMessage--out .o--ChatMessage--body .o--ChatMessage--content *,
.o--Widget--widget .o--ChatMessage--chatMessage.o--ChatMessage--displayMode-auto.o--ChatMessage--out .o--ChatMessage--avatar *,
.o--Widget--widget .o--ChatMessage--chatMessage.o--ChatMessage--displayMode-auto.o--ChatMessage--out .o--ChatMessage--body .o--ChatMessage--content * {
  color: var(--primary);
}
.o--Widget--widget a,
.o--Widget--widget a *,
.o--Widget--widget a:active {
  color: var(--secondary);
}
.force-right {
  margin-left: auto;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}
html {
  font-size: 14px;
}
.dif {
  display: inline-flex;
}
.dif.button {
  display: inline-flex;
}
aside {
  background: var(--yellow);
  border-radius: var(--br);
  padding: var(--p);
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
  display: none;
}
section {
  padding: calc(var(--p) * 1);
  border-radius: calc(var(--br) / 2);
  margin-bottom: var(--p);
}
section.add-haulier-stop {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr 1fr 1fr;
}
section.add-haulier-stop section.bs {
  margin-bottom: 0;
}
section.add-haulier-stop .select2 {
  max-width: 322px;
}
section:last-child {
  margin-bottom: 0;
}
section.npb {
  padding-bottom: 0;
}
section.npt {
  padding-top: 0;
}
@media only screen and (max-width: 480px) {
  section {
    padding: calc(var(--p) / 2);
    overflow: auto;
  }
}
section > .g:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
}
section.eticket-site {
  display: flex;
  gap: 20px;
  justify-content: space-between;
  padding: 0px;
}
@media only screen and (max-width: 480px) {
  section.eticket-site {
    flex-direction: column;
  }
}
section.eticket-site > section.bs {
  flex-grow: 1;
  margin-bottom: 0;
}
section.eticket-site > .g {
  margin-bottom: 0;
}
section.eticket-site > .g:last-child {
  background: var(--white);
  padding: var(--p);
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  gap: 20px;
  grid-template-columns: 120px 0.5fr 0.5fr;
  min-width: 330px;
  min-height: 320px;
}
section.eticket-site > .g:last-child .b.entryexittime {
  grid-column: 2 / 4;
}
section.eticket-site > .g:last-child .b.check.force-left {
  justify-content: flex-start;
}
section.eticket-site > .g:last-child .surcharge-dropdown,
section.eticket-site > .g:last-child .product-select {
  grid-column: 1 / -1;
}
@media only screen and (max-width: 480px) {
  section.eticket-site > .g:last-child {
    grid-template-columns: 1fr;
  }
}
section.eticket-site > .g:last-child input[type="number"] {
  font-size: 18px;
  font-weight: 600;
}
section.comment-section textarea {
  width: 100%;
  height: 300px;
}
section.comment-section.small textarea {
  height: 100px;
}
section.bs {
  border: 1px solid var(--border);
  background: #fff;
}
section.bs .subtitle {
  font-size: 14px;
  min-height: auto;
  border-radius: calc(var(--br) / 2);
  padding-bottom: 10px;
  width: auto;
  display: inline-flex;
  color: var(--grey);
  width: 100%;
  display: flex;
  justify-content: space-between;
}
section.bs .subtitle > div {
  align-items: center;
  display: inline-flex;
  gap: 10px;
}
section.bs .subtitle > div:last-child {
  justify-content: flex-end;
}
section.bs .subtitle svg {
  height: 15px;
  width: 15px;
  margin-right: 5px;
}
section.bs .subtitle svg path {
  fill: var(--grey);
}
section.bs .subtitle .button svg path {
  fill: var(--white);
}
section.bs .subtitle.route-subtitle {
  justify-content: flex-start;
  gap: 10px;
}
section.bs .subtitle.route-subtitle div:last-child {
  justify-content: flex-start;
}
section.bs .subtitle.route-subtitle .optimisation-status {
  margin-left: 0px;
  color: var(--grey);
  font-weight: 500;
  font-size: 12px;
}
@media only screen and (max-width: 480px) {
  section.bs .subtitle.route-subtitle .optimisation-status {
    display: none;
  }
  section.bs .subtitle.route-subtitle .optimisation-status + div {
    display: none;
  }
}
section.bs .subtitle.route-subtitle .button:last-child {
  margin-left: auto;
}
section.np {
  padding: 5px;
}
section.np > section {
  margin-bottom: 0;
}
section.np .subtitle {
  color: var(--primary);
  font-size: 16px;
  border-bottom: 1px solid var(--border);
  box-sizing: border-box;
  padding: calc(var(--p) / 3);
  border-radius: calc(var(--br) / 2.3) calc(var(--br) / 2.3) 0 0;
  margin-bottom: 0;
}
section.np .subtitle.editing {
  background: var(--secondary);
  color: var(--white);
}
section.np .subtitle.editing .button {
  color: var(--white);
  border-color: var(--white);
  box-shadow: none;
}
section.np .subtitle > div:first-child {
  padding: calc(var(--p) / 2);
}
section.np .subtitle .button.small {
  height: 34px;
  padding: calc(var(--p) / 2) calc(var(--p) / 2);
}
section.p0 {
  padding: 0;
}
.pa {
  padding: 10px;
}
a.forgot {
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  margin-left: auto;
  text-decoration: underline;
  color: #999;
}
.remove {
  cursor: pointer;
}
.remove svg path {
  fill: var(--grey);
}
.remove:hover svg path {
  fill: var(--red);
}
.weighbridge-photos {
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-left: auto;
  gap: 20px;
}
@media only screen and (max-width: 480px) {
  .weighbridge-photos {
    flex-direction: column;
  }
}
.weighbridge-photos > .site-name {
  grid-column: 1 / -1;
}
.weighbridge-photos > div:not(.b) {
  border: 1px solid var(--border);
  border-radius: var(--br);
  display: flex;
  flex-direction: column;
  padding: 5px;
  background: var(--bg);
}
.weighbridge-photos > div:not(.b) > div {
  padding: 5px 5px 7px 5px;
  font-weight: 600;
}
.weighbridge-photos > div:not(.b) img {
  height: 100%;
  width: 350px;
  max-width: 100%;
  object-fit: cover;
  border-radius: calc(var(--br) / 1.5);
  display: flex;
}
header {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--p);
  height: 50px;
  align-items: center;
}
@media only screen and (max-width: 480px) {
  header {
    height: auto;
    flex-wrap: wrap;
    gap: 10px;
  }
}
header .button {
  padding: calc(var(--p) / 1.2);
}
header .button > svg {
  margin-right: 10px;
  height: 13px;
  width: 20px;
  transform: scale(1.5);
}
header .button > svg path {
  fill: var(--white);
}
header .button.tertiary > svg path {
  fill: var(--primary);
}
header > div {
  display: flex;
  align-items: center;
}
header > div:last-child {
  width: max-content;
  gap: 10px;
}
@media only screen and (max-width: 480px) {
  header > div {
    flex-wrap: wrap;
  }
}
@media only screen and (max-width: 480px) {
  header > div .button {
    white-space: nowrap;
  }
}
header .title {
  align-items: center;
  color: var(--primary);
}
header .title > svg {
  margin-right: calc(var(--p) / 2);
  height: 26px;
  width: 26px;
}
header .title > svg path {
  fill: var(--primary);
}
header .title > a {
  font-weight: 400;
  margin-right: calc(var(--p) * 1.5);
  position: relative;
}
header .title > a:after {
  content: '>';
  position: absolute;
  right: -20px;
  top: 0px;
  color: var(--border);
}
header .title > a:hover {
  color: var(--purple);
  text-decoration: underline;
}
.posrel {
  position: relative;
}
.subtitle {
  font-weight: 600;
  font-size: 1.2rem;
  margin-bottom: var(--p);
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 40px;
}
.subtitle + .grid-flex {
  border-radius: calc(var(--br) / 2);
}
.subtitle .buttons {
  display: flex;
  align-items: center;
  gap: 20px;
}
.subtitle .buttons-container {
  padding: 0;
}
h1:focus {
  outline: none;
}
a {
  color: #0071c1;
}
p {
  line-height: 120%;
  margin-bottom: var(--p);
}
.button {
  background: var(--primary);
  color: var(--white);
  padding: calc(var(--p) / 1.5) var(--p);
  border-radius: calc(var(--br) / 2);
  border: none;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.05), 0 1px 0 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
@media only screen and (max-width: 480px) {
  .button {
    font-size: 0.9rem;
  }
}
.button.inline-button {
  display: inline-flex;
}
.button.drop-button {
  position: relative;
  padding-right: 30px;
}
.button.drop-button:after {
  content: '';
  position: absolute;
  right: 15px;
  top: 19px;
  height: 6px;
  width: 6px;
  border-bottom: 1px solid var(--primary);
  border-right: 1px solid var(--primary);
  transform: rotate(45deg);
}
.button.drop-button.small {
  padding-right: 30px;
}
.button.drop-button.small:after {
  top: 13px;
}
.button.drop-button.small:hover .drop-button-dropdown {
  top: 36px;
}
.button.drop-button .drop-button-dropdown {
  display: none;
}
.button.drop-button:hover {
  cursor: pointer;
}
.button.drop-button:hover .drop-button-dropdown {
  display: block;
  top: 45px;
  right: 0px;
  position: absolute;
  width: 200px;
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  background: #fff;
}
.button.drop-button:hover .drop-button-dropdown ul li {
  padding: 10px;
  border-bottom: 1px solid var(--border);
}
.button.drop-button:hover .drop-button-dropdown ul li:last-child {
  border-bottom: none;
}
.button.drop-button:hover .drop-button-dropdown ul li:hover {
  background: var(--bg);
  cursor: pointer;
}
.button.drop {
  position: relative;
}
.button.drop ul {
  top: 35px;
  left: 0px;
  display: none;
  position: absolute;
  width: 200px;
  background: var(--white);
  border-radius: var(--br);
  box-shadow: var(--shadow2);
  border: 1px solid var(--border);
}
.button.drop ul li {
  border-bottom: 1px solid var(--border);
}
.button.drop ul li:last-child {
  border-bottom: none;
}
.button.drop ul li > div {
  padding: calc(var(--p) / 2);
}
.button.drop:hover ul {
  display: block;
}
.button.drop:hover ul li div {
  color: var(--primary);
}
.button.drop:hover ul li div:hover {
  color: var(--secondary);
}
.button:hover {
  box-shadow: inset 100px 100px 0 0 rgba(0, 0, 0, 0.1);
}
.button.secondary-solid {
  background: #0f5ac1;
}
.button.secondary-solid:hover {
  background: #0f5ac1;
  box-shadow: inset 100px 100px 0 0 rgba(0, 0, 0, 0.1);
}
.button.secondary {
  color: var(--primary);
  border: 2px solid var(--primary);
  background: transparent;
}
.button.secondary:hover {
  box-shadow: inset 0 0 2px 0 var(--primary);
}
.button.tertiary {
  color: var(--primary);
  background: transparent;
}
.button.tertiary:hover {
  background: var(--highlight);
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.05), 0 1px 0 0 rgba(0, 0, 0, 0.4);
}
.button.inline {
  color: var(--primary);
  background: transparent;
  box-shadow: none;
}
.button.inline:hover {
  background: var(--highlight);
}
.button.small {
  padding: calc(var(--p) / 2) calc(var(--p) / 1.5);
  display: inline-flex;
  font-size: 14px;
  height: 40px;
}
.button.tiny {
  padding: calc(var(--p) / 2) calc(var(--p) / 2);
  display: inline-flex;
  font-size: 12px;
  height: 23px;
}
.button > svg {
  margin-right: 10px;
}
.button > svg path {
  fill: var(--white);
}
.button.secondary > svg path {
  fill: var(--primary);
}
.button.warning {
  background: var(--orange);
  color: white;
}
.button.delete {
  background: var(--red-fade);
  color: var(--red);
  font-size: 12px;
  padding: 10px;
  height: 30px;
}
.button.delete-button {
  background: var(--red);
}
.button.secondary-button {
  background: var(--secondary);
  color: white;
}
.button.secondary-button:hover {
  background: orange;
}
.button > svg {
  width: 15px;
  height: 15px;
}
.button.save {
  background: var(--green);
}
.content {
  padding-top: 1.1rem;
}
.valid.modified:not([type=checkbox]) {
  outline: 1px solid #26b050;
}
.invalid {
  outline: 1px solid red;
}
.validation-message {
  color: red;
}
#blazor-error-ui {
  background: lightyellow;
  bottom: 0;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
  display: none;
  left: 0;
  padding: 0.6rem 1.25rem 0.7rem 1.25rem;
  position: fixed;
  z-index: 1000;
}
#blazor-error-ui .dismiss {
  cursor: pointer;
  position: absolute;
  right: 0.75rem;
  top: 0.5rem;
}
.blazor-error-boundary {
  background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
  padding: 1rem 1rem 1rem 3.7rem;
  color: white;
}
.blazor-error-boundary::after {
  content: "An error has occurred.";
}
body {
  font-family: 'Poppins', Helvetica, Arial, sans-serif;
}
body.login-body {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  background: var(--bg);
}
a {
  text-decoration: none;
  color: var(--black);
}
a.active {
  color: var(--primary);
}
.page {
  display: flex;
  height: 100vh;
  box-sizing: border-box;
}
@media only screen and (max-width: 480px) {
  .page {
    flex-direction: column;
    padding: 0px;
    height: auto;
  }
}
.sidebar {
  background: var(--bg);
  padding: calc(var(--p) * 2) var(--p) var(--p);
  width: 230px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  flex-shrink: 0;
  flex-grow: 0;
  height: 100vh;
  box-sizing: border-box;
  overflow: auto;
}
@media only screen and (max-width: 480px) {
  .sidebar {
    height: 80px;
    box-sizing: border-box;
    padding: var(--p);
    position: sticky;
    top: 0px;
    left: 0px;
    border-radius: 0px;
    width: 100%;
    justify-content: space-between;
    display: flex;
    flex-direction: row;
    z-index: 99;
    overflow: visible;
  }
}
.sidebar .logo {
  width: 170px;
}
@media only screen and (max-width: 480px) {
  .sidebar .logo {
    height: 70px;
    object-fit: contain;
    object-position: left;
  }
}
.sidebar nav {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex-grow: 1;
}
.sidebar nav.desktop-menu {
  display: block;
}
@media only screen and (max-width: 480px) {
  .sidebar nav.desktop-menu {
    display: none;
  }
}
.sidebar nav ul {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  height: 100%;
}
.sidebar nav ul li {
  margin-bottom: 1px;
}
.sidebar nav ul li.nav-header {
  font-size: 10px;
  color: var(--grey);
  margin-top: 10px;
  border-radius: var(--br);
  padding: 5px 10px;
  margin-bottom: 4px;
}
.sidebar nav ul li a {
  padding: 7px 12px;
  display: flex;
  font-weight: 500;
  font-size: 0.9rem;
  align-items: center;
  border-radius: calc(var(--br) / 2);
  transition: var(--transition);
}
.sidebar nav ul li a svg {
  height: 20px;
  width: 20px;
  margin-right: 10px;
  transform: translateY(0px);
}
.sidebar nav ul li a svg path {
  fill: var(--grey);
}
.sidebar nav ul li a.active {
  background: var(--highlight2);
  font-weight: 600;
}
.sidebar nav ul li a.active:hover {
  background: var(--highlight2);
}
.sidebar nav ul li a.active svg path {
  fill: var(--primary);
}
.sidebar nav ul li a:hover {
  background: var(--highlight2);
}
.sidebar nav ul li a:hover svg path {
  fill: var(--primary);
}
.sidebar nav.mobile-menu-container {
  position: absolute;
  top: 65px;
  right: 0px;
  width: 200px;
  height: auto;
  background: var(--white);
  box-shadow: var(--shadow);
  z-index: 999;
}
.sidebar nav.mobile-menu-container ul li a {
  padding: calc(var(--p) / 2) var(--p);
}
.mobile-menu {
  height: 50px;
  width: 30px;
  z-index: 999;
  display: none;
}
@media only screen and (max-width: 480px) {
  .mobile-menu {
    display: block;
  }
}
.mobile-menu:hover + ul {
  display: block;
  position: absolute;
  right: 0px;
  top: 60px;
  background: var(--white);
  box-shadow: var(--shadow);
}
.mobile-menu:hover + ul > li > a {
  padding: var(--p) calc(var(--p) * 1.5);
}
.mobile-menu > div {
  display: block;
  width: 30px;
  height: 4px;
  background: var(--primary);
  position: relative;
  transform: translateY(23px);
}
.mobile-menu > div:before,
.mobile-menu > div:after {
  content: '';
  height: 4px;
  top: 10px;
  position: absolute;
  width: 100%;
  display: block;
  background: var(--primary);
}
.mobile-menu > div:after {
  top: -10px;
}
main {
  background: var(--bg);
  padding: calc(var(--p) * 1) calc(var(--p) * 1);
  flex-grow: 1;
  overflow: auto;
}
@media only screen and (max-width: 480px) {
  main {
    padding: var(--p);
    border-radius: 0px;
    z-index: 0;
    box-sizing: border-box;
  }
}
main > header + section.list-section {
  min-height: calc(100vh - 150px);
  padding: 0;
  display: flex;
  flex-direction: column;
}
main > header + section.list-section.plus-dash {
  flex-direction: column;
  gap: 20px;
}
main > header + section.list-section .grid-header {
  border-radius: calc(var(--br) / 2) calc(var(--br) / 2) 0 0;
}
main > header + section.list-section:last-child {
  margin-bottom: 0;
}
main > header + section.list-section table.table {
  margin: 20px;
  width: calc(100% - 40px);
}
@media only screen and (max-width: 480px) {
  main > header + section.list-section table.table {
    margin: 0;
    width: 100%;
  }
}
main > header + section.list-section table.table + .no-results {
  margin: 20px;
  width: calc(100% - 40px);
}
.title {
  font-weight: 600;
  font-size: 1.4rem;
}
@media only screen and (max-width: 480px) {
  .title {
    font-size: 1.1rem;
  }
}
@keyframes fadeFlash {
  0% {
    background: var(--white);
    box-shadow: 0 0 0px 10px var(--white), 0 0 0 8px var(--white);
  }
  50% {
    background: var(--fade-red);
    box-shadow: 0 0 0 10px var(--white), 0 0 0px 12px var(--red);
  }
  100% {
    background: var(--white);
    box-shadow: 0 0 0 8px var(--white), 0 0 0px 10px var(--white);
  }
}
.stat {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.stat.stat-button > div:first-child {
  font-size: inherit;
}
.stat > div:first-child {
  font-size: 1.6rem;
  color: var(--primary);
}
.stat.red {
  border-radius: calc(var(--br) / 2);
  animation: fadeFlash 2s infinite;
}
.stat.red div:first-child {
  color: var(--red);
}
.stat.important div:first-child {
  font-weight: 600;
}
.ancillary-info {
  font-weight: 600;
  color: var(--secondary);
  text-align: right;
  font-size: 20px;
  margin-left: auto;
}
.dashboard {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--p);
  margin-bottom: 10px;
}
@media only screen and (max-width: 480px) {
  .dashboard {
    grid-template-columns: 1fr;
    margin-bottom: 0;
  }
}
.dashboard.single {
  grid-template-columns: 1fr;
}
.dashboard > .widget {
  border: 1px solid var(--border);
  border-radius: var(--br);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.dashboard > .widget .divider {
  width: 1px;
  background: var(--border);
}
.dashboard > .widget .divider:last-child {
  display: none;
}
.dashboard > .widget.onstop-summary {
  grid-column: 1 / -1;
}
.dashboard > .widget.triple-widget {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-column: 1 / -1;
}
.dashboard > .widget.triple-widget > div {
  border-right: 1px solid var(--border);
}
.dashboard > .widget.triple-widget > div:last-child {
  border-right: none;
}
.dashboard > .widget.quadruple-widget {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-column: 1 / -1;
}
.dashboard > .widget.quadruple-widget > div {
  border-right: 1px solid var(--border);
}
.dashboard > .widget.quadruple-widget > div:last-child {
  border-right: none;
}
.dashboard > .widget.summary {
  display: grid;
  grid-template-columns: 0.75fr 1px 0.5fr 1px 1fr;
  grid-column: 1 / -1;
  background: var(--bg);
  flex-direction: row;
}
.dashboard > .widget.summary.all-sites {
  grid-template-columns: 0.75fr 1px 0.5fr 1px;
}
@media only screen and (max-width: 480px) {
  .dashboard > .widget.summary.all-sites {
    grid-template-columns: 1fr;
  }
}
@media only screen and (max-width: 480px) {
  .dashboard > .widget.summary {
    grid-template-columns: 1fr;
  }
}
.dashboard > .widget.summary.single {
  grid-template-columns: 1fr;
}
.dashboard > .widget.summary.triple {
  grid-template-columns: 1fr 1px 1fr 1px 1fr;
}
.dashboard > .widget.summary.double {
  grid-template-columns: 1fr 1px 1fr;
}
@media only screen and (max-width: 480px) {
  .dashboard > .widget.summary.double {
    grid-template-columns: 1fr;
  }
}
.dashboard > .widget.summary .summary-rows.onstop-rows {
  padding: var(--p);
  gap: 5px;
  display: flex;
  flex-wrap: wrap;
}
.dashboard > .widget.summary .summary-rows.onstop-rows .onstop {
  margin-left: 0;
  padding: calc(var(--p) / 2) var(--p);
  margin-bottom: 10px;
}
.dashboard > .widget.summary .summary-rows.onstop-rows > div {
  display: inline-flex;
}
.dashboard > .widget.summary > div.pie-summary {
  padding: var(--p);
  display: flex;
  align-items: center;
}
.dashboard > .widget.summary .total-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  flex-direction: row;
  flex-wrap: wrap;
  background: var(--bg2);
}
.dashboard > .widget.summary .total-summary .stat-row {
  border-bottom: none;
}
.dashboard > .widget.summary .total-summary .stat-row:last-child {
  border-bottom: none;
}
@media only screen and (max-width: 480px) {
  .dashboard > .widget.summary .total-summary {
    border-right: none;
    width: 100%;
  }
}
.dashboard > .widget.summary .stats {
  grid-template-columns: 1fr;
  padding: 0px;
}
.dashboard > .widget.summary .stats .stat > div:first-child {
  font-size: 2rem;
}
.dashboard > .widget .title {
  background: var(--highlight);
  padding: var(--p);
  border-radius: calc(var(--br) / 1) calc(var(--br) / 1) 0 0;
  border-bottom: 1px solid var(--border);
}
.dashboard > .widget .subtitle {
  margin-bottom: 0;
  padding: 0 var(--p);
  background: var(--bg);
  font-size: 1rem;
  border-bottom: 1px solid var(--border);
}
@media only screen and (max-width: 480px) {
  .dashboard > .widget .subtitle {
    border-top: 1px solid var(--border);
  }
}
.dashboard > .widget .subtitle:first-child {
  border-radius: var(--br) var(--br) 0 0;
}
@media only screen and (max-width: 480px) {
  .dashboard > .widget .subtitle:first-child {
    border-radius: 0px;
  }
}
.dashboard > .widget.full {
  grid-column: 1 / -1;
  background: var(--bg);
}
.onstop-rows {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  padding: 20px;
}
.onstop-rows .onstop {
  padding: 10px;
  border-radius: var(--br);
  background: var(--red-fade);
  color: var(--red);
  font-weight: 600;
  margin-bottom: 10px;
}
.dash-rows .dash-row {
  display: flex;
  gap: 10px;
  align-items: center;
  border-bottom: 1px solid var(--border);
  padding: calc(var(--p) / 2) var(--p);
}
.dash-rows .dash-row.hoverable {
  cursor: pointer;
}
.dash-rows .dash-row.hoverable:hover {
  background: #f3f3f3;
}
.dash-rows .dash-row.dash-row-internal-transfer-header {
  display: grid;
  background: hsla(160, 75%, 12.5%, 0.2);
  grid-template-columns: 1.5fr 1fr 1fr 0.5fr 1fr;
  font-weight: 600;
}
.dash-rows .dash-row.dash-row-internal-transfer {
  display: grid;
  background: hsla(160, 75%, 12.5%, 0.1);
  grid-template-columns: 1.5fr 1fr 1fr 0.5fr 1fr;
}
@media only screen and (max-width: 480px) {
  .dash-rows .dash-row {
    flex-wrap: wrap;
  }
  .dash-rows .dash-row .count {
    order: 2;
  }
  .dash-rows .dash-row .total {
    flex-grow: 1;
    order: 3;
  }
  .dash-rows .dash-row .tons {
    flex-grow: 1;
    order: 2;
  }
  .dash-rows .dash-row .wagon-tons {
    order: 3;
  }
  .dash-rows .dash-row .chart-container {
    order: 3;
    justify-content: flex-start;
  }
  .dash-rows .dash-row .wagons {
    order: 3;
  }
  .dash-rows .dash-row .name {
    order: 1;
    width: 100%;
  }
}
.dash-rows .dash-row.visitor-list {
  display: grid;
  grid-template-columns: 130px 100px 1fr;
}
.dash-rows .dash-row .total {
  font-weight: 600;
  width: 75px;
  text-align: right;
  flex-shrink: 0;
}
.dash-rows .dash-row .name {
  flex-grow: 1;
  font-size: 13px;
  font-weight: 600;
}
.dash-rows .dash-row .name .onstop {
  margin-top: 5px;
  margin-left: 0;
  font-weight: 600;
  font-size: 10px;
}
.dash-rows .dash-row .status-pill {
  width: 65px;
  justify-content: center;
  font-weight: 600;
}
.dash-rows .dash-row .ewc-code {
  font-weight: 600;
  color: #fff;
  width: 42px;
  justify-content: center;
  display: flex;
  border-radius: var(--br);
  background: var(--secondary);
  padding: calc(var(--p) / 4) calc(var(--p) / 2.5);
  font-size: 10px;
  flex-shrink: 0;
  flex-grow: 0;
}
.dash-rows .dash-row .tons {
  font-style: italic;
  width: 103px;
  font-size: 12px;
  flex-shrink: 0;
  text-align: right;
}
.dash-rows .dash-row .last-payment {
  width: 200px;
  font-size: 12px;
  flex-shrink: 0;
  text-align: right;
  font-style: italic;
}
.dash-rows .dash-row .area {
  width: 200px;
  font-size: 12px;
  flex-shrink: 0;
  text-align: right;
}
.dash-rows .dash-row .wagons {
  font-size: 10px;
  flex-shrink: 0;
  text-align: right;
}
.dash-rows .dash-row .wagon-tons {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.dash-rows .dash-row .count {
  width: 70px;
  text-align: center;
  border-radius: calc(var(--br) / 3);
  width: 38px;
  justify-content: center;
  background: var(--primary);
  display: inline-flex;
  padding: 5px;
  flex-shrink: 0;
  color: var(--white);
  font-size: 14px;
  font-weight: 600;
}
.dash-rows .dash-row:last-child {
  border-bottom: none;
}
.dash-rows .subtitle {
  grid-column: 1 / -1;
}
.dashboard .stat-row {
  display: flex;
  gap: 10px;
  padding: var(--p) 26px;
  align-items: center;
}
@media only screen and (max-width: 480px) {
  .dashboard .stat-row {
    padding: 15px;
    display: grid;
    grid-template-columns: 50px 1fr;
    gap: 10px;
  }
}
.dashboard .stat-row.stat-list-header {
  border-bottom: 1px solid var(--border);
  padding-right: 20px;
}
.dashboard .stat-row.small .stat-icon svg {
  height: 20px;
}
.dashboard .stat-row.small .stats .stat > div:first-child {
  font-size: 1.4rem;
}
.dashboard .stat-row:last-child {
  border-bottom: none;
}
.dashboard .stat-row .stat-icon svg {
  width: 50px;
  height: 30px;
}
.dashboard .stat-row .stat-icon svg path {
  fill: var(--primary);
}
.dashboard .stat-row .stats {
  padding: 0 var(--p);
  display: grid;
  gap: 20px;
}
.dashboard .stat-row .stats.double {
  grid-template-columns: 1fr 1fr;
}
@media only screen and (max-width: 480px) {
  .dashboard .stat-row .stats.double {
    grid-template-columns: 1fr;
  }
}
.dashboard .stat-row .stats.single {
  grid-template-columns: 1fr;
}
.dashboard .stat-row .stats.important .stat > div:first-child {
  font-weight: 600;
}
.dashboard .stat-row .stats.important .stat > div:last-child {
  font-size: 12px;
  line-height: 120%;
}
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}
html body table.quickgrid[theme="default"] tbody tr td {
  padding: 10px;
}
.flex {
  display: flex;
  gap: 20px;
  width: 100%;
}
.flex.aic {
  align-items: center;
}
.flex.delivery-flex {
  margin-top: var(--p);
  gap: 40px;
}
.flex.delivery-flex > div {
  flex-grow: 1;
}
.flex.totals-flex > div {
  flex-grow: 1;
}
.flex.totals-flex textarea {
  height: 120px;
}
.panel {
  width: 50%;
  background: var(--bg);
  border-radius: var(--br);
  border: 1px solid var(--border);
  height: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-grow: 1;
  padding: 0 calc(var(--p) * 2);
}
.panel .b {
  width: 100%;
}
.g-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}
.g-group .gg-3-1 {
  grid-template-columns: min-content 1fr;
}
.new-job-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 40px;
  border: 1px solid var(--border);
  border-radius: var(--br);
  align-items: center;
  justify-content: center;
}
.new-job-container .button {
  width: 300px;
  padding: 30px 0;
}
.new-job-container > * {
  width: 300px;
}
.g {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 30px;
  row-gap: 20px;
  max-width: 800px;
  height: fit-content;
  box-sizing: border-box;
  margin-bottom: var(--p);
  padding-bottom: var(--p);
}
.g.g3 {
  grid-template-columns: 1fr 1fr 1fr;
}
.g.full-width {
  max-width: 100%;
}
.g.g-address {
  grid-template-columns: 1fr;
  width: 375px;
  row-gap: 5px;
}
.g.g-address .b:nth-child(2) label,
.g.g-address .b:nth-child(3) label,
.g.g-address .b:nth-child(4) label {
  display: none;
}
.g.g-address .b:last-child {
  margin-top: 10px;
}
.g.g-order-top {
  grid-template-columns: 80px 120px 1fr  130px 120px;
  max-width: 100%;
  column-gap: 30px;
}
.g.g-order-top .b .button {
  margin-top: 22px;
}
.g.g-route-top {
  grid-template-columns: 120px 120px 1fr 1fr 200px 110px;
  max-width: 100%;
  column-gap: 30px;
}
.g.g-haulierroute-top {
  grid-template-columns: 120px 1fr 1.5fr 200px;
  max-width: 100%;
  column-gap: 30px;
}
@media only screen and (max-width: 480px) {
  .g.g-haulierroute-top {
    grid-template-columns: 1fr;
  }
}
.g.nm {
  margin: 0px;
}
.g.np {
  padding: 0px;
}
.g.single {
  grid-template-columns: 1fr;
}
.g .double {
  display: grid;
  gap: 50px;
  grid-template-columns: 1fr 1fr;
}
.g.g-invoice {
  grid-template-columns: 1fr 1fr 1fr;
}
.g.g-invoice .b label + div {
  font-size: 20px;
  font-weight: 600;
}
@media only screen and (max-width: 480px) {
  .g {
    grid-template-columns: 1fr;
  }
}
.g > div:not(.b):not(.suggestions):not(.product-switch):not(.b-product-container):not(.search-address):not(.daterangepicker-visibility-hidden) {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.g > div:not(.b):not(.suggestions):not(.product-switch):not(.b-product-container):not(.search-address):not(.daterangepicker-visibility-hidden).dual-inputs {
  flex-direction: row;
}
.b-product-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 30px;
  grid-column: 1 / 4;
  flex-direction: row;
}
.b-product-container .button {
  margin-top: 24px;
  white-space: nowrap;
}
.b {
  display: grid;
  grid-template-columns: 1fr;
  align-items: flex-start;
  gap: 10px;
}
.b[disabled] {
  opacity: 0.5;
}
.b label {
  font-weight: 500;
  font-size: 0.9rem;
}
.b.check {
  padding-top: 20px;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  justify-content: flex-end;
}
.b.check.pn {
  padding: 0px;
}
.b.search {
  margin-bottom: var(--p);
}
.b input,
.b select,
.b textarea {
  font-family: 'Poppins', sans-serif;
}
.b input[readonly],
.b select[readonly],
.b textarea[readonly] {
  background: var(--bg);
}
.b.multiselect {
  margin-bottom: var(--p);
}
.b.multiselect ul li {
  display: flex;
  align-items: center;
  gap: 10px;
}
.b.multiselect ul li label {
  padding-top: 3px;
}
.b.reg-plate input {
  background: var(--yellow);
  text-align: center;
  font-family: 'UKNumberPlate', sans-serif;
  text-transform: uppercase;
  font-size: 34px;
  border: none;
  border-radius: 10px;
}
.b .display {
  font-weight: 600;
  font-size: 0.9rem;
  border: 1px solid var(--border);
  background: var(--bg);
  border-radius: calc(var(--br) / 4);
  padding: calc(var(--p) / 1.6);
  height: 47px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.b.b-delivery-notes .display,
.b.b-delivery-notes textarea {
  height: 97px;
  width: 100%;
  align-items: flex-start;
  background: var(--lightyellow);
  margin-bottom: 0px;
}
.b.b-delivery-notes .display[readonly],
.b.b-delivery-notes textarea[readonly] {
  border: none;
  resize: none;
  border: 1px dashed var(--border);
}
.b.b-delivery-notes .display[readonly]:focus-visible,
.b.b-delivery-notes textarea[readonly]:focus-visible {
  outline: none;
  box-shadow: none;
}
.b.stretch {
  grid-column: 1 / -1;
}
.small-reg-plate {
  background: var(--yellow);
  text-align: center;
  font-family: 'UKNumberPlate', sans-serif;
  text-transform: uppercase;
  padding: 5px 5px;
  border-radius: calc(var(--br) / 2);
}
.small-reg-plate.is-zero {
  background: var(--red);
  color: white;
}
input,
select,
textarea {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 4);
  padding: calc(var(--p) / 1.6);
  font-size: 0.9rem;
  box-sizing: border-box;
  width: 100%;
  font-family: 'Poppins', sans-serif;
}
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 1px solid var(--primary);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 0 0 3px 0 var(--primary);
}
select.select2[aria-hidden="true"] {
  display: none;
}
input[type="checkbox"] {
  height: 20px;
  width: 20px;
  border-color: var(--border);
}
.select2-container--default .select2-selection--single {
  border-color: var(--border);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: calc(var(--br) / 4);
  height: 46px;
}
.select2-selection__rendered {
  height: 44px;
  padding-top: calc(var(--p) / 2);
  padding-left: calc(var(--p) / 1.4);
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 44px;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--primary);
}
.select2-container--default.select2-container--disabled .select2-selection--single {
  background: var(--bg);
}
.rz-multiselect,
.rz-dropdown {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border);
}
.rz-paginator-element {
  box-sizing: border-box;
}
.rz-paginator {
  border-top: 1px solid var(--border);
}
.dialog-overlay {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0px;
  left: 0px;
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.dialog-overlay .dialog {
  background: var(--white);
  width: 600px;
  min-height: 200px;
  max-height: 100vh;
  border-radius: var(--br);
  box-shadow: var(--shadow);
  display: flex;
  overflow: auto;
  flex-direction: column;
}
.dialog-overlay .dialog .will-rings-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.dialog-overlay .dialog .will-rings-buttons .button {
  margin-left: 10px;
}
.dialog-overlay .dialog header {
  padding: var(--p);
  font-weight: 600;
}
.dialog-overlay .dialog main {
  box-shadow: none;
  flex-grow: 1;
  padding: var(--p);
}
.dialog-overlay .dialog footer {
  display: flex;
  justify-content: flex-end;
}
.dialog-overlay .dialog .g {
  grid-template-columns: 1fr;
}
.dialog-overlay .dialog .g.double {
  grid-template-columns: 1fr 1fr;
}
.dialog-overlay .dialog .g.vehicle-override-row {
  grid-template-columns: 1fr 1fr 70px;
  align-items: flex-end;
  gap: 10px;
}
.dialog-overlay .dialog .g.vehicle-override-row .button {
  transform: translateY(-3px);
  font-size: 12px;
}
.rz-grid-table td {
  vertical-align: middle;
}
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 100px;
}
@media only screen and (max-width: 480px) {
  .login-container {
    width: 100%;
    padding: var(--p);
    box-sizing: border-box;
  }
}
.login-container img {
  width: 215px;
  margin: 0 auto 20px auto;
}
.login-container form {
  display: flex;
  flex-direction: column;
  padding: calc(var(--p) * 3);
  border-radius: var(--br);
  gap: 30px;
  width: 480px;
  box-sizing: border-box;
}
@media only screen and (max-width: 480px) {
  .login-container form {
    max-width: 100%;
    width: 100%;
    padding: calc(var(--p) * 2);
  }
}
.login-container form h1 {
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}
.login-container form .b label {
  display: flex;
  justify-content: space-between;
}
.login-container form input[type="submit"] {
  background: var(--primary);
  color: var(--white);
  padding: calc(var(--p) / 1.5) var(--p);
  border-radius: calc(var(--br) / 2);
  border: none;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.05), 0 1px 0 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  margin-left: 0;
  background: var(--secondary);
  color: var(--primary);
}
@media only screen and (max-width: 480px) {
  .login-container form input[type="submit"] {
    font-size: 0.9rem;
  }
}
.login-container form input[type="submit"].inline-button {
  display: inline-flex;
}
.login-container form input[type="submit"].drop-button {
  position: relative;
  padding-right: 30px;
}
.login-container form input[type="submit"].drop-button:after {
  content: '';
  position: absolute;
  right: 15px;
  top: 19px;
  height: 6px;
  width: 6px;
  border-bottom: 1px solid var(--primary);
  border-right: 1px solid var(--primary);
  transform: rotate(45deg);
}
.login-container form input[type="submit"].drop-button.small {
  padding-right: 30px;
}
.login-container form input[type="submit"].drop-button.small:after {
  top: 13px;
}
.login-container form input[type="submit"].drop-button.small:hover .drop-button-dropdown {
  top: 36px;
}
.login-container form input[type="submit"].drop-button .drop-button-dropdown {
  display: none;
}
.login-container form input[type="submit"].drop-button:hover {
  cursor: pointer;
}
.login-container form input[type="submit"].drop-button:hover .drop-button-dropdown {
  display: block;
  top: 45px;
  right: 0px;
  position: absolute;
  width: 200px;
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  background: #fff;
}
.login-container form input[type="submit"].drop-button:hover .drop-button-dropdown ul li {
  padding: 10px;
  border-bottom: 1px solid var(--border);
}
.login-container form input[type="submit"].drop-button:hover .drop-button-dropdown ul li:last-child {
  border-bottom: none;
}
.login-container form input[type="submit"].drop-button:hover .drop-button-dropdown ul li:hover {
  background: var(--bg);
  cursor: pointer;
}
.login-container form input[type="submit"].drop {
  position: relative;
}
.login-container form input[type="submit"].drop ul {
  top: 35px;
  left: 0px;
  display: none;
  position: absolute;
  width: 200px;
  background: var(--white);
  border-radius: var(--br);
  box-shadow: var(--shadow2);
  border: 1px solid var(--border);
}
.login-container form input[type="submit"].drop ul li {
  border-bottom: 1px solid var(--border);
}
.login-container form input[type="submit"].drop ul li:last-child {
  border-bottom: none;
}
.login-container form input[type="submit"].drop ul li > div {
  padding: calc(var(--p) / 2);
}
.login-container form input[type="submit"].drop:hover ul {
  display: block;
}
.login-container form input[type="submit"].drop:hover ul li div {
  color: var(--primary);
}
.login-container form input[type="submit"].drop:hover ul li div:hover {
  color: var(--secondary);
}
.login-container form input[type="submit"]:hover {
  box-shadow: inset 100px 100px 0 0 rgba(0, 0, 0, 0.1);
}
.login-container form input[type="submit"].secondary-solid {
  background: #0f5ac1;
}
.login-container form input[type="submit"].secondary-solid:hover {
  background: #0f5ac1;
  box-shadow: inset 100px 100px 0 0 rgba(0, 0, 0, 0.1);
}
.login-container form input[type="submit"].secondary {
  color: var(--primary);
  border: 2px solid var(--primary);
  background: transparent;
}
.login-container form input[type="submit"].secondary:hover {
  box-shadow: inset 0 0 2px 0 var(--primary);
}
.login-container form input[type="submit"].tertiary {
  color: var(--primary);
  background: transparent;
}
.login-container form input[type="submit"].tertiary:hover {
  background: var(--highlight);
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.05), 0 1px 0 0 rgba(0, 0, 0, 0.4);
}
.login-container form input[type="submit"].inline {
  color: var(--primary);
  background: transparent;
  box-shadow: none;
}
.login-container form input[type="submit"].inline:hover {
  background: var(--highlight);
}
.login-container form input[type="submit"].small {
  padding: calc(var(--p) / 2) calc(var(--p) / 1.5);
  display: inline-flex;
  font-size: 14px;
  height: 40px;
}
.login-container form input[type="submit"].tiny {
  padding: calc(var(--p) / 2) calc(var(--p) / 2);
  display: inline-flex;
  font-size: 12px;
  height: 23px;
}
.login-container form input[type="submit"] > svg {
  margin-right: 10px;
}
.login-container form input[type="submit"] > svg path {
  fill: var(--white);
}
.login-container form input[type="submit"].secondary > svg path {
  fill: var(--primary);
}
.login-container form input[type="submit"].warning {
  background: var(--orange);
  color: white;
}
.login-container form input[type="submit"].delete {
  background: var(--red-fade);
  color: var(--red);
  font-size: 12px;
  padding: 10px;
  height: 30px;
}
.login-container form input[type="submit"].delete-button {
  background: var(--red);
}
.login-container form input[type="submit"].secondary-button {
  background: var(--secondary);
  color: white;
}
.login-container form input[type="submit"].secondary-button:hover {
  background: orange;
}
.login-container form input[type="submit"] > svg {
  width: 15px;
  height: 15px;
}
.login-container form input[type="submit"].save {
  background: var(--green);
}
.login-container form .validation {
  background: var(--red);
  color: var(--white);
  padding: 20px;
  text-align: center;
  border-radius: calc(var(--br) / 2);
}
.dialog-overlay .dialog {
  background: var(--bg);
}
.dialog-overlay .dialog header {
  padding: var(--p) calc(var(--p) * 2);
  margin-bottom: 0;
  border-bottom: 1px solid var(--border);
}
.dialog-overlay .dialog header .close {
  transform: scale(1.5);
  cursor: pointer;
}
.dialog-overlay .dialog header .close:hover {
  color: var(--secondary);
}
.dialog-overlay .dialog > div {
  padding: calc(var(--p) * 2);
  flex-grow: 1;
}
@media only screen and (max-width: 480px) {
  .dialog-overlay .dialog > div {
    padding: var(--p);
  }
}
.dialog-overlay .dialog > div section {
  background: var(--white);
}
.dialog-overlay .dialog .card.form-card {
  display: flex;
  flex-direction: column-reverse;
}
.dialog-overlay .dialog button,
.dialog-overlay .dialog .button {
  margin-left: auto;
}
.dialog-overlay .dialog .add-line-row {
  justify-content: space-between;
  padding: 10px;
  border-radius: 0px;
}
.dialog-overlay .dialog .add-line-row button,
.dialog-overlay .dialog .add-line-row .button {
  margin-left: 0px;
}
.dialog-overlay .dialog .add-line-row button.small.dif,
.dialog-overlay .dialog .add-line-row .button.small.dif {
  order: 2;
}
.buttons-container {
  display: flex;
  justify-content: flex-end;
  padding: var(--p);
}
.link-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  border: 1px solid var(--border);
  border-radius: var(--br);
}
@media only screen and (max-width: 480px) {
  .link-cards {
    grid-template-columns: 1fr;
  }
}
.link-cards .link-card {
  padding: var(--p);
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid var(--border);
}
.link-cards .link-card:hover {
  background: var(--primary);
  color: var(--white);
  box-shadow: var(--shadowhighlight);
}
.container .tabs .container .tab.selected {
  transform: translateY(0px);
}
.tabs > .container {
  display: flex;
  gap: 20px;
}
@media only screen and (max-width: 480px) {
  .tabs > .container {
    display: grid;
    gap: 10px;
    margin-bottom: 10px;
    grid-template-columns: 1fr 1fr;
  }
  .tabs > .container .tab {
    background: var(--bg);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    border-radius: var(--br);
    box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.05), inset 0 2px 5px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  .tabs > .container .tab.selected {
    background: var(--secondary);
    color: var(--white);
    border-color: var(--primary);
  }
  .tabs > .container .tab.selected:hover {
    background: var(--secondary);
    color: var(--white);
  }
}
.tabs .tab {
  border-left: 1px solid transparent;
  border-right: 1px solid transparent;
  border-top: 1px solid transparent;
  padding: calc(var(--p) / 1) calc(var(--p) * 0);
  transition: var(--transition);
}
.tabs .tab:hover {
  cursor: pointer;
  color: var(--primary);
}
.tabs .tab.selected {
  font-weight: 600;
  color: var(--primary);
  box-shadow: inset 0 -3px 0 0 var(--primary);
}
.tabs .tab.selected:hover {
  background: transparent;
  cursor: default;
}
.tabs + .container {
  border-radius: 0 var(--br);
  border: 1px solid var(--border);
  padding: var(--p);
  min-height: calc(100vh - 290px);
  display: flex;
  flex-direction: column;
}
@media only screen and (max-width: 480px) {
  .tabs + .container {
    border-radius: var(--br);
    padding: 0px;
    border: none;
  }
}
.tabs + .container > .title {
  padding: 20px 0;
}
@media only screen and (max-width: 480px) {
  .tabs + .container > .title {
    padding: var(--p);
  }
}
.tabs + .container > .subtitle {
  padding-left: calc(var(--p) / 1.5);
}
.tabs + .container > section {
  border: none;
  height: 100%;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: calc(var(--p) / 2);
}
@media only screen and (max-width: 480px) {
  .tabs {
    overflow: auto;
  }
  .tabs .tab {
    padding: var(--p);
  }
}
.vertical-tabs {
  display: flex;
  gap: 10px;
}
@media only screen and (max-width: 480px) {
  .vertical-tabs {
    flex-direction: column;
  }
}
@media only screen and (max-width: 480px) {
  .vertical-tabs .tabs {
    width: max-content;
  }
}
.vertical-tabs .tabs .container {
  display: flex;
  gap: 5px;
  flex-direction: column;
}
@media only screen and (max-width: 480px) {
  .vertical-tabs .tabs .container {
    flex-direction: row;
  }
}
.vertical-tabs .tabs .container .tab {
  width: 175px;
  border-radius: 0px;
  border: none;
  border: 1px solid transparent;
  font-weight: 500;
  padding: 15px var(--p);
}
@media only screen and (max-width: 480px) {
  .vertical-tabs .tabs .container .tab {
    width: auto;
  }
}
.vertical-tabs .tabs .container .tab.selected {
  background: var(--bg);
  font-weight: 700;
  color: var(--secondary);
  box-shadow: inset -3px 0 0 0 var(--primary);
}
.vertical-tabs > .container {
  flex-grow: 1;
  border: none;
  border-left: 1px solid var(--border);
  border-radius: 0px;
  padding-top: 4px;
}
@media only screen and (max-width: 480px) {
  .vertical-tabs > .container {
    border-left: none;
  }
}
.lds-ring {
  display: block;
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}
.lds-ring div {
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 64px;
  height: 64px;
  margin: 8px;
  border: 8px solid var(--primary);
  border-radius: 50%;
  animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: var(--primary) transparent transparent transparent;
}
.lds-ring div:nth-child(1) {
  animation-delay: -0.45s;
}
.lds-ring div:nth-child(2) {
  animation-delay: -0.3s;
}
.lds-ring div:nth-child(3) {
  animation-delay: -0.15s;
}
@keyframes lds-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.loading-message {
  display: block;
  text-align: center;
  margin-top: var(--p);
}
.zero-warning {
  color: var(--red);
  font-size: 10px;
  font-weight: 600;
  margin-top: 5px;
}
table.table {
  width: 100%;
}
table.table.table-smaller {
  font-size: 0.9rem;
}
table.table .drop {
  z-index: 1;
}
table.table thead tr {
  border-bottom: 2px solid var(--border);
}
table.table thead tr th {
  padding: calc(var(--p) / 2);
  text-align: left;
  font-weight: 600;
}
table.table thead tr th.text-right {
  text-align: right;
}
table.table tbody tr {
  border-bottom: 1px solid var(--border);
}
table.table tbody tr td {
  padding: calc(var(--p) / 1.5) calc(var(--p) / 2);
  height: 23px;
  vertical-align: middle;
}
table.table tbody tr td .status-pill {
  margin-right: 2px;
}
table.table tbody tr td.Void {
  background: #eee;
  text-decoration: line-through;
}
table.table tbody tr td.Void ~ td {
  background: #eee;
  text-decoration: line-through;
}
table.table tbody tr td.Void ~ td .small-reg-plate {
  background: #ccc;
  text-decoration: none;
  color: black;
}
table.table tbody tr td.Void ~ td .zero-warning {
  display: none;
}
table.table tbody tr td.Void ~ td .status-pill {
  text-decoration: none;
  color: black;
}
table.table tbody tr:last-child {
  border-bottom: none;
}
table.table tbody tr:hover {
  background: var(--bg);
  cursor: pointer;
  color: var(--primary);
  box-shadow: inset 0px 3px 0 0 var(--white), inset 0px -2px 0 0 var(--white);
}
table.table tbody tr.nohov:hover {
  background: transparent;
  cursor: default;
  color: #000;
}
table.table tbody tr.highlight {
  background: lightyellow;
}
table.table tfoot tr {
  border-top: 2px solid var(--border);
}
table.table tfoot tr td {
  padding: var(--p) calc(var(--p) / 2);
  font-weight: 600;
}
@media only screen and (max-width: 480px) {
  table.table thead {
    display: none;
  }
  table.table tbody tr {
    display: flex;
    flex-wrap: wrap;
  }
  table.table tbody tr td {
    height: auto;
  }
}
table.table .vate-rate-column .b input {
  width: 65px;
}
table.table.bordered {
  border: 1px solid var(--border);
  margin-bottom: 20px;
  border-radius: calc(var(--br) / 2);
}
table.table.nohov-table tbody tr:hover {
  background: transparent;
  cursor: default;
  color: #000;
}
.nohov-table table.table tbody tr:hover {
  background: transparent;
  cursor: default;
}
.grid-flex {
  display: flex;
  min-height: calc(100% - 71px);
  border: 1px solid var(--border);
  border-radius: var(--br);
  flex-grow: 1;
  background: var(--white);
}
@media only screen and (max-width: 480px) {
  .grid-flex {
    flex-direction: column;
  }
}
.grid-flex .right-sidebar {
  width: 250px;
  border-left: 1px solid var(--border);
  padding: var(--p);
  background: var(--bg);
  border-radius: 0 calc(var(--br) / 1) calc(var(--br) / 1) 0;
}
.grid-flex .adv-filters {
  width: 250px;
  border-right: 1px solid var(--border);
  padding: var(--p);
  background: var(--bg);
  border-radius: calc(var(--br) / 1) 0 0 calc(var(--br) / 1);
}
.grid-flex .adv-filters:empty {
  display: none;
}
@media only screen and (max-width: 480px) {
  .grid-flex .adv-filters {
    display: none;
  }
}
.grid-flex .adv-filters .button {
  margin-bottom: calc(var(--p) * 2);
}
.grid-flex .grid-main {
  width: 100%;
}
.mobile-filters {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100vw;
  height: 100vh;
  z-index: 10;
  background: var(--bg);
  box-sizing: border-box;
  padding: var(--p);
  padding-top: 100px;
}
.mobile-filters .title {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mobile-filters .button.small.secondary {
  margin-bottom: 20px;
  background: var(--primary);
  color: var(--white);
  width: 100%;
  height: 50px;
}
.button.show-mobile-filters {
  display: none;
}
@media only screen and (max-width: 480px) {
  .button.show-mobile-filters {
    display: block;
    margin: 5px;
    text-align: center;
  }
}
.grid-header {
  display: flex;
  height: 60px;
  background: var(--bg);
  border-radius: 0 calc(var(--br) / 2) 0 0;
  margin-bottom: var(--p);
  border-bottom: 1px solid var(--border);
}
@media only screen and (max-width: 480px) {
  .grid-header {
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 0;
    height: auto;
  }
}
.grid-header .sort-filter {
  display: flex;
  padding-left: var(--p);
  align-items: center;
  gap: 10px;
}
.grid-header .sort-filter .button.drop.small.secondary svg {
  margin-right: 0px;
}
.grid-header .sort-filter .top-sorting {
  display: flex;
  align-items: center;
  gap: 10px;
}
.grid-header .sort-filter .top-sorting .button.small > svg {
  margin-right: 0;
}
.grid-header .sort-filter .top-sorting .button.small > svg path {
  fill: var(--primary);
}
.grid-header .sort-filter svg {
  height: 15px;
  width: 15px;
}
.grid-header .paginator {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-left: auto;
  padding-right: var(--p);
}
@media only screen and (max-width: 480px) {
  .grid-header .paginator {
    padding-bottom: 10px;
  }
}
.grid-header .paginator > select {
  height: 30px;
  padding: 0px;
}
@media only screen and (max-width: 480px) {
  .grid-header .paginator > select {
    display: none;
  }
}
.grid-header .paginator .results-total {
  white-space: nowrap;
}
@media only screen and (max-width: 480px) {
  .grid-header .paginator .results-total {
    display: none;
  }
}
.grid-header .paginator .paginator-range-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
.grid-header .paginator .paginator-range-actions .paginator-range {
  background: var(--highlight);
  font-size: 0.8rem;
  padding: 5px 8px;
  border-radius: var(--br);
  white-space: nowrap;
}
@media only screen and (max-width: 480px) {
  .grid-header .paginator .paginator-range-actions .paginator-range {
    display: none;
  }
}
.grid-header .paginator .paginator-range-buttons {
  display: flex;
  gap: 5px;
}
.grid-header .paginator .paginator-range-buttons button {
  background: var(--primary);
  color: var(--white);
  padding: calc(var(--p) / 1.5) var(--p);
  border-radius: calc(var(--br) / 2);
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.05), 0 1px 0 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  box-shadow: none;
  border: none;
  background: transparent;
  color: var(--primary);
  padding: calc(var(--p) / 2);
  flex-grow: 0;
  height: 30px;
}
@media only screen and (max-width: 480px) {
  .grid-header .paginator .paginator-range-buttons button {
    font-size: 0.9rem;
  }
}
.grid-header .paginator .paginator-range-buttons button.inline-button {
  display: inline-flex;
}
.grid-header .paginator .paginator-range-buttons button.drop-button {
  position: relative;
  padding-right: 30px;
}
.grid-header .paginator .paginator-range-buttons button.drop-button:after {
  content: '';
  position: absolute;
  right: 15px;
  top: 19px;
  height: 6px;
  width: 6px;
  border-bottom: 1px solid var(--primary);
  border-right: 1px solid var(--primary);
  transform: rotate(45deg);
}
.grid-header .paginator .paginator-range-buttons button.drop-button.small {
  padding-right: 30px;
}
.grid-header .paginator .paginator-range-buttons button.drop-button.small:after {
  top: 13px;
}
.grid-header .paginator .paginator-range-buttons button.drop-button.small:hover .drop-button-dropdown {
  top: 36px;
}
.grid-header .paginator .paginator-range-buttons button.drop-button .drop-button-dropdown {
  display: none;
}
.grid-header .paginator .paginator-range-buttons button.drop-button:hover {
  cursor: pointer;
}
.grid-header .paginator .paginator-range-buttons button.drop-button:hover .drop-button-dropdown {
  display: block;
  top: 45px;
  right: 0px;
  position: absolute;
  width: 200px;
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  background: #fff;
}
.grid-header .paginator .paginator-range-buttons button.drop-button:hover .drop-button-dropdown ul li {
  padding: 10px;
  border-bottom: 1px solid var(--border);
}
.grid-header .paginator .paginator-range-buttons button.drop-button:hover .drop-button-dropdown ul li:last-child {
  border-bottom: none;
}
.grid-header .paginator .paginator-range-buttons button.drop-button:hover .drop-button-dropdown ul li:hover {
  background: var(--bg);
  cursor: pointer;
}
.grid-header .paginator .paginator-range-buttons button.drop {
  position: relative;
}
.grid-header .paginator .paginator-range-buttons button.drop ul {
  top: 35px;
  left: 0px;
  display: none;
  position: absolute;
  width: 200px;
  background: var(--white);
  border-radius: var(--br);
  box-shadow: var(--shadow2);
  border: 1px solid var(--border);
}
.grid-header .paginator .paginator-range-buttons button.drop ul li {
  border-bottom: 1px solid var(--border);
}
.grid-header .paginator .paginator-range-buttons button.drop ul li:last-child {
  border-bottom: none;
}
.grid-header .paginator .paginator-range-buttons button.drop ul li > div {
  padding: calc(var(--p) / 2);
}
.grid-header .paginator .paginator-range-buttons button.drop:hover ul {
  display: block;
}
.grid-header .paginator .paginator-range-buttons button.drop:hover ul li div {
  color: var(--primary);
}
.grid-header .paginator .paginator-range-buttons button.drop:hover ul li div:hover {
  color: var(--secondary);
}
.grid-header .paginator .paginator-range-buttons button:hover {
  box-shadow: inset 100px 100px 0 0 rgba(0, 0, 0, 0.1);
}
.grid-header .paginator .paginator-range-buttons button.secondary-solid {
  background: #0f5ac1;
}
.grid-header .paginator .paginator-range-buttons button.secondary-solid:hover {
  background: #0f5ac1;
  box-shadow: inset 100px 100px 0 0 rgba(0, 0, 0, 0.1);
}
.grid-header .paginator .paginator-range-buttons button.secondary {
  color: var(--primary);
  border: 2px solid var(--primary);
  background: transparent;
}
.grid-header .paginator .paginator-range-buttons button.secondary:hover {
  box-shadow: inset 0 0 2px 0 var(--primary);
}
.grid-header .paginator .paginator-range-buttons button.tertiary {
  color: var(--primary);
  background: transparent;
}
.grid-header .paginator .paginator-range-buttons button.tertiary:hover {
  background: var(--highlight);
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.05), 0 1px 0 0 rgba(0, 0, 0, 0.4);
}
.grid-header .paginator .paginator-range-buttons button.inline {
  color: var(--primary);
  background: transparent;
  box-shadow: none;
}
.grid-header .paginator .paginator-range-buttons button.inline:hover {
  background: var(--highlight);
}
.grid-header .paginator .paginator-range-buttons button.small {
  padding: calc(var(--p) / 2) calc(var(--p) / 1.5);
  display: inline-flex;
  font-size: 14px;
  height: 40px;
}
.grid-header .paginator .paginator-range-buttons button.tiny {
  padding: calc(var(--p) / 2) calc(var(--p) / 2);
  display: inline-flex;
  font-size: 12px;
  height: 23px;
}
.grid-header .paginator .paginator-range-buttons button > svg {
  margin-right: 10px;
}
.grid-header .paginator .paginator-range-buttons button > svg path {
  fill: var(--white);
}
.grid-header .paginator .paginator-range-buttons button.secondary > svg path {
  fill: var(--primary);
}
.grid-header .paginator .paginator-range-buttons button.warning {
  background: var(--orange);
  color: white;
}
.grid-header .paginator .paginator-range-buttons button.delete {
  background: var(--red-fade);
  color: var(--red);
  font-size: 12px;
  padding: 10px;
  height: 30px;
}
.grid-header .paginator .paginator-range-buttons button.delete-button {
  background: var(--red);
}
.grid-header .paginator .paginator-range-buttons button.secondary-button {
  background: var(--secondary);
  color: white;
}
.grid-header .paginator .paginator-range-buttons button.secondary-button:hover {
  background: orange;
}
.grid-header .paginator .paginator-range-buttons button > svg {
  width: 15px;
  height: 15px;
}
.grid-header .paginator .paginator-range-buttons button.save {
  background: var(--green);
}
.grid-header .paginator .paginator-range-buttons button.selected {
  background: var(--secondary);
  color: var(--white);
}
.grid-header .paginator .paginator-range-buttons button:hover {
  color: var(--white);
}
.grid-header .paginator .paginator-range-buttons button[disabled] {
  background: transparent;
  color: var(--grey);
  box-shadow: none;
  border: none;
  cursor: default;
}
.grid-header .sort-filter {
  display: flex;
  flex-grow: 1;
}
.mission-control {
  padding: var(--p);
  background: var(--secondary);
  width: 100%;
  box-sizing: border-box;
}
.mission-control main {
  background: var(--bg);
  border: none;
}
@media only screen and (max-width: 480px) {
  .mission-control .grid-header {
    display: none;
  }
}
.check-text {
  display: flex;
}
.check-text .b:first-child {
  flex-grow: 1;
}
.check-text .b.check {
  display: flex;
  flex-direction: row-reverse;
  padding-top: 20px;
  padding-left: 20px;
}
.check-text .b.check label {
  width: 80px;
}
.onstop {
  background: var(--red);
  color: var(--white);
  padding: 5px 10px;
  border-radius: 100px;
  display: inline-flex;
  margin-left: 10px;
}
.onstop.big {
  margin-left: 0;
  padding: var(--p);
  width: 100%;
  box-sizing: border-box;
  text-align: center;
  justify-content: center;
}
.onstop-text {
  font-weight: 600;
  color: var(--red);
  display: inline-flex;
}
strong {
  font-weight: 600;
}
.status-pill {
  background: #333;
  color: var(--white);
  padding: 5px 10px;
  border-radius: 100px;
  display: inline-flex;
  font-size: 12px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100px;
  width: fit-content;
}
.status-pill.Invoice {
  background: var(--grey);
}
.status-pill.Saved {
  background: var(--primary);
}
.status-pill.Closed {
  background: var(--secondary);
}
.status-pill.Void {
  background: #ccc;
}
.status-pill.Late {
  background: var(--red);
}
.status-pill.Completed {
  background: var(--green);
}
.status-pill.Confirmed,
.status-pill.Finalised {
  background: var(--primary);
}
.status-pill.Cancelled {
  background: var(--red);
}
.status-pill.Draft,
.status-pill.Open {
  background: var(--grey);
}
.status-pill.Rounds {
  background: var(--secondary);
}
.status-pill.Retail {
  background: var(--primary);
}
.status-pill.status-pill-small {
  padding: 2px 5px 0 6px;
  font-size: 8px;
}
.print-eticket {
  background: var(--white);
}
.print-eticket section {
  border: none;
  padding-bottom: 0px;
}
.print-eticket section .b {
  display: grid;
  grid-template-columns: 300px 1fr;
  margin-bottom: 10px;
}
.print-eticket section .b label + div {
  font-weight: 600;
}
.filter-button {
  position: relative;
}
.filter-button .filter-dropdown {
  position: absolute;
  top: 50px;
  left: 0px;
  background: var(--white);
  border: 1px solid var(--border);
  box-shadow: var(--shadow2);
  border-radius: var(--br);
  padding: calc(var(--p) / 2);
  width: 200px;
}
.filter-button .filter-dropdown .title {
  font-size: 16px;
  margin-bottom: 5px;
  display: block;
}
.filter-button .filter-dropdown ul li {
  display: flex;
  gap: 5px;
  align-items: center;
}
.select-etickets .select-eticket-table {
  border: 1px solid var(--border);
  border-radius: var(--br);
  margin: var(--p) 0;
}
.select-etickets .select-eticket-table .select-eticket-row {
  border-bottom: 1px solid var(--border);
  display: grid;
  gap: 10px;
  align-items: center;
  grid-template-columns: 40px 1fr 1fr 1fr;
}
.select-etickets .select-eticket-table .select-eticket-row > * {
  padding: calc(var(--p) / 2);
}
.select-etickets .select-eticket-table .select-eticket-row:last-child {
  border-bottom: none;
}
.select-etickets .no-closed {
  color: var(--red);
  border: 1px solid var(--border);
  border-radius: var(--br);
  padding: var(--p);
  margin-top: var(--p);
  font-weight: 600;
}
.no-results {
  height: 100px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: 400;
  color: var(--grey);
  margin-top: var(--p);
}
.product-switch {
  display: flex;
  gap: 10px;
}
.product-switch > div {
  border-radius: var(--br);
  border: 1px solid var(--border);
  padding: 0px;
  flex-grow: 1;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: 0.05s all ease-out;
  gap: 10px;
}
.product-switch > div svg {
  height: 17px;
  width: 17px;
}
.product-switch > div.selected {
  background: var(--primary);
  color: var(--white);
  border-color: var(--primary);
  font-weight: 600;
  cursor: default;
}
.product-switch > div.selected svg path {
  fill: white;
}
.product-switch > div.selected:hover {
  border-color: var(--primary);
  background: var(--primary);
}
.product-switch > div:hover {
  background: var(--primary);
  color: var(--white);
  border-color: var(--primary);
}
.product-switch > div:hover svg path {
  fill: white;
}
.site-name {
  font-size: 1.4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}
.tons-to {
  background: var(--bg);
  padding: var(--p);
  font-size: 1.4rem;
  border-radius: var(--br);
  margin-bottom: calc(var(--p) * 2);
}
.tons-to span {
  font-size: 2rem;
  font-weight: 600;
}
.approval-flex {
  border-radius: var(--br);
  display: flex;
  gap: 10px;
}
.approval-flex .approval-side {
  width: 350px;
  background: var(--bg);
  border-radius: calc(var(--br) / 1) 0 0 calc(var(--br) / 1);
}
.approval-flex .approval-side .approval-area {
  padding: var(--p);
}
.approval-flex .approval-side .approval-area .invoices-left {
  border-radius: calc(var(--br) / 2);
  text-align: center;
  background: lightyellow;
  padding: calc(var(--p) / 1.5);
}
.approval-flex .approval-side .approval-area .invoices-left span {
  font-weight: 600;
}
.approval-flex .approval-side > .approval-top {
  padding: var(--p) var(--p) 0 var(--p);
  text-align: center;
  box-sizing: border-box;
}
.approval-flex .approval-side > .approval-top .button svg {
  margin-right: 10px;
}
.approval-flex .approval-side > .approval-top .button svg path {
  fill: var(--white);
}
.approval-flex .approval-side .grid-flex {
  flex-direction: column;
}
.approval-flex .approval-side .grid-flex table.table td {
  font-size: 12px;
  line-height: 120%;
}
.approval-flex .approval-side .grid-flex table.table th {
  font-size: 10px;
}
.approval-flex .approval-side .grid-flex table.table th input[type="checkbox"] {
  transform: translateY(7 px);
}
.approval-flex .approval-side .grid-flex .adv-filters {
  border-right: none;
  padding-top: 5px;
  padding-bottom: 5px;
  width: 100%;
  box-sizing: border-box;
  border-radius: var(--br);
  padding-top: 20px;
}
.approval-flex .approval-side .grid-flex .grid-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-bottom: 0;
}
.approval-flex .approval-side .grid-flex .grid-header select {
  width: 90px;
  padding: 5px;
  height: 30px;
}
.approval-flex .approval-side .grid-flex .grid-header .sort-filter {
  padding-left: 10px;
}
.approval-flex .approval-side .grid-flex .grid-header .button.tertiary.small {
  height: 30px;
  width: 15px;
}
.approval-flex .approval-side .grid-flex .grid-header .paginator {
  gap: 5px;
}
.approval-flex .approval-side .grid-flex .grid-header .results-total > span:first-child {
  display: block;
  background: var(--primary);
  color: var(--white);
  padding: 5px 10px;
  border-radius: var(--br);
}
.approval-flex .approval-side .grid-flex .grid-header .results-total > span:first-child:after {
  content: ' Invs.';
}
.approval-flex .approval-side .grid-flex .grid-header .results-total > span:last-child {
  display: none;
}
.approval-flex .approval-side .grid-flex .grid-header .paginator select {
  display: none;
}
.approval-flex .approval-side .grid-flex .grid-header .paginator-range {
  display: none;
}
.approval-flex .approval-side .grid-flex .grid-header .paginator-range-buttons {
  gap: 0;
}
.approval-flex .approval-side .grid-flex .grid-header .paginator-range-actions .first-page,
.approval-flex .approval-side .grid-flex .grid-header .paginator-range-actions .page-number,
.approval-flex .approval-side .grid-flex .grid-header .paginator-range-actions .last-page {
  display: none;
}
.approval-flex .approval-side .grid-flex .grid-header .top-sorting > label {
  display: none;
}
.approval-flex .approval-side .grid-flex .grid-header .top-sorting + .button {
  display: none;
}
.paper {
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  background: var(--white);
  width: 100%;
  flex-grow: 1;
  padding: calc(var(--p) * 3) calc(var(--p) * 2);
  box-sizing: border-box;
  line-height: 120%;
}
.paper .company-name {
  font-size: 36px;
  font-weight: 600;
  margin-bottom: calc(var(--p) * 2);
}
.paper .paper-top {
  display: flex;
  width: 100%;
  margin-bottom: calc(var(--p) * 2);
}
.paper .paper-top > div {
  flex-grow: 1;
}
.paper .paper-top > div:last-child {
  text-align: right;
}
.paper .paper-top .invoice-title {
  font-size: 30px;
  font-weight: 600;
  margin-top: calc(var(--p) * 8.5);
}
.paper .paper-top .company-address {
  margin-bottom: calc(var(--p) * 2);
}
.paper .paper-top .contact-details > div {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}
.paper .paper-top .contact-details > div > div:first-child {
  font-weight: 600;
  width: 60px;
}
.paper .paper-middle {
  display: flex;
  width: 100%;
  border: 1px solid #ddd;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 30px;
}
.paper .paper-middle .title {
  margin-bottom: 15px;
}
.paper .paper-middle .paper-block:last-child {
  margin-bottom: 0;
}
.paper .paper-middle > div.paper-block:first-child {
  margin-bottom: 0;
}
.paper .paper-middle > div:last-child {
  flex-grow: 1;
  text-align: right;
}
.paper .invoice-to {
  border: 1px solid #ddd;
  padding: 20px;
}
.paper .paper-block {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--p);
}
.paper .paper-block > div:first-child {
  font-weight: 600;
}
.paper .paper-rows table {
  width: 100%;
}
.paper .paper-rows table th {
  background: #eee;
  padding: 5px 10px;
  text-align: left;
  font-weight: 600;
  font-size: 10px;
}
.paper .paper-rows table th.text-right {
  text-align: right;
}
.paper .paper-rows table td {
  padding: 10px;
  font-size: 12px;
}
.paper .paper-rows table td.text-right {
  text-align: right;
}
.paper .paper-rows table td a {
  text-decoration: underline;
}
.paper .paper-rows table td a:hover {
  background: lightyellow;
}
.paper .paper-rows table tbody td {
  border-bottom: 1px solid #ddd;
}
.paper .paper-rows table .tfoot-bg {
  background: #eee;
}
.paper .paper-rows table .tfoot-total {
  font-size: 20px;
  font-weight: 600;
  border-top: 1px solid #ddd;
}
.paper .paper-footer {
  display: flex;
  gap: 60px;
  margin-top: 100px;
}
.paper .paper-footer .title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px !important;
}
.paper .paper-footer .payment-details > div {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}
.paper .paper-footer .payment-details > div > div:first-child {
  font-weight: 600;
  width: 100px;
}
.paper .paper-bottom {
  margin-top: 100px;
}
.paper .paper-bottom p {
  margin-bottom: 10px;
}
.invoice-container {
  padding: var(--p);
  width: 100%;
  border: 1px solid var(--border);
  border-radius: var(--br);
}
.invoice-container > header .title {
  font-size: 14px;
}
input[type="file"] {
  position: absolute;
  z-index: -1;
  top: 0px;
  left: 0px;
  font-size: 17px;
  color: #b8b8b8;
  height: 100px;
}
.upload-wrap {
  position: relative;
}
.upload-text {
  background: var(--primary);
  color: var(--white);
  padding: calc(var(--p) / 1.5) var(--p);
  border-radius: calc(var(--br) / 2);
  border: none;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.05), 0 1px 0 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  height: 100px;
}
@media only screen and (max-width: 480px) {
  .upload-text {
    font-size: 0.9rem;
  }
}
.upload-text.inline-button {
  display: inline-flex;
}
.upload-text.drop-button {
  position: relative;
  padding-right: 30px;
}
.upload-text.drop-button:after {
  content: '';
  position: absolute;
  right: 15px;
  top: 19px;
  height: 6px;
  width: 6px;
  border-bottom: 1px solid var(--primary);
  border-right: 1px solid var(--primary);
  transform: rotate(45deg);
}
.upload-text.drop-button.small {
  padding-right: 30px;
}
.upload-text.drop-button.small:after {
  top: 13px;
}
.upload-text.drop-button.small:hover .drop-button-dropdown {
  top: 36px;
}
.upload-text.drop-button .drop-button-dropdown {
  display: none;
}
.upload-text.drop-button:hover {
  cursor: pointer;
}
.upload-text.drop-button:hover .drop-button-dropdown {
  display: block;
  top: 45px;
  right: 0px;
  position: absolute;
  width: 200px;
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  background: #fff;
}
.upload-text.drop-button:hover .drop-button-dropdown ul li {
  padding: 10px;
  border-bottom: 1px solid var(--border);
}
.upload-text.drop-button:hover .drop-button-dropdown ul li:last-child {
  border-bottom: none;
}
.upload-text.drop-button:hover .drop-button-dropdown ul li:hover {
  background: var(--bg);
  cursor: pointer;
}
.upload-text.drop {
  position: relative;
}
.upload-text.drop ul {
  top: 35px;
  left: 0px;
  display: none;
  position: absolute;
  width: 200px;
  background: var(--white);
  border-radius: var(--br);
  box-shadow: var(--shadow2);
  border: 1px solid var(--border);
}
.upload-text.drop ul li {
  border-bottom: 1px solid var(--border);
}
.upload-text.drop ul li:last-child {
  border-bottom: none;
}
.upload-text.drop ul li > div {
  padding: calc(var(--p) / 2);
}
.upload-text.drop:hover ul {
  display: block;
}
.upload-text.drop:hover ul li div {
  color: var(--primary);
}
.upload-text.drop:hover ul li div:hover {
  color: var(--secondary);
}
.upload-text:hover {
  box-shadow: inset 100px 100px 0 0 rgba(0, 0, 0, 0.1);
}
.upload-text.secondary-solid {
  background: #0f5ac1;
}
.upload-text.secondary-solid:hover {
  background: #0f5ac1;
  box-shadow: inset 100px 100px 0 0 rgba(0, 0, 0, 0.1);
}
.upload-text.secondary {
  color: var(--primary);
  border: 2px solid var(--primary);
  background: transparent;
}
.upload-text.secondary:hover {
  box-shadow: inset 0 0 2px 0 var(--primary);
}
.upload-text.tertiary {
  color: var(--primary);
  background: transparent;
}
.upload-text.tertiary:hover {
  background: var(--highlight);
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.05), 0 1px 0 0 rgba(0, 0, 0, 0.4);
}
.upload-text.inline {
  color: var(--primary);
  background: transparent;
  box-shadow: none;
}
.upload-text.inline:hover {
  background: var(--highlight);
}
.upload-text.small {
  padding: calc(var(--p) / 2) calc(var(--p) / 1.5);
  display: inline-flex;
  font-size: 14px;
  height: 40px;
}
.upload-text.tiny {
  padding: calc(var(--p) / 2) calc(var(--p) / 2);
  display: inline-flex;
  font-size: 12px;
  height: 23px;
}
.upload-text > svg {
  margin-right: 10px;
}
.upload-text > svg path {
  fill: var(--white);
}
.upload-text.secondary > svg path {
  fill: var(--primary);
}
.upload-text.warning {
  background: var(--orange);
  color: white;
}
.upload-text.delete {
  background: var(--red-fade);
  color: var(--red);
  font-size: 12px;
  padding: 10px;
  height: 30px;
}
.upload-text.delete-button {
  background: var(--red);
}
.upload-text.secondary-button {
  background: var(--secondary);
  color: white;
}
.upload-text.secondary-button:hover {
  background: orange;
}
.upload-text > svg {
  width: 15px;
  height: 15px;
}
.upload-text.save {
  background: var(--green);
}
.check-list .b {
  flex-direction: row-reverse;
  justify-content: flex-end;
  margin-bottom: 5px;
}
.row {
  display: flex;
  padding: calc(var(--p) / 2) var(--p);
  border-bottom: 1px solid var(--border);
  gap: 14px;
}
.row.hover {
  cursor: pointer;
}
.row.hover:hover {
  background: var(--bg);
}
.row:last-child {
  border-bottom: none;
}
.date-filters {
  display: flex;
  margin-bottom: var(--p);
  align-items: center;
  gap: 10px;
}
@media only screen and (max-width: 480px) {
  .date-filters {
    flex-direction: column;
    gap: 10px;
  }
}
.date-filters .dates {
  display: flex;
  gap: 10px;
}
@media only screen and (max-width: 480px) {
  .date-filters .dates {
    display: grid;
    width: 100%;
  }
}
.date-filters .date-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}
.date-filters .date-buttons > .button:first-child {
  width: 40px;
}
@media only screen and (max-width: 480px) {
  .date-filters .date-buttons > .button:nth-child(6) {
    grid-column: 2;
  }
}
@media only screen and (max-width: 480px) {
  .date-filters .date-buttons {
    display: grid;
    grid-template-columns: 40px 1fr 1fr 1fr 1fr;
    width: 100%;
    gap: 5px;
  }
  .date-filters .date-buttons > .button {
    padding: 5px;
    font-size: 10px;
    text-align: center;
  }
  .date-filters .date-buttons > .button:first-child {
    width: 100%;
  }
}
.date-filters input {
  font-family: 'Poppins', sans-serif;
  width: 200px;
}
@media only screen and (max-width: 480px) {
  .date-filters input {
    font-size: 10px;
    width: 100%;
  }
}
.date-filters .b {
  display: flex;
  flex-direction: row;
}
@media only screen and (max-width: 480px) {
  .date-filters .b {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
}
.date-filters .b label {
  width: auto;
  margin-left: 30px;
}
@media only screen and (max-width: 480px) {
  .date-filters .b label {
    margin-left: 0;
  }
}
.date-filters .b input {
  width: 130px;
}
@media only screen and (max-width: 480px) {
  .date-filters .b input {
    width: 140px;
    box-sizing: border-box;
  }
}
.date-filters .button.small.tertiary svg {
  height: 20px;
  margin-right: 0;
}
.date-filters .button.small.tertiary svg path {
  fill: var(--secondary);
}
.daterangepicker {
  position: fixed;
  color: inherit;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
  width: 278px;
  max-width: none;
  padding: 0;
  margin-top: 7px;
  top: 0;
  left: 0;
  right: auto;
  z-index: 3001;
  font-family: arial;
  font-size: 15px;
  line-height: 1em;
}
.daterangepicker:before,
.daterangepicker:after {
  position: absolute;
  display: inline-block;
  border-bottom-color: rgba(0, 0, 0, 0.2);
  content: '';
}
.daterangepicker:before {
  top: -7px;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #ccc;
}
.daterangepicker:after {
  top: -6px;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #fff;
  border-left: 6px solid transparent;
}
.daterangepicker.inline:before,
.daterangepicker.inline:after {
  content: none;
}
.daterangepicker.inline {
  position: inherit;
  display: inline-block;
}
.daterangepicker.opensleft:before {
  right: 9px;
}
.daterangepicker.opensleft:after {
  right: 10px;
}
.daterangepicker.openscenter:before {
  left: 0;
  right: 0;
  width: 0;
  margin-left: auto;
  margin-right: auto;
}
.daterangepicker.openscenter:after {
  left: 0;
  right: 0;
  width: 0;
  margin-left: auto;
  margin-right: auto;
}
.daterangepicker.opensright:before {
  left: 9px;
}
.daterangepicker.opensright:after {
  left: 10px;
}
.daterangepicker.drop-up {
  margin-top: -7px;
}
.daterangepicker.drop-up:before {
  top: initial;
  bottom: -7px;
  border-bottom: initial;
  border-top: 7px solid #ccc;
}
.daterangepicker.drop-up:after {
  top: initial;
  bottom: -6px;
  border-bottom: initial;
  border-top: 6px solid #fff;
}
.daterangepicker.single .daterangepicker .ranges,
.daterangepicker.single .drp-calendar {
  float: none;
}
.daterangepicker.single .drp-selected {
  display: none;
}
.daterangepicker.show-calendar .drp-calendar {
  display: block;
}
.daterangepicker.show-calendar .drp-buttons {
  display: block;
}
.daterangepicker.auto-apply .drp-buttons {
  display: none;
}
.daterangepicker .drp-calendar {
  display: none;
  max-width: 270px;
}
.daterangepicker .drp-calendar.left {
  padding: 8px 0 8px 8px;
}
.daterangepicker .drp-calendar.right {
  padding: 8px;
}
.daterangepicker .drp-calendar.single .calendar-table {
  border: none;
}
.daterangepicker .calendar-table .next span,
.daterangepicker .calendar-table .prev span {
  color: #fff;
  border: solid #000;
  border-width: 0 2px 2px 0;
  border-radius: 0;
  display: inline-block;
  padding: 3px;
}
.daterangepicker .calendar-table .next span {
  transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
}
.daterangepicker .calendar-table .prev span {
  transform: rotate(135deg);
  -webkit-transform: rotate(135deg);
}
.daterangepicker .calendar-table th,
.daterangepicker .calendar-table td {
  text-align: center;
  vertical-align: middle;
  min-width: 32px;
  width: 32px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid transparent;
  white-space: nowrap;
  cursor: pointer;
}
.daterangepicker .calendar-table {
  border: 1px solid #fff;
  border-radius: 4px;
  background-color: #fff;
}
.daterangepicker .calendar-table table {
  width: 100%;
  margin: 0;
  border-spacing: 0;
  border-collapse: collapse;
}
.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
  background-color: #eee;
  border-color: transparent;
  color: inherit;
}
.daterangepicker td.week,
.daterangepicker th.week {
  font-size: 80%;
  color: #ccc;
}
.daterangepicker td.disabled,
.daterangepicker option.disabled {
  color: #999;
  cursor: not-allowed;
  text-decoration: line-through;
}
.daterangepicker td.off,
.daterangepicker td.off.in-range,
.daterangepicker td.off.start-date,
.daterangepicker td.off.end-date {
  background-color: #fff;
  border-color: transparent;
  color: #999;
}
.daterangepicker td.in-range {
  background-color: #ebf4f8;
  border-color: transparent;
  color: #000;
  border-radius: 0;
}
.daterangepicker td.start-date {
  border-radius: 4px 0 0 4px;
}
.daterangepicker td.end-date {
  border-radius: 0 4px 4px 0;
}
.daterangepicker td.start-date.end-date {
  border-radius: 4px;
}
.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: #357ebd;
  border-color: transparent;
  color: #fff;
}
.daterangepicker th.month {
  width: auto;
}
.daterangepicker select.monthselect,
.daterangepicker select.yearselect {
  font-size: 12px;
  padding: 1px;
  height: auto;
  margin: 0;
  cursor: default;
}
.daterangepicker select.monthselect {
  margin-right: 2%;
  width: 56%;
}
.daterangepicker select.yearselect {
  width: 40%;
}
.daterangepicker select.hourselect,
.daterangepicker select.minuteselect,
.daterangepicker select.secondselect,
.daterangepicker select.ampmselect {
  width: 50px;
  margin: 0 auto;
  background: #eee;
  border: 1px solid #eee;
  padding: 2px;
  outline: 0;
  font-size: 12px;
}
.daterangepicker .calendar-time {
  text-align: center;
  margin: 4px auto 0 auto;
  line-height: 30px;
  position: relative;
}
.daterangepicker .calendar-time select.disabled {
  color: #ccc;
  cursor: not-allowed;
}
.daterangepicker .drp-buttons {
  clear: both;
  text-align: right;
  padding: 8px;
  border-top: 1px solid #ddd;
  display: none;
  line-height: 12px;
  vertical-align: middle;
}
.daterangepicker .drp-selected {
  display: inline-block;
  font-size: 12px;
  padding-right: 8px;
}
.daterangepicker .drp-buttons .btn {
  margin-left: 8px;
  font-size: 12px;
  font-weight: bold;
  padding: 4px 8px;
}
.daterangepicker.show-ranges.single.rtl .drp-calendar.left {
  border-right: 1px solid #ddd;
}
.daterangepicker.show-ranges.single.ltr .drp-calendar.left {
  border-left: 1px solid #ddd;
}
.daterangepicker.show-ranges.rtl .drp-calendar.right {
  border-right: 1px solid #ddd;
}
.daterangepicker.show-ranges.ltr .drp-calendar.left {
  border-left: 1px solid #ddd;
}
.daterangepicker .ranges {
  float: none;
  text-align: left;
  margin: 0;
}
.daterangepicker.show-calendar .ranges {
  margin-top: 8px;
}
.daterangepicker .ranges ul {
  list-style: none;
  margin: 0 auto;
  padding: 0;
  width: 100%;
}
.daterangepicker .ranges li {
  font-size: 12px;
  padding: 8px 12px;
  cursor: pointer;
}
.daterangepicker .ranges li:hover {
  background-color: #eee;
}
.daterangepicker .ranges li.active {
  background-color: #08c;
  color: #fff;
}
@media (min-width: 564px) {
  .daterangepicker {
    width: auto;
  }
  .daterangepicker .ranges ul {
    width: 140px;
  }
  .daterangepicker.single .ranges ul {
    width: 100%;
  }
  .daterangepicker.single .drp-calendar.left {
    clear: none;
  }
  .daterangepicker.single .ranges,
  .daterangepicker.single .drp-calendar {
    float: left;
  }
  .daterangepicker {
    direction: ltr;
    text-align: left;
  }
  .daterangepicker .drp-calendar.left {
    clear: left;
    margin-right: 0;
  }
  .daterangepicker .drp-calendar.left .calendar-table {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .daterangepicker .drp-calendar.right {
    margin-left: 0;
  }
  .daterangepicker .drp-calendar.right .calendar-table {
    border-left: none;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .daterangepicker .drp-calendar.left .calendar-table {
    padding-right: 8px;
  }
  .daterangepicker .ranges,
  .daterangepicker .drp-calendar {
    float: left;
  }
}
@media (min-width: 730px) {
  .daterangepicker .ranges {
    width: auto;
  }
  .daterangepicker .ranges {
    float: left;
  }
  .daterangepicker.rtl .ranges {
    float: right;
  }
  .daterangepicker .drp-calendar.left {
    clear: none !important;
  }
}
.daterangepicker-visibility-hidden {
  display: none;
}
.daterangepicker td.in-range,
.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
  background-color: var(--secondary);
}
.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: var(--primary);
}
.daterangepicker .drp-buttons .btn {
  background: var(--primary);
  color: var(--white);
  padding: calc(var(--p) / 1.5) var(--p);
  border-radius: calc(var(--br) / 2);
  border: none;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.05), inset 0 2px 5px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: calc(var(--p) / 2) calc(var(--p) / 1.5);
  display: inline-flex;
  font-size: 14px;
  height: 40px;
  color: var(--primary);
  background: transparent;
}
.daterangepicker .drp-buttons .btn:hover {
  background: var(--highlight);
}
.daterangepicker .drp-buttons .btn.btn-primary {
  background: var(--primary);
  color: var(--white);
}
.tonnage-table-container {
  overflow: auto;
}
.tonnage-table-container table th:first-child,
.tonnage-table-container table td:first-child {
  position: sticky;
  left: -1px;
  top: 0px;
  background: white;
}
.contract-text {
  font-size: 12px;
  margin-top: 5px;
  color: #333;
  font-weight: 600;
}
.reports-list {
  display: grid;
  grid-template-columns: 1fr;
  width: 100%;
  border: 1px solid var(--border);
  border-radius: var(--br);
}
.reports-list a {
  display: flex;
  padding: 20px;
  border-bottom: 1px solid var(--border);
  gap: 10px;
  align-items: center;
  font-weight: 600;
}
.reports-list a svg {
  width: 30px;
}
.reports-list a svg path {
  fill: var(--secondary);
}
.reports-list a:first-child {
  border-radius: var(--br) var(--br) 0 0;
}
.reports-list a:last-child {
  border-bottom: none;
  border-radius: 0 0 var(--br) var(--br);
}
.reports-list a:hover {
  background: var(--bg);
}
.chart-container {
  width: 80px;
  flex-grow: 0;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
}
.chart-container > * {
  border-radius: var(--br);
  background: var(--secondary);
  height: 14px;
}
.coming-soon-blocks {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
}
.coming-soon-blocks li a {
  background: var(--white);
  box-shadow: var(--shadow);
  padding: var(--p);
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  border: 1px solid var(--border);
  border-radius: var(--br);
  gap: 10px;
}
.initial-circle {
  border-radius: 50%;
  height: 30px;
  width: 30px;
  background: var(--primary);
  color: var(--white);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}
.attendance-group {
  border: 1px solid var(--border);
  margin-bottom: 10px;
  padding: calc(var(--p) / 1) var(--p);
  border-radius: var(--br);
}
.attendance-group .subtitle {
  margin-bottom: 10px;
}
.attendance-group .attendance-row-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.attendance-group .attendance-row-group .attendance-group--title {
  display: flex;
  gap: 10px;
  width: 200px;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}
.attendance-group .attendance-row-group .attendance-row--month-title {
  display: grid;
  gap: 2px;
  flex-grow: 1;
  grid-template-columns: repeat(30, 1fr);
}
.attendance-group .attendance-row-group .attendance-row--month-title > * {
  height: 25px;
  background: #fff;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 3px;
  padding: 5px;
  border-radius: calc(var(--br) / 2);
}
.attendance-group .attendance-row-group .attendance-row--month-title > * > *:first-child {
  font-weight: 600;
  font-size: 12px;
}
.attendance-group .attendance-row-group .attendance-row--month-title > * > *:last-child {
  font-size: 10px;
  color: var(--grey);
}
.attendance-group .attendance-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.attendance-group .attendance-row .attendance-staff {
  display: flex;
  gap: 10px;
  width: 200px;
  align-items: center;
}
.attendance-group .attendance-row .attendance-staff .attendance-staff--namejob {
  display: flex;
  flex-direction: column;
}
.attendance-group .attendance-row .attendance-staff .attendance-staff--name {
  font-weight: 600;
}
.attendance-group .attendance-row .attendance-staff .attendance-staff--job {
  font-size: 10px;
}
.attendance-group .attendance-row .attendance-row--month {
  display: grid;
  gap: 2px;
  flex-grow: 1;
  grid-template-columns: repeat(30, 1fr);
}
.attendance-group .attendance-row .attendance-row--month > * {
  height: 30px;
  background: #eee;
  display: flex;
  align-items: center;
  font-size: 10px;
  justify-content: center;
  position: relative;
  border-radius: calc(var(--br) / 2);
}
.attendance-group .attendance-row .attendance-row--month > *:hover {
  cursor: pointer;
  background: #ddd;
}
.attendance-group .attendance-row .attendance-row--month > *.selected {
  box-shadow: inset 0 0 0 2px var(--primary);
}
.attendance-group .attendance-row .attendance-row--month > *.selected .workforce-pop {
  display: flex;
}
.attendance-group .attendance-row .attendance-row--month > * .workforce-pop {
  position: absolute;
  display: none;
  background: var(--white);
  border-radius: var(--br);
  box-shadow: var(--shadow2);
  z-index: 9;
  padding: var(--p);
  border: 2px solid var(--primary);
  bottom: 37px;
  width: 200px;
  flex-direction: column;
  gap: 20px;
}
.attendance-group .attendance-row .attendance-row--month > * .workforce-pop:after {
  top: 100%;
  left: 50%;
  border: solid transparent;
  content: "";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(136, 183, 213, 0);
  border-top-color: var(--primary);
  border-width: 10px;
  margin-left: -10px;
}
.attendance-group .attendance-row .attendance-row--month > * .workforce-pop .date {
  color: var(--primary);
  font-size: 12px;
  font-weight: 600;
}
.attendance-group .attendance-row .attendance-row--month > * .workforce-pop .name {
  font-size: 16px;
  font-weight: 600;
}
.attendance-group .attendance-row .attendance-row--month > * .workforce-pop .clock {
  display: flex;
  gap: 20px;
}
.attendance-group .attendance-row .attendance-row--month > * .workforce-pop .clock > * {
  display: flex;
  gap: 5px;
  flex-direction: column;
  font-weight: 600;
}
.attendance-group .attendance-row .attendance-row--month > * .workforce-pop .clock > * > *:first-child {
  font-size: 20px;
}
.attendance-group .attendance-row .attendance-row--month > * .workforce-pop .clock > * svg {
  width: 15px;
  height: 15px;
  margin-left: 10px;
}
.attendance-group .attendance-row .attendance-row--month > * .workforce-pop .clock > * svg path {
  fill: var(--secondary);
}
.attendance-group .attendance-row .attendance-row--month > * .workforce-pop .button.small {
  width: 100%;
}
.attendance-group .attendance-row .attendance-row--month > *.a-holiday {
  background: var(--primary);
}
.attendance-group .attendance-row .attendance-row--month > *.a-holiday:after {
  content: 'H';
  background: rgba(0, 0, 0, 0.05);
  border-radius: 100px;
  padding: 3px 5px;
  color: var(--white);
}
.attendance-group .attendance-row .attendance-row--month > *.a-sickness {
  background: var(--red);
}
.attendance-group .attendance-row .attendance-row--month > *.a-sickness:after {
  content: 'S';
  background: rgba(0, 0, 0, 0.05);
  border-radius: 100px;
  padding: 3px 5px;
  color: var(--white);
}
.attendance-group .attendance-row .attendance-row--month > *.bank-holiday {
  background: var(--grey);
}
.attendance-group .attendance-row .attendance-row--month > *.bank-holiday:after {
  content: 'B';
  background: rgba(0, 0, 0, 0.05);
  border-radius: 100px;
  padding: 3px 5px;
  color: var(--white);
}
.attendance-group .attendance-row .attendance-row--month > *.clocked {
  background: var(--secondary);
}
.attendance-group .attendance-row .attendance-row--month > *.clocked > svg {
  width: 20px;
}
.attendance-group .attendance-row .attendance-row--month > *.clocked > svg path {
  fill: var(--white);
}
.attendance-row:nth-child(odd) .attendance-row--month > * {
  background: #f8f8f8;
}
.attendance-row:nth-child(odd) .attendance-row--month > *:hover {
  cursor: pointer;
  background: #ddd;
}
.month-spinner {
  border-radius: var(--br);
  border: 1px solid var(--border);
  display: inline-flex;
  overflow: hidden;
  margin-bottom: 10px;
}
.month-spinner > div {
  padding: var(--p);
  font-size: 20px;
  font-weight: 600;
}
.month-spinner > div:first-child,
.month-spinner > div:last-child {
  cursor: pointer;
}
.month-spinner > div:first-child:hover,
.month-spinner > div:last-child:hover {
  background: var(--bg);
}
.month-spinner > div:first-child {
  border-right: 1px solid var(--border);
}
.month-spinner > div:last-child {
  border-left: 1px solid var(--border);
}
.month-spinner > div:nth-child(2) {
  width: 160px;
  display: flex;
  justify-content: center;
}
@media only screen and (max-width: 480px) {
  .eticket-list table.table tbody tr {
    display: grid;
    grid-template-columns: 1.75fr 1fr 1fr;
    padding: 10px;
  }
  .eticket-list table.table tbody tr td {
    font-size: 12px;
    padding: 5px;
    align-items: center;
    display: flex;
  }
  .eticket-list table.table tbody tr td .small-reg-plate {
    width: 100%;
  }
  .eticket-list table.table tbody tr td:nth-child(5) {
    display: none;
  }
}
.rc-options {
  display: flex;
  border-radius: var(--br);
  border: 1px solid var(--border);
  background: var(--bg);
  padding: var(--p);
  align-items: center;
  display: inline-flex;
  gap: 10px;
  margin-bottom: var(--p);
}
.rc-options div.date-range {
  font-weight: 600;
}
@media only screen and (max-width: 480px) {
  .rc-options div.date-range {
    text-align: center;
  }
}
.route-calendar {
  display: grid;
  border-radius: var(--br);
  height: 100%;
  flex-grow: 1;
}
.route-calendar.rc-week {
  grid-template-columns: repeat(7, 1fr);
  gap: 10px;
}
@media only screen and (max-width: 480px) {
  .route-calendar.rc-week {
    grid-template-columns: 1fr;
  }
}
.route-calendar.rc-week > div {
  display: flex;
  flex-direction: column;
  gap: 10px;
  border-radius: calc(var(--br) / 2);
  height: 100%;
}
@media only screen and (max-width: 480px) {
  .route-calendar.rc-week > div {
    border-bottom: 1px solid var(--border);
    padding-bottom: 8px;
    border-radius: 0px;
  }
}
@media only screen and (max-width: 480px) {
  .route-calendar.rc-week > div.empty-day {
    padding-bottom: 0px;
    opacity: 0.3;
  }
}
.route-calendar.rc-week > div.current-day {
  background: var(--lightyellow);
  box-shadow: 0 0 0 5px var(--lightyellow), 0 0 0 7px var(--primary);
  border-bottom: none;
  border-radius: 10px;
}
.route-calendar .rc-header {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  border-radius: var(--br) 0 0 0;
}
.route-calendar .rc-header .print-route-buttons {
  display: flex;
  column-gap: 10px;
  flex-wrap: wrap;
}
.route-calendar .rc-header .button {
  font-size: 12px;
  padding: 5px 0px;
  font-weight: 600;
  height: 24px;
  border: none;
  box-shadow: none;
}
.route-calendar .rc-header .button:hover {
  text-decoration: underline;
}
.route-calendar .rc-header > *:first-child {
  color: var(--primary);
  font-size: 18px;
  font-weight: 600;
}
.route-calendar .rc-footer {
  padding: 5px;
}
.route-calendar .rc-footer > div {
  margin-bottom: calc(var(--p) / 2);
}
.route-calendar .rc-footer > div .subtitle {
  font-size: 14px;
  margin-bottom: 0px;
}
.route-calendar .rc-footer > div ul li {
  margin-bottom: 5px;
}
.route-calendar .rc-item-list {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 10px;
}
.route-calendar .rc-item-list .rc-item {
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  padding: 0px;
  display: flex;
  flex-direction: column;
  background: var(--white);
  font-size: 12px;
  cursor: pointer;
}
.route-calendar .rc-item-list .rc-item.DriverReturned .rc-vehicle:after {
  content: 'Driver Returned';
  background: var(--yellow);
  font-size: 10px;
  font-weight: 600;
  color: black;
  padding: 0 2px;
  border-radius: 2px;
  transform: translateX(5px);
  box-shadow: 0 0 0px 2px var(--yellow);
}
.route-calendar .rc-item-list .rc-item.Completed {
  background: var(--disabled);
}
.route-calendar .rc-item-list .rc-item.Completed .rc-vehicle {
  background: transparent !important;
  color: var(--primary);
}
.route-calendar .rc-item-list .rc-item.Completed .rc-vehicle:after {
  content: 'Completed';
  font-size: 10px;
  font-weight: 600;
  color: black;
  padding: 0 2px;
  border-radius: 2px;
  transform: translateX(5px);
}
.route-calendar .rc-item-list .rc-item.Completed .rc-vehicle[data-area="R1"],
.route-calendar .rc-item-list .rc-item.Completed .rc-vehicle[data-area="R2"],
.route-calendar .rc-item-list .rc-item.Completed .rc-vehicle[data-area="R3"],
.route-calendar .rc-item-list .rc-item.Completed .rc-vehicle[data-area="R4"],
.route-calendar .rc-item-list .rc-item.Completed .rc-vehicle[data-area="R5"],
.route-calendar .rc-item-list .rc-item.Completed .rc-vehicle[data-area="R6"] {
  background: transparent;
  color: #b41c1c;
}
.route-calendar .rc-item-list .rc-item:hover {
  cursor: pointer;
  border-color: var(--primary);
}
.route-calendar .rc-item-list .rc-item .rc-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  padding: 10px;
}
.route-calendar .rc-item-list .rc-item .rc-grid .rc-info {
  display: flex;
}
.route-calendar .rc-item-list .rc-item .rc-grid .rc-info svg {
  height: 10px;
  width: 20px;
}
.route-calendar .rc-item-list .rc-item .rc-grid .rc-info svg path {
  fill: var(--grey);
}
.route-calendar .rc-item-list .rc-item .rc-grid .rc-area {
  border: 1px solid var(--border);
  border-radius: 100px;
  padding: 10px;
  background: var(--bg);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  justify-self: flex-start;
}
.route-calendar .rc-item-list .rc-item .rc-grid .rc-area svg {
  height: 15px;
}
.route-calendar .rc-item-list .rc-item .rc-grid .rc-area svg path {
  fill: var(--primary);
}
.route-calendar .rc-item-list .rc-item .rc-vehicle {
  background: var(--primary);
  color: #fff;
  padding: 5px calc(var(--p) / 2);
  border-radius: calc(var(--br) / 2) calc(var(--br) / 2) 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.route-calendar .rc-item-list .rc-item .rc-vehicle > *:first-child {
  font-weight: 600;
}
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="Route 1"] {
  background: var(--primary);
}
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="Route 2"] {
  background: var(--secondary);
}
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="Route 3"] {
  background: #578310;
}
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="R1"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="R2"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="R3"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="R4"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="R5"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="R6"] {
  background: #b41c1c;
}
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="T1"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="T2"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="T3"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="T4"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="T5"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="T6"] {
  background: #666;
}
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="Route 5"] {
  background: #104b83;
}
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="Transit 1"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="Transit 2"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="Transit 3"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="Transit 4"],
.route-calendar .rc-item-list .rc-item .rc-vehicle[data-area="Transit 5"] {
  background: var(--grey);
}
.route-calendar .rc-item-list .rc-item .rc-vehicle.empty-route {
  background: white;
  color: var(--primary);
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1);
}
.route-calendar .rc-item-list .rc-item .rc-vehicle.empty-route[data-area="R1"],
.route-calendar .rc-item-list .rc-item .rc-vehicle.empty-route[data-area="R2"],
.route-calendar .rc-item-list .rc-item .rc-vehicle.empty-route[data-area="R3"],
.route-calendar .rc-item-list .rc-item .rc-vehicle.empty-route[data-area="R4"],
.route-calendar .rc-item-list .rc-item .rc-vehicle.empty-route[data-area="R5"],
.route-calendar .rc-item-list .rc-item .rc-vehicle.empty-route[data-area="R6"] {
  color: #b41c1c;
}
.route-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
}
@media only screen and (max-width: 480px) {
  .route-container {
    grid-template-columns: 1fr;
  }
}
@media only screen and (max-width: 480px) {
  .route-container .route-map {
    display: none;
  }
}
.route-container .route-map img {
  width: 100%;
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
}
.fake-input {
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 4);
  height: 46px;
  display: flex;
  align-items: center;
  padding: 10px;
  box-sizing: border-box;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: 0.2s all ease-out;
}
.fake-input:hover {
  background: var(--yellow2);
}
.money-collected-flex {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}
.money-collected-flex > section:last-child {
  margin-bottom: 20px;
}
.cash-grid {
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: center;
  width: 100%;
  margin-top: 10px;
}
.cash-grid > div {
  padding: 5px;
  width: 100%;
  flex-direction: column;
  gap: 5px;
  display: flex;
}
.cash-grid > div:nth-child(even) {
  background: var(--bg);
}
.cash-grid > div label {
  padding: 5px;
}
.cash-grid > div input {
  height: 24px;
}
.cash-grid > div.cash-balance {
  display: grid;
  background: var(--green);
  font-weight: 600;
  color: white;
  padding-top: 5px;
  grid-template-columns: 0.5fr 1fr;
}
.cash-grid > div.cash-balance > div {
  padding: 5px;
}
.cash-grid > div.cash-balance.red {
  background: var(--red);
}
.cash-grid .b {
  grid-template-columns: 0.5fr 1fr;
  align-items: center;
}
.photo-required {
  border-radius: 100px;
  background: var(--primary);
  padding: 5px 10px;
  color: var(--white);
  display: inline-flex;
  align-self: flex-start;
}
.signature-required {
  border-radius: 100px;
  background: var(--secondary);
  padding: 5px 10px;
  color: var(--white);
  display: inline-flex;
  align-self: flex-start;
}
.required-notes {
  display: flex;
  gap: 5px;
  margin-top: 5px;
  flex-wrap: wrap;
}
.required-notes .get-tonnage {
  grid-template-columns: 1fr 1fr 1fr;
  display: none;
  gap: 5px;
  margin-bottom: 10px;
}
.required-notes .get-tonnage > div {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
@media only screen and (max-width: 480px) {
  .required-notes .get-tonnage {
    display: grid;
  }
}
.required-notes .get-tonnage label {
  font-size: 10px;
}
.required-notes .get-tonnage input {
  width: 100%;
  border: 2px solid var(--secondary);
}
.required-notes .get-tonnage input[readonly] {
  background: var(--bg);
  border: 2px solid #ddd;
}
.stop-list {
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border);
}
.stop-list.haulier-route > div {
  border-bottom: 1px solid var(--border);
  display: grid;
  grid-template-columns: 20px 20px 58px 0.8fr 1fr 30px;
}
.stop-list.haulier-route > div.tip-stop {
  background: var(--bg);
  border-left: 10px solid #08382829;
}
.stop-list.haulier-route > div.tip-stop .route-address {
  padding-left: 15px;
  position: relative;
}
.stop-list.haulier-route > div.tip-stop .route-address:after {
  content: "";
  position: absolute;
  top: -20px;
  left: -10px;
  border-left: 2px solid var(--border);
  border-bottom: 2px solid var(--border);
  height: 30px;
  width: 15px;
}
@media only screen and (max-width: 480px) {
  .stop-list.haulier-route > div {
    grid-template-columns: 20px 58px 1fr;
  }
}
@media only screen and (max-width: 480px) {
  .stop-list.haulier-route > div div.route-notes-area {
    grid-column: 1 / -1;
  }
}
.stop-list.haulier-route .button.button-full {
  display: none;
}
@media only screen and (max-width: 480px) {
  .stop-list.haulier-route .button.button-full {
    display: flex;
  }
}
.stop-list.haulier-route .stop-notes {
  padding: 10px;
  background: var(--lightyellow);
  border: 1px solid #dbce94;
  border-radius: calc(var(--br) / 2);
}
.stop-list.haulier-route .stop-notes > div {
  font-weight: 600;
  margin-bottom: 5px;
}
@media only screen and (max-width: 480px) {
  .stop-list.haulier-route .kebab-menu {
    display: none;
  }
}
.stop-list .route-check input {
  width: 15px;
  height: 15px;
}
.stop-list.route-finished {
  position: sticky;
  top: -20px;
  left: 0;
  background: white;
  z-index: 999;
}
.stop-list.route-finished .b.check {
  padding-top: 0;
}
.stop-list.route-finished > div {
  padding: 0;
  grid-template-columns: 20px 45px 1fr 190px 80px 100px 125px 100px 50px 310px;
}
.stop-list.route-finished > div input {
  padding: 5px;
  margin-bottom: 5px;
}
.stop-list.route-finished > div .select2-container--default .select2-selection--single {
  height: 32px;
}
.stop-list.route-finished > div .select2-container--default .select2-selection--single .select2-selection__rendered {
  padding-top: 2px;
}
.stop-list.route-finished > div .select2-container--default .select2-selection--single .select2-selection__arrow {
  top: -6px;
}
.stop-list.route-finished > div .stop-list.route-finished > div .select2-container--default .select2-selection--single .select2-selection__rendered {
  height: 28px;
}
.stop-list.route-finished > div:first-child div {
  padding: 5px 0;
}
.stop-list.route-finished.and-completed > div {
  grid-template-columns: 20px 1fr 300px 80px 80px 100px 125px 100px 100px;
}
.stop-list.route-finished .returned-options div {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.stop-list.route-finished .returned-options div label {
  width: 200px;
  font-weight: 600;
}
.stop-list.route-finished .returned-options div select,
.stop-list.route-finished .returned-options div .b {
  width: 100%;
  flex-grow: 1;
}
.stop-list.route-finished .route-products {
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
}
.stop-list.route-finished .route-products > div {
  padding: 10px;
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
}
.stop-list.route-finished .route-products > div:last-child {
  border-bottom: none;
}
.stop-list.route-finished .route-products:hover {
  position: relative;
  cursor: pointer;
  background: var(--primary-fade);
  border-color: var(--primary);
}
.stop-list.route-finished .route-products:hover > div {
  border-bottom-color: var(--primary);
}
.stop-list.route-finished .route-products:hover:after {
  content: 'Edit';
  position: absolute;
  top: 100%;
  left: 50%;
  background: var(--primary-fade);
  color: var(--primary);
  padding: 5px 10px;
  border-radius: 0 0 calc(var(--br) / 3) calc(var(--br) / 3);
}
.stop-list > div {
  border-bottom: 1px solid var(--border);
  display: grid;
  grid-template-columns: 20px 20px 50px 1.2fr 1fr 50px 30px;
  padding: calc(var(--p) / 2);
  align-items: center;
  font-size: 12px;
  gap: 10px;
}
.stop-list > div .driver-returned-payment {
  padding: 5px;
}
.stop-list > div .stop-images {
  display: flex;
  width: 100%;
  gap: 5px;
  max-width: 500px;
  flex-wrap: wrap;
}
.stop-list > div .stop-images .signature-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 9px;
  justify-content: flex-end;
}
.stop-list > div .stop-images .signature-container .stop-image {
  height: 30px;
}
.stop-list > div .stop-images a {
  display: inline-flex;
  position: relative;
}
.stop-list > div .stop-images a .remove {
  height: 15px;
  width: 15px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 4px;
  right: 4px;
}
.stop-list > div .stop-images a .remove:hover > svg path {
  fill: var(--red);
}
.stop-list > div .stop-images a .remove > svg {
  height: 14px;
  width: 14px;
}
.stop-list > div .stop-images a .remove > svg path {
  fill: var(--primary);
}
.stop-list > div .stop-images .stop-image {
  height: 50px;
  width: 70px;
  border-radius: calc(var(--br) / 2);
  background-size: cover;
  border: 1px solid var(--border);
}
.stop-list > div .type-time {
  line-height: 140%;
}
.stop-list > div .type-time .haulier-fee {
  border: 1px solid var(--primary);
  color: var(--primary);
  border-radius: 5px;
  padding: 2px 5px;
  font-size: 10px;
  margin-top: 5px;
  display: inline-flex;
  font-weight: 600;
}
@media only screen and (max-width: 480px) {
  .stop-list > div .type-time .haulier-fee {
    display: none;
  }
}
.stop-list > div .button.inline.small {
  border: none;
}
.stop-list > div.fake-stop {
  border-bottom: none;
  padding: 0;
}
.stop-list > div.disabled {
  background: var(--bg);
}
.stop-list > div:last-child {
  border-bottom: none;
}
.stop-list > div:first-child {
  border-radius: calc(var(--br) / 2) calc(var(--br) / 2) 0 0;
}
.stop-list > div.visit {
  background: var(--secondary-fade);
}
@media only screen and (max-width: 480px) {
  .stop-list > div .move-route {
    display: none;
  }
}
.stop-list > div .move-route > * {
  background: var(--bg);
  border-radius: 50%;
  height: 20px;
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.stop-list > div .move-route > *:after {
  content: '>';
  font-size: 14px;
  transform: rotate(90deg);
}
.stop-list > div .move-route > *.move-route-up:after {
  transform: rotate(-90deg);
}
.stop-list > div .move-route > *:hover {
  cursor: pointer;
  background: var(--primary);
  color: #fff;
}
.stop-list > div:nth-child(2) .move-route > *:first-child {
  pointer-events: none;
  opacity: 0.3;
}
.stop-list > div:nth-child(2) .move-route > *:first-child:hover {
  cursor: default;
  background: var(--bg);
  color: var(--grey);
}
.stop-list > div:nth-last-child(2) .move-route > *:last-child {
  pointer-events: none;
  opacity: 0.3;
}
.stop-list > div:nth-last-child(2) .move-route > *:last-child:hover {
  cursor: default;
  background: var(--bg);
  color: var(--grey);
}
.stop-list > div .stop-number {
  background: var(--stop-color, var(--primary));
  color: #fff;
  border-radius: 100px;
  padding: 5px 10px;
  text-align: center;
  height: 24px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  font-weight: 600;
}
.stop-list > div .stop-number.start-stop {
  background: var(--green);
  padding: 5px 0px;
  font-size: 10px;
}
.stop-list > div .stop-number.end-stop {
  background: var(--red);
  padding: 5px 0px;
  font-size: 10px;
}
.stop-list > div > div.stop-address {
  flex-grow: 1;
  line-height: 120%;
}
.stop-list > div > div.stop-address .route-address {
  font-weight: 600;
}
.stop-list > div .stop-type {
  display: flex;
  align-items: center;
}
.stop-list > div .stop-type svg {
  height: 15px;
}
.stop-list > div .stop-type svg path {
  fill: var(--grey);
}
.choose {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 400px;
  overflow: auto;
}
.choose > div {
  border: 1px solid var(--border);
  padding: 10px;
  gap: 10px;
  display: grid;
  grid-template-columns: 26px 1fr 1fr 1fr 1fr;
  align-items: center;
}
.choose > div.selected {
  border-color: var(--primary);
  box-shadow: 0 0 2px 0 var(--primary);
}
.choose > div.selected:hover {
  border-color: var(--primary);
  cursor: default;
}
.choose > div input[type="checkbox"] {
  height: 20px;
  width: 20px;
}
.choose > div > div {
  text-align: left;
}
.choose > div > div:not(:first-child) {
  flex-grow: 1;
}
.choose > div > div:nth-child(2) {
  font-weight: 600;
}
.choose > div > div:last-child {
  text-align: right;
  font-size: 10px;
}
.choose > div:hover {
  border-color: var(--secondary);
  cursor: pointer;
}
.pill-choose {
  border: 1px solid var(--border);
  border-radius: 100px;
  padding: 10px;
  background: var(--bg);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  justify-self: flex-start;
}
.pill-choose svg {
  height: 15px;
}
.pill-choose svg path {
  fill: var(--primary);
}
.totals-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-width: 500px;
  margin-left: auto;
}
.totals-grid > * {
  padding: 10px;
}
.totals-grid > *:nth-child(even) {
  text-align: right;
}
.totals-grid > *.total {
  font-weight: 600;
  font-size: 16px;
  border-top: 2px solid var(--border);
}
.add-line-row {
  display: flex;
  padding: calc(var(--p) / 2) 25px;
  background: var(--bg);
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  justify-content: space-between;
}
.add-line-row .b {
  padding: 0;
}
.add-line-row .b label {
  transform: translateY(2px);
}
.add-line-row-haulier {
  display: flex;
  padding: calc(var(--p) / 2) 25px;
  background: var(--bg);
  border-top: 1px solid var(--border);
  flex-direction: row !important;
  border-bottom: 1px solid var(--border);
  justify-content: space-between;
  align-items: center;
}
.add-line-row-haulier .b {
  width: 100%;
}
.add-line-row-haulier .button {
  flex-shrink: 0;
}
.order-dropdown-container {
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  position: relative;
}
.order-dropdown-container .button-text {
  white-space: nowrap;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
}
.order-dropdown-container .order-dropdown {
  display: none;
  position: absolute;
  top: 30px;
  right: 0px;
  background: var(--white);
  box-shadow: var(--shadow);
  width: 400px;
  padding: 5px;
  z-index: 99;
}
.order-dropdown-container .order-dropdown > div:first-child {
  padding: 10px;
  background: var(--bg);
  font-weight: 600;
}
.order-dropdown-container .order-dropdown table tr:hover {
  background: transparent;
}
.order-dropdown-container:hover {
  box-shadow: var(--shadow);
}
.order-dropdown-container:hover .order-dropdown {
  display: flex;
  flex-direction: column;
}
.delivery-area {
  background: var(--bg);
  border-radius: calc(var(--br) / 4);
  flex-grow: 1;
  padding: 10px;
  border: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.delivery-area .delivery-area-card-container {
  display: flex;
  gap: 10px;
}
.delivery-area .delivery-area-card-container > div:last-child {
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: flex-start;
  justify-content: center;
}
.delivery-area .delivery-area-card-container > div:last-child .button {
  padding: 5px 10px;
  height: 30px;
  font-size: 14px;
}
.delivery-area .delivery-area-card {
  padding: 15px;
  border-radius: var(--br);
  background: var(--white);
  display: flex;
  gap: 10px;
  flex-direction: column;
  border: 1px solid var(--border);
}
.delivery-area .delivery-area-card > div:first-child {
  font-size: 16px;
  font-weight: 600;
}
.page-break {
  page-break-after: always;
}
.customer-balance {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 10px;
  row-gap: 20px;
  padding: 0;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border);
  justify-content: space-between;
}
.customer-balance + .grid-flex {
  margin-bottom: 10px;
}
.customer-balance.bad {
  background: var(--red);
}
.customer-balance > div:nth-last-child(2) {
  margin-bottom: 20px;
}
.customer-balance > div:last-child {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--green);
}
.customer-balance > div:last-child.negative {
  color: var(--red);
}
.latlong {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}
.latlong .b {
  display: flex;
  gap: 10px;
  align-items: center;
}
.latlong .b label {
  color: var(--grey);
}
.latlong .b input {
  padding: 0px;
  background: transparent;
  border: none;
  box-shadow: none;
  width: 100px;
  color: var(--grey);
}
.inline-balance {
  color: var(--white);
  padding: 5px 10px;
  border-radius: 50px;
  display: inline-flex;
  font-weight: 600;
}
.inline-balance.positive {
  background: var(--green);
}
.inline-balance.negative {
  background: var(--red);
}
.media-folder-list {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.media-folder {
  border: 1px solid var(--border);
  padding: var(--p);
  border-radius: var(--br);
  cursor: pointer;
}
.media-folder:hover {
  border-color: var(--primary);
}
.search-address {
  display: flex;
  gap: 10px;
  align-items: center;
}
.search-address input {
  font-family: 'Poppins', sans-serif;
}
.suggestion-title {
  color: var(--primary);
  font-weight: 600;
}
.suggestions {
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 3);
  max-height: 300px;
  overflow: auto;
  grid-column: span 2;
}
.suggestions > div {
  border-bottom: 1px solid var(--border);
  padding: 10px;
}
.suggestions > div:first-child {
  border-radius: calc(var(--br) / 3) calc(var(--br) / 3) 0 0;
}
.suggestions > div:last-child {
  border-bottom: none;
  border-radius: 0 0 calc(var(--br) / 3) calc(var(--br) / 3);
}
.suggestions > div:hover {
  background: var(--primary);
  color: var(--white);
  cursor: pointer;
}
.markdown-preview-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}
.markdown-preview-container > div {
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  padding: var(--p);
}
.markdown-preview-container > div h1 {
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 20px;
}
.markdown-preview-container > div h2 {
  font-size: 26px;
  font-weight: 600;
  margin-bottom: 20px;
}
.markdown-preview-container > div h3 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 20px;
}
.markdown-preview-container > div h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
}
.markdown-preview-container > div h5 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 20px;
}
.markdown-preview-container > div h6 {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 20px;
}
.markdown-preview-container > div p {
  line-height: 120%;
  margin-bottom: 20px;
}
.markdown-preview-container > div ul,
.markdown-preview-container > div ol {
  margin-left: 20px;
  margin-bottom: 20px;
}
.markdown-preview-container > div ul li,
.markdown-preview-container > div ol li {
  list-style-type: disc;
  margin-bottom: 5px;
}
.markdown-preview-container > div a {
  color: blue;
}
section.tonnage-report .dates input {
  max-width: 200px;
}
section.tonnage-report .subtitle {
  border-bottom: none;
}
section.tonnage-report .table {
  min-width: 600px;
  border: 1px solid var(--border);
}
section.tonnage-report .table td,
section.tonnage-report .table th {
  border: 1px solid var(--border);
  padding: 10px;
}
section.tonnage-report .table th {
  font-weight: 600;
  border-bottom: 3px solid var(--border);
  color: var(--black);
  text-align: left;
}
section.tonnage-report .table td:first-child {
  background: var(--bg);
  font-weight: 600;
}
section.tonnage-report .table .totals-row {
  background: var(--bg);
  border-top: 2px solid var(--border);
  font-weight: 600;
}
.block-builder-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  padding: 20px;
  background: #fff;
}
.block-builder-container .block-builder-header {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}
.block-builder-container .block-builder-header .b {
  flex-grow: 1;
}
.block-builder-container > .buttons {
  display: flex;
  justify-content: flex-end;
}
.block-builder-container .block-builder {
  border: 2px solid var(--primary);
  border-radius: calc(var(--br) / 2);
  padding: 20px;
}
.block-chooser {
  border-radius: var(--br);
  background: var(--bg);
  border: 1px solid var(--border);
  padding: var(--p);
  margin-bottom: var(--p);
}
.basic-list {
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  margin-bottom: 20px;
}
.basic-list > * {
  padding: 10px;
  border-bottom: 1px solid var(--border);
  font-weight: 600;
}
.basic-list > *:last-child {
  border-bottom: none;
}
.sample-limit {
  padding: 20px;
  border-radius: var(--br);
  color: white;
  font-weight: 600;
}
.sample-limit.sample-limit--warning {
  background: #e47a00;
}
.sample-limit.sample-limit--exceeded {
  background: var(--red);
}
.reconnect-modal {
  background-color: rgba(0, 0, 0, 0.75);
  color: white;
  text-align: center;
  padding: 2rem;
  border-radius: 10px;
}
.reconnect-modal h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}
.reconnect-modal button {
  background-color: #007bff;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}
.inline-error {
  background: var(--red);
  color: white;
  padding: 20px;
  box-sizing: border-box;
  border-radius: var(--br);
  width: 100%;
}
.create-order-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 100px 100px;
  gap: 20px;
  margin-bottom: 20px;
}
.create-order-grid > * {
  border: 1px solid var(--border);
  border-radius: var(--br);
  overflow: auto;
}
.create-order-grid > *:first-child {
  grid-row: 1 / 3;
}
.create-order-grid > *:nth-child(2) {
  grid-row: 1 / 2;
}
.create-order-grid > * .customer-balance {
  border-bottom: none;
  padding: 10px;
}
.create-order-grid > * .subtitle {
  font-size: 14px;
  padding-left: 10px;
  text-align: center;
  border-bottom: 1px solid var(--border);
  background: var(--bg);
  margin-bottom: 0;
}
.create-order-grid > * table.table thead tr th {
  padding: 5px;
  font-size: 10px;
}
.create-order-grid > * table.table thead tr th:last-child {
  text-align: right;
}
.create-order-grid > * table.table tbody tr td {
  padding: 5px;
  font-size: 12px;
}
.create-order-grid > * table.table tbody tr td:last-child {
  text-align: right;
}
.create-order-grid.create-order-grid--header {
  margin-bottom: 20px;
}
.create-order-grid.create-order-grid--header > * {
  height: auto;
  display: flex;
  flex-direction: column;
}
.create-order-grid.create-order-grid--header > * > div:last-child {
  padding: 10px;
  display: flex;
  align-items: center;
  height: 100%;
  gap: 5px;
  flex-wrap: wrap;
}
.rounds-table {
  width: 100%;
}
.rounds-table tr {
  background: var(--lightyellow);
}
.rounds-table tr:nth-last-child(2) td {
  border-bottom: none;
}
.rounds-table tr.last-occurrence {
  font-weight: bold;
  background-color: #f0f0f0;
  color: red;
}
.rounds-table tr.old-customer {
  background: white;
}
.rounds-table tr.will-ring-row {
  background: hsla(214.74, 82.61%, 31.57%, 0.13);
  opacity: 0.8;
}
.rounds-table tr.will-ring-row .future-deliveries {
  display: table-cell;
}
.rounds-table tr.next-booked {
  background: var(--green-fade);
}
.rounds-table tr th {
  padding: 10px;
  border-bottom: 2px solid var(--border);
  text-align: left;
  font-weight: 600;
  font-size: 10px;
}
.rounds-table tr td {
  padding: 10px;
  vertical-align: middle;
  border-bottom: 1px solid var(--border);
  font-size: 12px;
}
.rounds-table tr td.click-customer:hover {
  cursor: pointer;
  text-decoration: underline;
}
.rounds-table tr td:first-child {
  font-weight: 600;
  font-size: 13px;
  line-height: 120%;
}
.rounds-table tr td.will-ring {
  color: var(--red);
}
.rounds-table tr td:nth-child(5) {
  font-size: 12px;
}
.rounds-table tr td.booked {
  color: var(--green);
  font-weight: 600;
}
.rounds-table tr td.booked:after {
  content: 'Booked';
  color: var(--green);
  background: rgba(0, 255, 115, 0.15);
  padding: 5px 7px;
  border-radius: 3px;
  margin-left: 5px;
}
.rounds-table tr td .button {
  font-size: 10px;
  display: inline-flex;
  padding: 5px 2px 4px 2px;
  white-space: nowrap;
}
.rounds-table tr td .button.button-inline {
  padding: 0;
}
.rounds-table tr td .rounds-buttons {
  display: flex;
  gap: 10px;
}
.rounds-table thead tr {
  background: white;
}
.rounds-table thead tr > th:last-child {
  width: 305px;
}
.rounds-filters {
  padding: 20px 0;
  display: flex;
  gap: 10px;
}
.rounds-filters + .grid-header {
  border-bottom: none;
  width: 450px;
}
.rounds-filters + .grid-header .sort-filter {
  padding-left: 0;
}
.rounds-filters + .grid-header .button svg {
  margin-right: 0;
}
.rounds-filters input {
  max-width: 200px;
}
.rounds-filters .rounds-filter {
  border: 2px solid var(--border);
  background: var(--white);
  padding: 10px;
  border-radius: calc(var(--br) / 2);
  display: flex;
  align-items: center;
  cursor: pointer;
}
.rounds-filters .rounds-filter.selected {
  background: var(--green);
  color: var(--white);
}
.rounds-filters .rounds-filter:nth-child(n+3):nth-child(odd) {
  border-color: var(--primary);
}
.rounds-filters .rounds-filter:nth-child(n+3):nth-child(odd).selected {
  background: var(--primary);
  color: var(--white);
}
.rounds-filters .rounds-filter:nth-child(n+3):nth-child(even) {
  border-color: var(--red);
}
.rounds-filters .rounds-filter:nth-child(n+3):nth-child(even).selected {
  background: var(--red);
  color: var(--white);
}
.switch {
  border-radius: 100px;
  border: 1px solid var(--border);
  padding: 4px;
  width: 50px;
  height: 26px;
  box-sizing: border-box;
  display: flex;
  cursor: pointer;
}
.switch.small {
  height: 16px;
  width: 30px;
  padding: 2px;
  margin-top: 5px;
  margin-bottom: 5px;
}
.switch.small:before {
  height: 10px;
  width: 10px;
}
.switch:before {
  content: '';
  height: 16px;
  width: 16px;
  border-radius: 50%;
}
.switch.on {
  justify-content: flex-end;
}
.switch.on:before {
  background: var(--green);
}
.switch.off {
  justify-content: flex-start;
}
.switch.off:before {
  background: var(--red);
}
#map {
  height: 100% !important;
  min-height: 600px !important;
}
@media print {
  .sidebar {
    display: none !important;
  }
  main {
    overflow: visible;
  }
  main > .b {
    display: none !important;
  }
  main > header {
    display: none !important;
  }
  .grid-flex .adv-filters {
    display: none !important;
  }
  .select2-results {
    display: none !important;
  }
  .grid-header {
    display: none !important;
  }
  .grid-main table.table tbody tr td:nth-child(2),
  .grid-main table.table thead tr th:nth-child(2),
  .grid-main table.table tbody tr td:nth-child(1),
  .grid-main table.table thead tr th:nth-child(1) {
    display: none !important;
  }
}
.ledger-grid {
  margin-bottom: 20px;
}
.ledger-grid .grid-flex {
  overflow: hidden;
}
.ledger-grid .grid-header {
  margin-bottom: 0;
  border-radius: var(--br) var(--br) 0 0;
}
.ledger-grid table.table tbody tr td,
.ledger-grid table.table thead tr th {
  padding: 5px;
}
.ledger-grid table.table tbody tr td:nth-child(4),
.ledger-grid table.table thead tr th:nth-child(4),
.ledger-grid table.table tbody tr td:nth-child(5),
.ledger-grid table.table thead tr th:nth-child(5),
.ledger-grid table.table tbody tr td:nth-child(6),
.ledger-grid table.table thead tr th:nth-child(6),
.ledger-grid table.table tbody tr td:nth-child(7),
.ledger-grid table.table thead tr th:nth-child(7) {
  text-align: right;
}
.ledger-grid table.table tbody tr td:nth-child(3),
.ledger-grid table.table thead tr th:nth-child(3) {
  font-size: 12px;
}
.ledger-grid table.table tbody tr:hover {
  cursor: default;
}
.ledger-grid table.table tbody tr:first-child {
  font-weight: 600;
}
.ledger-grid td.ledger-order {
  background-color: var(--primary-fade);
}
.ledger-grid td.ledger-payment {
  background-color: var(--secondary-fade);
}
.ledger-grid td.ledger-future {
  background-color: var(--green-fade);
}
.ledger-grid .ledger-order-line {
  display: grid;
  align-items: center;
  grid-template-columns: 1fr 75px 100px;
}
.ledger-grid .ledger-order-line > div:last-child {
  text-align: right;
}
.balance-ledger-container {
  display: grid;
  gap: 20px;
  grid-template-columns: 300px 1fr;
  align-items: flex-start;
}
.customer-details-container {
  display: grid;
  gap: 20px;
  grid-template-columns: 1fr 1fr;
}
strong.inactive-status {
  color: #ccc;
  text-decoration: line-through;
}
strong.deleted-status {
  color: var(--red);
  text-decoration: line-through;
}
.route-order {
  display: grid;
  grid-template-columns: 1fr 50px;
  margin-right: 10px;
  gap: 5px;
}
.route-order .route-order-line {
  display: grid;
  grid-column: 1 / -1;
  align-items: center;
  grid-template-columns: subgrid;
}
.route-order .route-order-line > div:last-child {
  text-align: right;
}
.route-order .route-order-line > div:last-child:before {
  content: '\2715 ';
  margin-right: 5px;
  font-size: 8px;
  color: var(--grey);
}
.future-deliveries {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  width: 305px;
}
.future-deliveries > div {
  border-left: 1px solid var(--border);
  padding: 5px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: center;
  width: 50px;
}
.future-deliveries > div.span-columns {
  width: 100%;
  color: var(--grey);
  grid-column: 1 / -1;
}
.future-deliveries > div.future-sorted .button {
  background: transparent !important;
  color: black;
}
.future-deliveries > div .button:nth-child(2) {
  padding: 5px 2px 4px 2px;
}
.future-deliveries .button {
  padding: 3px;
  font-size: 11px;
  width: 100%;
  box-sizing: border-box;
}
.future-deliveries .button.button-miss {
  background: var(--secondary);
}
.future-deliveries .button.button-change {
  background: var(--bg);
  color: black;
}
.booked {
  background: var(--green-fade);
  color: var(--green);
  display: flex;
  width: 100%;
  padding: 4px;
  gap: 5px;
  border-radius: calc(var(--br) / 2);
  justify-content: center;
  box-sizing: border-box;
}
.booked.booked-differently {
  background: var(--purple-fade);
}
.booked.booked-differently svg path {
  fill: var(--purple);
}
.booked svg {
  height: 14px;
  width: 14px;
}
.booked svg path {
  fill: var(--green);
}
.missed {
  background: var(--secondary-fade);
  color: var(--secondary);
  display: flex;
  padding: 4px;
  box-sizing: border-box;
  width: 100%;
  gap: 5px;
  font-size: 14px;
  font-weight: 600;
  border-radius: calc(var(--br) / 2);
  justify-content: center;
}
.endofday {
  border-bottom: 4px dashed var(--grey);
}
.endofday:last-child {
  display: none;
}
.subtle-link {
  font-weight: 600;
}
.subtle-link.future-date {
  font-size: 12px;
  display: inline-flex;
  gap: 5px;
}
.subtle-link:hover {
  cursor: pointer;
  text-decoration: underline;
}
.ledger-date {
  display: inline-flex;
  gap: 5px;
}
.view-order-container {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  margin-bottom: 20px;
}
.view-order-container > * {
  gap: 20px;
}
@media only screen and (max-width: 1570px) {
  .view-order-container {
    grid-template-columns: 1fr;
  }
}
.view-order-container section.bs.np {
  margin-bottom: 0;
}
.inline-header-info {
  font-size: 12px;
  font-weight: 400;
  color: var(--grey);
  display: flex;
  gap: 10px;
  flex-grow: 1;
  justify-content: flex-end;
}
.inline-header-info .b {
  display: flex;
  align-items: center;
}
.inline-header-info .b input {
  height: 32px;
}
.tag-container {
  margin: 20px 0;
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}
.display-inline {
  line-height: 120%;
  font-weight: 600;
}
.edit-note-container {
  display: grid;
  align-items: center;
  gap: 10px;
  position: relative;
}
.edit-note-container .button {
  bottom: 6px;
  right: 6px;
  margin-top: 10px;
  position: absolute;
  z-index: 2;
  border-color: #776204;
  color: #776204;
  font-size: 14px;
  padding: 2px 8px;
  height: 30px;
}
.edit-note-container .button:hover {
  border-color: #776204;
  background: hsla(49, 93%, 24%, 0.1);
  color: #776204;
}
.edit-note-container .button.save {
  background: #776204;
  color: white;
}
.edit-note-container .button.save:hover {
  border-color: #4e4003;
}
.customer-results-container {
  border: 1px solid var(--border);
  border-radius: var(--br);
  grid-template-columns: 100px 1fr 250px 1fr 300px;
  background: var(--white);
  display: grid;
  position: absolute;
  z-index: 9;
  top: 70px;
}
.customer-results-container.haulier-results {
  grid-template-columns: 100px 1fr 1fr;
  min-width: 1000px;
}
.customer-results-container .customer-result-grid {
  border-bottom: 1px solid var(--border);
  display: grid;
  grid-column: 1 / -1;
  grid-template-columns: subgrid;
  align-items: center;
  gap: 10px;
}
.customer-results-container .customer-result-grid:last-child {
  border-bottom: none;
}
.customer-results-container .customer-result-grid:hover {
  color: var(--primary);
  cursor: pointer;
  background: var(--primary-fade);
}
.customer-results-container .customer-result-grid > div {
  padding: 10px;
}
.customer-results-container .customer-result-grid > div:nth-child(2) {
  font-weight: 600;
}
.customer-results-container .customer-result-grid > div:last-child {
  display: flex;
  gap: 5px;
}
.create-order-title {
  display: flex;
  gap: 20px;
  padding: 10px;
  align-items: center;
}
.create-order-title > .title {
  flex-grow: 1;
}
.blue-route svg {
  width: 10px;
  height: 10px;
}
.blue-route svg path {
  fill: var(--primary);
}
.red-route svg {
  width: 10px;
  height: 10px;
}
.red-route svg path {
  fill: var(--red);
}
.dual-inputs {
  display: flex;
}
.info {
  background: var(--lightyellow);
  padding: 20px;
  border-radius: var(--br);
}
.product-out {
  background: #333;
  color: white;
  font-size: 10px;
  padding: 5px 10px;
  border-radius: 50px;
  display: inline-flex;
  margin-left: 5px;
  gap: 5px;
}
.product-out svg {
  height: 10px;
  width: 10px;
}
.product-out svg path {
  fill: #fff;
}
.kebab-menu {
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  position: relative;
  cursor: pointer;
  height: 21px;
  width: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.kebab-menu:hover {
  background: var(--bg);
  z-index: 999;
}
.kebab-menu .kebab {
  transform: translateY(-5px) translateX(-0.5px);
  position: relative;
  height: 3px;
  width: 3px;
  background: var(--primary);
  border-radius: 50%;
}
.kebab-menu .kebab:before {
  content: '';
  position: absolute;
  height: 3px;
  width: 3px;
  background: var(--primary);
  border-radius: 50%;
  top: 5px;
}
.kebab-menu .kebab:after {
  content: '';
  position: absolute;
  height: 3px;
  width: 3px;
  background: var(--primary);
  border-radius: 50%;
  top: 10px;
}
.kebab-menu ul {
  display: none;
  position: absolute;
  top: 20px;
  right: 0px;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  width: 140px;
  background: white;
  border-radius: var(--br);
}
.kebab-menu:hover ul {
  display: flex;
  flex-direction: column;
}
.kebab-menu:hover ul li {
  padding: 10px;
  border-bottom: 1px solid var(--border);
}
.kebab-menu:hover ul li:last-child {
  border-bottom: none;
  border-radius: 0 0 var(--br) var(--br);
}
.kebab-menu:hover ul li:first-child {
  border-radius: var(--br) var(--br) 0 0;
}
.kebab-menu:hover ul li:hover {
  background: var(--bg);
}
.generate-buttons {
  width: 100%;
  border: 1px solid var(--border);
  border-radius: var(--br);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-sizing: border-box;
  margin-bottom: 10px;
}
.generate-buttons > *.button.tertiary {
  margin-left: auto;
}
.empty-invoice-container {
  color: var(--grey);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  gap: 30px;
  font-weight: 600;
}
.empty-invoice-container svg {
  height: 40px;
  width: 40px;
}
.empty-invoice-container svg path {
  fill: var(--grey);
}
.order-summary-lines {
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
}
.order-summary-lines > * {
  padding: 5px;
  border-bottom: 1px solid var(--border);
  font-weight: 600;
}
.order-summary-lines > *:last-child {
  border-bottom: none;
}
.round-info {
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
}
.round-info > * {
  padding: 5px;
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
}
.round-info > *:last-child {
  border-bottom: none;
}
.button.button-inline {
  padding: 0px;
  background: transparent;
  border: none;
  color: var(--primary);
  box-shadow: none;
}
.button.button-inline:hover {
  background: white;
  color: var(--secondary);
}
.waste-product-display-column {
  grid-column: 2 / 4;
}
.mobile-add-buttons {
  display: none;
}
@media only screen and (max-width: 480px) {
  .mobile-add-buttons {
    display: flex;
    gap: 5px;
    padding: 5px 0;
    justify-content: flex-end;
  }
  .mobile-add-buttons .button-full {
    padding: 5px 10px;
    text-align: center;
    height: 40px;
  }
}
.waste-colours .dash-rows .dash-row .count {
  background: var(--brown);
}
.waste-colours .chart-container > * {
  background: var(--brown-secondary);
}
.waste-colours .ancillary-info {
  color: var(--brown-secondary);
}
.waste-colours .stat-row .stat-icon svg path {
  fill: var(--brown);
}
.waste-colours .stat > div:first-child {
  color: var(--brown);
}
.order-colours .dash-rows .dash-row .count {
  background: var(--grey);
}
.order-colours .chart-container > * {
  background: var(--grey-secondary);
}
.order-colours .ancillary-info {
  color: var(--grey-secondary);
}
.order-colours .stat-row .stat-icon svg path {
  fill: var(--grey);
}
.order-colours .stat > div:first-child {
  color: var(--grey);
}
.route-colours .dash-rows .dash-row .count {
  background: var(--blue);
}
.route-colours .chart-container > * {
  background: var(--blue-secondary);
}
.route-colours .ancillary-info {
  color: var(--blue-secondary);
}
.route-colours .stat-row .stat-icon svg path {
  fill: var(--blue);
}
.route-colours .stat > div:first-child {
  color: var(--blue);
}
.stat-row {
  background: var(--bg2);
}
.top-products-dash {
  display: grid;
  gap: 10px;
  padding: 10px;
  grid-template-columns: 1fr 1fr;
  border-bottom: 1px solid var(--border);
  background: var(--bg2);
}
.top-products-dash .dash-row {
  border: 1px solid var(--border);
  border-radius: var(--br);
  background: var(--white);
}
.top-products-dash .dash-row:last-child {
  border-bottom: 1px solid var(--border) !important;
}
section.bs.np + .dialog-overlay {
  position: fixed;
}
.night-log {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.night-log .night-button {
  border-radius: var(--br);
  padding: 50px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  gap: 10px;
  flex-direction: column;
}
.night-log .night-button svg path {
  fill: white;
}
.night-log .night-button.nb-in {
  background: var(--primary);
  color: var(--white);
}
.night-log .night-button.nb-out {
  background: var(--secondary);
  color: var(--white);
}
.night-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.night-form .night-block {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.night-form .night-block label {
  font-weight: 600;
}
.night-form .night-block input,
.night-form .night-block select {
  height: 50px;
  font-size: 30px;
}
.night-form .night-block input:not([readonly]),
.night-form .night-block select:not([readonly]) {
  border: 2px solid var(--primary);
}
.night-form .night-block select {
  font-size: 14px;
}
.notifications-area {
  display: flex;
  gap: 10px;
  padding-bottom: 20px;
}
.notifications-area .notification {
  border-radius: var(--br);
  background: hsla(161, 76%, 13%, 0.11);
  color: var(--primary);
  padding: 10px 10px 10px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  cursor: pointer;
}
.notifications-area .notification .notification-icon svg {
  width: 14px;
  height: 14px;
}
.notifications-area .notification .notification-icon svg path {
  fill: var(--primary);
}
.notifications-area .notification .notifications-list {
  display: none;
  width: 500px;
  border: 1px solid var(--border);
  border-radius: var(--br);
  background: var(--white);
  box-shadow: 0 0 5px 5px rgba(0, 0, 0, 0.05);
}
.notifications-area .notification .notifications-list > div {
  display: flex;
  padding: 14px;
  justify-content: space-between;
  gap: 5px;
  border-bottom: 1px solid var(--border);
}
.notifications-area .notification .notifications-list > div:last-child {
  border-bottom: none;
}
.notifications-area .notification:hover .notifications-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: absolute;
  top: 30px;
  right: 0px;
}
.notifications-area .notification .dismiss {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  align-items: center;
  display: inline-flex;
  justify-content: center;
}
.notifications-area .notification .dismiss svg {
  height: 15px;
  width: 15px;
}
.notifications-area .notification .dismiss svg path {
  fill: var(--primary);
}
.notifications-area .notification .dismiss:hover {
  cursor: pointer;
}
.notifications-area .notification .dismiss:hover svg path {
  fill: var(--red);
}
.notifications-area .notification.notification-danger {
  background: var(--red-fade);
  color: var(--red);
}
.notifications-area .notification.notification-danger svg path {
  fill: var(--red);
}
.notifications-area .notification.notification-danger .dismiss svg path {
  fill: var(--red);
}
.signature-area {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  touch-action: none;
  /* Prevents default touch actions */
  position: relative;
}
.signature-area header {
  width: 300px;
}
.signature-area canvas {
  border-radius: var(--br);
  margin-bottom: 10px;
  border: 2px solid var(--border);
  width: 100%;
  max-width: 420px;
  height: 200px;
  touch-action: none;
  /* Prevents default touch actions */
}
.signature-area > div {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 300px;
  align-items: center;
  justify-content: center;
}
.signature-area > div .button {
  width: 100%;
}
.signature-area > div .button.tertiary {
  width: 80px;
}
.show-tonnage {
  margin-top: 5px;
  border: 1px solid var(--border);
  padding: 5px;
  background: var(--bg);
  border-radius: calc(var(--br) / 2);
}
.show-tonnage > div {
  display: grid;
  grid-template-columns: 1fr 1fr;
}
.show-tonnage > div strong {
  padding-right: 5px;
  text-align: right;
}
.proof-of-delivery-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--border);
  border-radius: var(--br);
  padding: 0 10px;
  background: var(--secondary);
  height: 52px;
}
.proof-of-delivery-icon svg path {
  fill: var(--primary);
}
.contract-job-add {
  display: flex;
  gap: 10px;
  flex-direction: row !important;
  align-items: flex-end;
}
.contract-job-add .b {
  flex-grow: 1;
}
.contract-job-add .button {
  margin-bottom: 3px;
}
.contract-job-add .or {
  color: var(--grey);
  padding-bottom: 15px;
}
.eticket-upload .upload-wrap {
  display: flex;
  gap: 10px;
  align-items: center;
}
.eticket-upload .upload-wrap input[type="file"] {
  height: 40px;
  box-sizing: border-box;
}
.eticket-upload .upload-wrap .upload-text {
  height: 40px;
}
.eticket-upload .upload-wrap .button {
  padding: 5px 10px;
}
.uploaded-docs-container {
  display: flex;
  gap: 20px;
  flex-direction: row !important;
  flex-wrap: wrap;
}
.uploaded-docs-container .doc-container {
  display: flex;
  gap: 0px;
  position: relative;
  border: 1px solid var(--border);
  border-radius: calc(var(--br) / 2);
  align-items: flex-end;
  background: var(--bg);
}
.uploaded-docs-container .doc-container .doc-info {
  font-size: 10px;
  color: var(--grey);
  padding: 10px;
  text-align: right;
}
.uploaded-docs-container a {
  border-right: 1px solid var(--border);
  height: 66px;
  width: 51px;
  border-radius: var(--br) 0 0 var(--br);
  background-size: cover;
}
.uploaded-docs-container .remove {
  height: 15px;
  width: 15px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 5px;
  right: 5px;
}
.uploaded-docs-container .remove:hover > svg path {
  fill: var(--red);
}
.uploaded-docs-container .remove > svg {
  height: 14px;
  width: 14px;
}
.uploaded-docs-container .remove > svg path {
  fill: var(--primary);
}
.has-attachment svg {
  height: 20px;
  width: 18px;
}
.has-attachment svg path {
  fill: var(--secondary);
}
.subsubtitle {
  background: rgba(8, 56, 40, 0.05);
  font-weight: 600;
  color: var(--primary);
  padding: 8px 20px;
}
.nowrap {
  white-space: nowrap;
}
.time-in-list {
  line-height: 160%;
  font-size: 12px;
  width: 85px;
}
.will-ring-notes-container {
  display: flex;
  gap: 5px;
  align-items: center;
  margin-top: 5px;
}
.will-ring-notes-container .b {
  flex-grow: 1;
}
.will-ring-notes-container .b textarea,
.will-ring-notes-container .b input {
  flex-grow: 1;
  font-size: 10px;
  height: 30px;
  padding: 5px;
  max-width: 300px;
  margin-bottom: 0 !important;
}
.will-ring-notes-container .b label {
  display: none;
}
