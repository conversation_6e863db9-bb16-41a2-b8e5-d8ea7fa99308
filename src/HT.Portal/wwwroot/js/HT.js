﻿function ShowSuccessNotification(text)
{
    ohSnap(text, { color: 'green', icon: '', duration: 2000 });
}

function ShowErrorNotification(text)
{
    ohSnap(text, { color: 'red', icon: '', duration: 2000 });
}

function selectText(elementId) {
    var input = document.getElementById(elementId);
    input.focus();
    input.select();
}

function downloadFileFromBytes(fileName, contentAsBase64, contentType) {
    const linkSource = `data:${contentType};base64,${contentAsBase64}`;
    const downloadLink = document.createElement("a");
    downloadLink.href = linkSource;
    downloadLink.download = fileName;
    downloadLink.click();
}

function setCheckboxIndeterminate(checkboxElement) {
    if (checkboxElement) {
        checkboxElement.indeterminate = true;
    }
}

