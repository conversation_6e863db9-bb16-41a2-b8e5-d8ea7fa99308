﻿using HT.Shared.Models.Google.Maps;

namespace HT.Api.Tests.Services.Google.Maps
{
    public class GeocodeServiceTests
    {
        [Fact]
        public async Task GetGeocodeLocationAsync_ValidAddress_ReturnsLocation()
        {
            // Arrange
            string address = "19 Wilsic Road, Tickhill, Doncaster, DN11 9JG";
            GoogleGeocodeService geocodeService = new GoogleGeocodeService();

            // Act
            GeocodeLocation location = await geocodeService.GetGeocodeLocationAsync(address);

            // Assert
            Assert.NotNull(location);
            Assert.Equal(53.4359998, location.lat);
            Assert.Equal(-1.1119947, location.lng);
        }

        [Fact]
        public async Task GetGeocodeLocationAsync_InvalidAddress_ReturnsNull()
        {
            // Arrange
            string address = "Invalid Address";
            GoogleGeocodeService geocodeService = new GoogleGeocodeService();

            // Act
            GeocodeLocation location = await geocodeService.GetGeocodeLocationAsync(address);

            // Assert
            Assert.Null(location);
        }
    }
}
