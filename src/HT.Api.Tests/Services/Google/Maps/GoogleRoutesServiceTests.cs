﻿using HT.Api.Services.Google.Maps;

namespace HT.Api.Tests.Services.Google.Maps
{
    public class GoogleRoutesServiceTests
    {
        [Fact]
        public async Task GetGeocodeLocationAsync_ValidAddress_ReturnsLocation()
        {
            // Arrange
            string home = "53.4390, -1.1155";
            var stopGeocodes = new List<string>
            {
                "53.4794,-2.2451", // manchester 2
                "54.8948, -2.9362", // carlisle 3
                "53.4071,-2.9916", // liverpool 1
                "51.5073, -0.1276", //london 4
            };

            GoogleRoutesService googleRoutesService = new GoogleRoutesService();

            // Act
            var result = await googleRoutesService.GetOptimisedRoute(home, home, stopGeocodes);

            // Assert
            //Assert.Equal("endAddress1 -> endAddress2", result);
        }


    }
}
