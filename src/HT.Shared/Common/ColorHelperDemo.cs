using System;

namespace HT.Shared.Common;

/// <summary>
/// Demo class to show how the ColorHelper works with different contract names
/// </summary>
public static class ColorHelperDemo
{
    /// <summary>
    /// Demonstrates the color generation for various contract names
    /// </summary>
    public static void ShowColorExamples()
    {
        string[] testContracts = {
            "Green Earth Waste Management",
            "City Recycling Services", 
            "Premium Waste Solutions",
            "Industrial Cleanup Co",
            "Metro Collection Services",
            "Green Earth Waste Management", // Duplicate to test consistency
            null, // Test null handling
            "", // Test empty string
            "ABC", // Short name
            "A Very Long Contract Name That Might Be Used In Real World Scenarios"
        };

        Console.WriteLine("Contract Color-Coding Demo:");
        Console.WriteLine("===========================");
        
        foreach (var contract in testContracts)
        {
            var color = ColorHelper.GetContractColor(contract);
            var style = ColorHelper.GetContractColorStyle(contract);
            var displayName = string.IsNullOrWhiteSpace(contract) ? "[null/empty]" : contract;
            Console.WriteLine($"{displayName,-50} -> Color: {color,-8} Style: {style}");
        }
        
        Console.WriteLine("\nNote: Same contract names will always generate the same color!");
    }
}