using System.Security.Cryptography;
using System.Text;

namespace HT.Shared.Common;

public static class ColorHelper
{
    private static readonly string[] ContractColors = 
    {
        "#07bf50",  // Green
        "#052a63",  // Blue
        "#6f42c1",  // Purple  
        "#e18700",  // Orange
        "#db1731",  // Red
        "#521a00",  // <PERSON>
        "#922e00",  // Brown Secondary
        "#0042ac",  // Blue Secondary
        "#FFE500",  // Yellow
        "#41454b",  // Grey
        "#7f8691"   // Grey Secondary
    };

    private static readonly string DefaultColor = "#052a63"; // Primary blue

    public static string GetContractColor(string contractName)
    {
        if (string.IsNullOrWhiteSpace(contractName))
        {
            return DefaultColor;
        }

        using var sha1 = SHA1.Create();
        var hash = sha1.ComputeHash(Encoding.UTF8.GetBytes(contractName));
        var hashValue = BitConverter.ToUInt32(hash, 0);
        var colorIndex = (int)(hashValue % ContractColors.Length);
        
        return ContractColors[colorIndex];
    }

    public static string GetContractColorStyle(string contractName)
    {
        var color = GetContractColor(contractName);
        return $"background: {color};";
    }
}