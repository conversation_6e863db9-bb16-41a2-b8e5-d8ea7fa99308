window.select2Component = {
    init: function (id, placeholder, values) {
        $('#' + id).select2({
            placeholder: placeholder,
            allowClear: true,
        });

        if (values != null) {
            $('#' + id).val(values).trigger('change');
        }
    },
    onChange: function (id, dotnetHelper, nameFunc) {
        $('#' + id).on('select2:clear', function (e) {
            $('#' + id).data('unselecting', true);

            var functionName = nameFunc;
            var value = $('#' + id).val();

            if (Array.isArray(value)) {
                functionName = 'Multi' + nameFunc;
            }
            dotnetHelper.invokeMethodAsync(functionName, null);
        });

        $('#' + id).on('select2:unselect', function (e) {
            if (!$(this).data('unselecting')) {
                $('#' + id).data('unselecting', true);
                HandleSelect2Selection(id, dotnetHelper, nameFunc);
            }
        });
        $('#' + id).on('select2:select', function (e) {
            HandleSelect2Selection(id, dotnetHelper, nameFunc);
        });
        $('#' + id).on('select2:opening', function (e) {
            if ($(this).data('unselecting')) {
                $(this).removeData('unselecting');
                e.preventDefault();
            }
        });
    },
    update: function (id, value) {
        var currentValue = $('#' + id).val();

        if (currentValue != value)
            $('#' + id).val(value).trigger('change.select2')
    },
}

function HandleSelect2Selection(id, dotnetHelper, nameFunc) {
    var functionName = nameFunc;
    var value = $('#' + id).val();

    if (Array.isArray(value)) {
        functionName = 'Multi' + nameFunc;
    }
    dotnetHelper.invokeMethodAsync(functionName, value);
}
