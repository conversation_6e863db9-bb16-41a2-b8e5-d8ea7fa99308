﻿@typeparam TValue
@inherits FieldContainerBase<TValue>

<CascadingValue Value="@this">
    <div class="@GetClass @ExtraClasses">
        @if (!string.IsNullOrEmpty(this.FieldBase.Label))
        {
            <label for="@this.FieldBase.GetId">
                @this.FieldBase.Label <span hidden="@(!this.FieldBase.IsMandatory)" class="mandatory">*</span>
                @if (!string.IsNullOrEmpty(this.FieldBase.Tooltip))
                {
                    <span class="inline-help"><span>@this.FieldBase.Tooltip</span></span>
                }
            </label>
        }

        @ChildContent

@*        @if (this.FieldProperties.CascadedEditContext != null && !this.FieldBase.DisableInlineValidation)
        {
            <ValidationMessage For=@this.FieldBase.ValueExpression />
        }*@
    </div>
</CascadingValue>
