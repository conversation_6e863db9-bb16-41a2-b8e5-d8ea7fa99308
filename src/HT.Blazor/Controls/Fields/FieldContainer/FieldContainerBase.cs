﻿using HT.Blazor.Controls.Fields.FieldBase;
using Microsoft.AspNetCore.Components;

namespace HT.Blazor.Controls.Fields.FieldContainer;

public class FieldContainerBase<TValue> : ComponentBase
{
    [Parameter] public RenderFragment ChildContent { get; set; }

    [Parameter] public IFieldBase<TValue> FieldBase { get; set; }

    [Parameter] public string ExtraClasses { get; set; }

    public string GetClass => $"b {FieldBase.Class}";

}
