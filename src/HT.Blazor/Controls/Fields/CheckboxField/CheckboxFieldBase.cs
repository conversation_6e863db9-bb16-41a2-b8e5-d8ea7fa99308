﻿using HT.Blazor.Controls.Fields.FieldBase;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace HT.Blazor.Controls.Fields.CheckboxField;

public class CheckboxFieldBase<T> : FieldBase<T>
{
    [Parameter] public bool Indeterminate { get; set; }
    [Inject] public IJSRuntime JSRuntime { get; set; }

    protected ElementReference checkboxElement;

    public bool ValueAsBool
    {
        get => System.Convert.ToBoolean(CurrentValue);
        set
        {
            CurrentValue = (T)(object)value;

            CascadedEditContext?.NotifyFieldChanged(FieldIdentifier);
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (Indeterminate)
        {
            await JSRuntime.InvokeVoidAsync("setCheckboxIndeterminate", checkboxElement);
        }
        await base.OnAfterRenderAsync(firstRender);
    }
}
