﻿@using HT.Blazor.Controls.Fields.FieldContainer
@typeparam TValue
@inherits CheckboxFieldBase<TValue>

<FieldContainer FieldBase="@this" ExtraClasses="check force-left">

   <div class="checkbox @(ValueAsBool == true ? "selected" : "") @(ReadOnly == true ? "readonly" : "") @(Indeterminate ? "indeterminate" : "")">
        <input type="checkbox"
               @bind=@ValueAsBool
               id=@GetId
               name=@GetName
               class=@FieldCss()
               readonly="@ReadOnly"
               disabled="@ReadOnly"
               @ref="checkboxElement" />
    </div>

</FieldContainer>