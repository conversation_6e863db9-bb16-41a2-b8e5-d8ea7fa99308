﻿@using HT.Blazor.Controls.Fields.FieldContainer
@using Microsoft.JSInterop;
@typeparam TValue
@inherits DropdownFieldBase<TValue>
@inject IJSRuntime JSRuntime

<FieldContainer FieldBase="@this">

    <select id="@GetId" class=@DropdownFieldCss style="width: 100%;" multiple="@AllowMultiple" readonly="@ReadOnly" disabled="@ReadOnly">
        <option></option>
        @foreach (var item in DropDownItems)
        {
            <option value="@item.Id">@item.Name</option>
        }
    </select>

</FieldContainer>

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            if (AllowMultiple)
                await JSRuntime.InvokeAsync<string>("select2Component.init", this.GetId, this.Placeholder, this.Values);

            else
                await JSRuntime.InvokeAsync<string>("select2Component.init", this.GetId, this.Placeholder, this.Value);

            await JSRuntime.InvokeVoidAsync("select2Component.onChange", this.GetId, this.DotNetRef, "ChangeSelectEvent");
        }
        else
        {
            if (AllowMultiple)
                await JSRuntime.InvokeVoidAsync("select2Component.update", this.GetId, this.Values);
            else
                await JSRuntime.InvokeVoidAsync("select2Component.update", this.GetId, this.Value);
        }
    }

}