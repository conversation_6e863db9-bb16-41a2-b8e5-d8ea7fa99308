﻿using HT.Blazor.Controls.Fields.FieldBase;
using HT.Blazor.Models.Field;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Collections;

namespace HT.Blazor.Controls.Fields.DropdownField;

public class DropdownFieldBase<T> : FieldBase<T>
{
    public DotNetObjectReference<DropdownFieldBase<T>> DotNetRef;

    [Parameter]
    public List<DropdownListItem> Items { get; set; }

    [Parameter]
    public bool AllowMultiple { get; set; } = false;

    protected override Task OnInitializedAsync()
    {
        DotNetRef = DotNetObjectReference.Create(this);

        return base.OnInitializedAsync();
    }

    [JSInvokable("ChangeSelectEvent")]
    public void ChangeSelectEvent(string value)
    {
        CurrentValue = GetValue(value);

        CascadedEditContext?.NotifyFieldChanged(FieldIdentifier);
    }

    [JSInvokable("MultiChangeSelectEvent")]
    public void MultiChangeSelectEvent(List<string> values)
    {
        if (values == null)
        {
            CurrentValue = (T)(object)null;
        }
        else if (typeof(T) == typeof(List<int>))
        {
            var listOfValues = new List<int>();
            listOfValues.AddRange(values.Select(x => Convert.ToInt32(x)));

            CurrentValue = (T)(object)listOfValues;

        }
        else if (typeof(T) == typeof(List<int?>))
        {
            var listOfValues = new List<int?>();
            listOfValues.AddRange(values.Select(x => (int?)Convert.ToInt32(x)));

            CurrentValue = (T)(object)listOfValues;

        }
        else if (typeof(T) == typeof(List<Guid>))
        {
            var listOfValues = new List<Guid>();
            listOfValues.AddRange(values.Where(s => !string.IsNullOrWhiteSpace(s)).Select(x => new Guid(x)));

            CurrentValue = (T)(object)listOfValues;
        }
        else if (typeof(T) == typeof(List<Guid?>))
        {
            var listOfValues = new List<Guid?>();
            listOfValues.AddRange(values.Where(s => !string.IsNullOrWhiteSpace(s)).Select(x => (Guid?)new Guid(x)));

            CurrentValue = (T)(object)listOfValues;
        }
        else if (typeof(T) == typeof(List<string>))
        {
            var listOfValues = new List<string>();
            listOfValues.AddRange(values.Select(x => x.ToString()));

            CurrentValue = (T)(object)listOfValues;
        }

        CascadedEditContext?.NotifyFieldChanged(FieldIdentifier);
    }

    internal new string GetId => $"{(!string.IsNullOrEmpty(Id) ? Id : FieldIdentifier.FieldName)}-select";

    internal List<string> Values
    {
        get
        {
            var values = new List<string>();

            if (this.CurrentValue is IEnumerable valueAsEnumerable)
            {
                foreach (object item in valueAsEnumerable)
                {
                    values.Add(item.ToString().ToLower());
                }
            }
            return values;
        }
    }

    internal List<DropdownListItem> DropDownItems => Items.Select(x => new DropdownListItem(x.Id.ToLower(), x.Name)).ToList();

    private static T GetValue(string value)
    {
        if (value == null)
        {
            return (T)(object)null;
        }

        var type = typeof(T);

        if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            type = Nullable.GetUnderlyingType(type);
        }

        if (type == typeof(int))
        {
            return (T)(object)Convert.ToInt32(value);
        }
        else if (type == typeof(Guid))
        {
            if (string.IsNullOrEmpty(value))
            {
                return (T)(object)null;
            }
            else
            {
                return (T)(object)new Guid(value);
            }
        }
        else if (type == typeof(string))
        {
            return (T)(object)value.ToString();
        }
        else if (type == typeof(List<int>))
        {
            return (T)(object)value;
        }
        else if (type == typeof(List<Guid>))
        {
            return (T)(object)value;
        }
        else if (type == typeof(List<string>))
        {
            return (T)(object)value;
        }
        else if (type == typeof(decimal))
        {
            return (T)(object)Convert.ToDecimal(value);
        }
        else if (type.IsEnum)
        {
            return (T)(object)Convert.ToInt32(value);
        }


        return (T)(object)null;
    }

    internal string DropdownFieldCss => $"{FieldCss()} select2";
}
