﻿using HT.Blazor.Controls.Fields.FieldBase;
using Microsoft.AspNetCore.Components;

namespace HT.Blazor.Controls.Fields.TextField;

public class TextFieldBase<T> : FieldBase<T>
{
    [Parameter] public bool HideContents { get; set; }

    [Parameter] public int? MinValue { get; set; }

    [Parameter] public int? MaxValue { get; set; }

    internal string ValueAsString
    {
        get => System.Convert.ToString(CurrentValue);
        set
        {
            CurrentValue = (T)(object)value;

            CascadedEditContext?.NotifyFieldChanged(FieldIdentifier);
            CascadedEditContext?.NotifyValidationStateChanged();
        }
    }

    internal T ValueAsNumber
    {
        get
        {
            string value = System.Convert.ToString(CurrentValue);
            var parsedValue = TryParseNumber(value);
            return parsedValue;
        }
        set
        {
            CurrentValue = (T)(object)value;

            CascadedEditContext?.NotifyFieldChanged(FieldIdentifier);
        }
    }

    internal static bool IsNumericType(Type type)
    {
        if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            type = Nullable.GetUnderlyingType(type);
        }

        return Type.GetTypeCode(type) switch
        {
            TypeCode.Byte or TypeCode.SByte or TypeCode.UInt16 or TypeCode.UInt32 or TypeCode.UInt64 or TypeCode.Int16 or TypeCode.Int32 or TypeCode.Int64 or TypeCode.Decimal or TypeCode.Double or TypeCode.Single => true,
            TypeCode.Empty => false,
            TypeCode.Object => false,
            TypeCode.DBNull => false,
            TypeCode.Boolean => false,
            TypeCode.Char => false,
            TypeCode.DateTime => false,
            TypeCode.String => false,
            _ => false,
        };
    }

    internal string GetTextType => HideContents ? "password" : "text";

    private static T TryParseNumber(string value)
    {
        var type = typeof(T);

        if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            type = Nullable.GetUnderlyingType(type);
        }

        if (type == typeof(double))
        {
            if (double.TryParse(value, out double doubleoutValue))
            {
                return (T)(object)doubleoutValue;
            }
        }
        else if (type == typeof(decimal))
        {
            if (decimal.TryParse(value, out decimal decimaloutValue))
            {
                return (T)(object)decimaloutValue;
            }
        }
        else
        {
            if (int.TryParse(value, out int intoutValue))
            {
                return (T)(object)intoutValue;
            }
        }

        return default;
    }
}
