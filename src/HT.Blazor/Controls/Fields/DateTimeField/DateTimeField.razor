﻿@using HT.Blazor.Models.Field;
@using HT.Blazor.Controls.Fields.FieldContainer

@typeparam TValue
@inherits DateTimeFieldBase<TValue>

<FieldContainer FieldBase="@this">

    @if (FieldType == DateTimeFieldType.DateTime)
    {
        <input class=@FieldCss()
           type="datetime-local"
           @bind="@ValueAsDateTime"
           min="@MinValueString"
           max="@MaxValueString"
           readonly="@ReadOnly"
           disabled="@ReadOnly" />

    }
    else if (FieldType == DateTimeFieldType.Date)
    {
        <input type="date"
           class=@FieldCss()
           id="@GetId"
           @bind-value="@ValueAsDateTime"
           min="@MinValueString"
           max="@MaxValueString"
           readonly="@ReadOnly"
           disabled="@ReadOnly" />
    }
@*     else if (FieldType == DateTimeFieldType.Time)
    {
        <input type="time"
           class=@FieldCss()
           id="@GetId"
           @bind="@ValueAsTime"
           min="@MinValueString"
           max="@MaxValueString"
           readonly="@ReadOnly"
           disabled="@ReadOnly" />
    } *@

</FieldContainer>
