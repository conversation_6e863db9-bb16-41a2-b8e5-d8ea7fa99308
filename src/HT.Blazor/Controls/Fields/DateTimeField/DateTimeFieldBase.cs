﻿using HT.Blazor.Controls.Fields.FieldBase;
using HT.Blazor.Models.Field;
using Microsoft.AspNetCore.Components;

namespace HT.Blazor.Controls.Fields.DateTimeField;

public class DateTimeFieldBase<T> : FieldBase<T>
{
    [Parameter] public DateTimeFieldType FieldType { get; set; } = DateTimeFieldType.Date;

    [Parameter] public T MinValue { get; set; }

    [Parameter] public T MaxValue { get; set; }

    public DateTime? ValueAsDateTime
    {
        get => System.Convert.ToDateTime(CurrentValue) == DateTime.MinValue ? null : System.Convert.ToDateTime(CurrentValue);
        set
        {
            var dateValue = value;
            if (typeof(T) == typeof(DateTime))
            {
                dateValue ??= DateTime.Today;
            }

            CurrentValue = (T)(object)dateValue;

            CascadedEditContext?.NotifyFieldChanged(FieldIdentifier);
        }
    }

    public string MinValueString
    {
        get
        {
            switch (FieldType)
            {
                case DateTimeFieldType.Date:
                    var date = MinValue as DateTime?;
                    return date?.ToString("yyyy-MM-dd");
                case DateTimeFieldType.Time:
                    string time = MinValue as string;
                    return time.ToString();
                case DateTimeFieldType.DateTime:
                    var dateTime = MinValue as DateTime?;
                    return dateTime?.ToString("yyyy-MM-ddThh:mm");
                default:
                    return "";
            }
        }
    }

    public string MaxValueString
    {
        get
        {
            switch (FieldType)
            {
                case DateTimeFieldType.Date:
                    var date = MaxValue as DateTime?;
                    return date?.ToString("yyyy-MM-dd");
                case DateTimeFieldType.Time:
                    string time = MaxValue as string;
                    return time.ToString();
                case DateTimeFieldType.DateTime:
                    var dateTime = MaxValue as DateTime?;
                    return dateTime?.ToString("yyyy-MM-ddThh:mm");
                default:
                    return "";
            }
        }
    }

}
