﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using System.Linq.Expressions;

namespace HT.Blazor.Controls.Fields.FieldBase;

public class FieldBase<T> : ComponentBase, IFieldBase<T>
{
    [CascadingParameter] public EditContext CascadedEditContext { get; set; }

    protected override void OnInitialized()
    {
        CustomAttributes = new Dictionary<string, object>()
        {
           { "name", this.GetName },
        };
    }

    [Parameter]
    public T Value { get; set; }

    [Parameter]
    public EventCallback<T> ValueChanged { get; set; }

    [Parameter]
    public Expression<Func<T>> ValueExpression { get; set; }

    [Parameter]
    public string Name { get; set; }

    [Parameter]
    public string Id { get; set; }

    [Parameter]
    public string Label { get; set; }

    [Parameter]
    public string Class { get; set; }

    [Parameter]
    public string Placeholder { get; set; }

    [Parameter]
    public string Tooltip { get; set; }

    [Parameter]
    public bool ReadOnly { get; set; } = false;

    [Parameter]
    public EventCallback<bool> ReadOnlyChanged { get; set; }

    [Parameter]
    public bool DisableInlineValidation { get; set; } = false;

    public string GetId => !string.IsNullOrEmpty(Id) ? Id : FieldIdentifier.FieldName;

    [Parameter(CaptureUnmatchedValues = true)]
    public Dictionary<string, object> CustomAttributes { get; set; }

    [Parameter]
    public Action OnChangeAction { get; set; }

    [Parameter]
    public Func<Task> OnChangeActionAsync { get; set; }

    public T CurrentValue
    {
        get => Value;
        set
        {
            Value = value;
            ValueChanged.InvokeAsync(value);

            OnChangeAction?.Invoke();

            OnChangeActionAsync?.Invoke();
        }
    }

    public bool IsMandatory { get; set; }

    //public bool IsMandatory { get { return CascadedEditContext?.Model.GetAttributeFrom<RequiredAttribute>(FieldIdentifier.FieldName) != null; } set { } }

    public FieldIdentifier FieldIdentifier => FieldIdentifier.Create(ValueExpression);

    public string FieldCss()
    {
        string css = "form-control";

        if (CascadedEditContext != null)
        {
            bool isValid = !CascadedEditContext.GetValidationMessages(FieldIdentifier).Any();
            bool isModified = CascadedEditContext.IsModified(FieldIdentifier);

            if (isModified)
            {
                css += " modified";
            }

            css += isValid ? " valid" : " invalid";
        }

        return css;
    }

    internal string GetName => !string.IsNullOrEmpty(Name) ? Name : FieldIdentifier.FieldName;

}
