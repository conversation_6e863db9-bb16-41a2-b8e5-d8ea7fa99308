﻿using Microsoft.AspNetCore.Components.Forms;
using System.Linq.Expressions;

namespace HT.Blazor.Controls.Fields.FieldBase;

public interface IFieldBase<T>
{
    EditContext CascadedEditContext { get; set; }

    string Class { get; set; }

    Dictionary<string, object> CustomAttributes { get; set; }

    string Id { get; set; }

    string Label { get; set; }

    string Name { get; set; }

    string Tooltip { get; set; }

    bool IsMandatory { get; set; }

    bool DisableInlineValidation { get; set; }

    string GetId { get; }

    FieldIdentifier FieldIdentifier { get; }

    Expression<Func<T>> ValueExpression { get; set; }
}
