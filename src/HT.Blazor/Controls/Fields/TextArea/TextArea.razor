﻿@using HT.Blazor.Controls.Fields.FieldContainer
@typeparam TValue
@inherits HT.Blazor.Controls.Fields.TextField.TextFieldBase<TValue>

<FieldContainer FieldBase="@this">

    @if (typeof(TValue) == typeof(string))
    {
        <input type=@GetTextType 
            @bind-value=@ValueAsString 
            id="@GetId" 
            name="@GetName" 
            class=@FieldCss() 
            readonly="@ReadOnly" 
            disabled="@ReadOnly" 
            placeholder="@Placeholder" />
    }
    else if (IsNumericType(typeof(TValue)))
    {
        <input step="any" 
            type="number" 
            min="@MinValue" 
            max="@MaxValue" 
            @bind-value=@ValueAsNumber 
            id="@GetId" 
            name="@GetName" 
            class=@FieldCss() 
            readonly="@ReadOnly" 
            disabled=@ReadOnly 
            placeholder="@Placeholder" />
    }

</FieldContainer>