﻿@if (IsOpen)
{
    <CascadingValue Value="@this">
        <div class="dialog-overlay">
            <div class="dialog @Class" style="width: @Width;height: @Height;">

                <DialogHeader></DialogHeader>

                <div>
                @ChildContent
                </div>

                <DialogFooter></DialogFooter>
            </div>
        </div>
    </CascadingValue>
}

@code{
    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public RenderFragment<DialogControl> HeaderTemplate { get; set; }

    [Parameter]
    public RenderFragment<DialogControl> ActionsTemplate { get; set; }

    [Parameter]
    public string Title { get; set; }

    [Parameter]
    public string Width { get; set; }

    [Parameter]
    public string Height { get; set; }

    [Parameter]
    public string Class { get; set; }

    [Parameter]
    public bool IsOpen { get; set; } = false;

    [Parameter]
    public EventCallback<bool> IsOpenChanged { get; set; }

    [Parameter]
    public bool HasFormEditorComponent { get; set; } = false;

    [Parameter]
    public EventCallback<bool> HasFormEditorComponentChanged { get; set; }

    public void OpenDialog()
    {
        IsOpen = true;
    }

    public void CloseDialog()
    {
        IsOpen = false;
        IsOpenChanged.InvokeAsync(IsOpen);


        StateHasChanged();
    }
}
