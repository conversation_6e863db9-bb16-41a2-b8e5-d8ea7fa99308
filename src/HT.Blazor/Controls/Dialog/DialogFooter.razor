﻿
    <footer>
        @if (this.DialogControl.ActionsTemplate != null)
        {

            @this.DialogControl.ActionsTemplate(this.DialogControl)

        }
        else if (!this.DialogControl.HasFormEditorComponent)
        {
            @* <span class="button tertiary" @onclick="@this.DialogControl.CloseDialog">Close</span> *@
        }
    </footer>

@code{
    [CascadingParameter]
    private DialogControl DialogControl { get; set; }
}