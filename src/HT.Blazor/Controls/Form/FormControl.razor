﻿@using Microsoft.AspNetCore.Components.Forms
@typeparam TValue
@inherits FormControlBase<TValue>

<CascadingValue Value="@this">
    <EditForm OnSubmit="SubmitForm" EditContext="EditContext">
        <div class="card form-card">

            @if (this.ButtonTemplate != null)
            {
                <div class="subtitle">
                    <div>@Title</div>
                    <div class="buttons-container">
                        @this.ButtonTemplate(this)
                    </div>
                </div>
            }
            else
            {
                <div class="subtitle">
                    <div>@Title</div>
                    <button type="submit" class="button small secondary">Save</button>
                </div>
            }

            <DataAnnotationsValidator />
            <ValidationSummary />

            @FormTemplate(DataModel)

        </div>
    </EditForm>
</CascadingValue>