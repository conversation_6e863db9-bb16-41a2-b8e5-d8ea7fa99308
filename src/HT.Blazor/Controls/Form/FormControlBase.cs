﻿using HT.Blazor.Models.Form;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;

namespace HT.Blazor.Controls.Form;

public class FormControlBase<TValue> : ComponentBase
{
    [Parameter] public TValue DataModel { get; set; }

    [Parameter] public RenderFragment<FormControlBase<TValue>> ButtonTemplate { get; set; }

    [Parameter] public RenderFragment<TValue> FormTemplate { get; set; }

    [Parameter] public Func<TValue, Task<FormResponseModel<TValue>>> SubmitFormAsync { get; set; }

    [Parameter] public Func<TValue, Task> AfterSuccessAsync { get; set; }

    [Parameter] public string Title { get; set; }


    public EditContext EditContext;

    public ValidationMessageStore ValidationMessageStore;

    protected override void OnInitialized()
    {
        EditContext = new EditContext(DataModel);
        ValidationMessageStore = new ValidationMessageStore(EditContext);

        base.OnInitialized();
    }

    private bool FormIsValid { get; set; }

    public async Task SubmitForm()
    {
        ValidationMessageStore.Clear();

        this.FormIsValid = EditContext.Validate();

        if (!FormIsValid)
        {
            return;
        }

        var formResponseModel = await this.SubmitFormAsync.Invoke(DataModel);

        if (formResponseModel.Success)
        {
            await this.AfterSuccessAsync.Invoke(DataModel);
            return;
        }

        foreach (var error in formResponseModel.Errors)
        {
            object objectInstance = EditContext.Model;
            ValidationMessageStore.Add(new FieldIdentifier(objectInstance, error.FieldIdentifier.FieldName), error.Message);
        }

        EditContext.NotifyValidationStateChanged();
    }

    public async Task Delete()
    {
        // Method intentionally left empty.
    }
}
