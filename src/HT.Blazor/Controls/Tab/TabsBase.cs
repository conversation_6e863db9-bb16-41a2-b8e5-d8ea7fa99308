﻿using Microsoft.AspNetCore.Components;
using System.Collections.Specialized;

namespace HT.Blazor.Controls.Tab;

public class TabsBase : ComponentBase
{
    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Inject]
    public NavigationManager NavigationManager { get; set; }

    public TabBase SelectedItem { get; set; }

    protected List<TabBase> TabItems { get; set; } = [];

    public void SelectTab(string tabName)
    {
        var selectedTab = this.TabItems.FirstOrDefault(ti => ti.Text.ToLower().Equals(tabName, StringComparison.OrdinalIgnoreCase));

        if (selectedTab != null)
        {
            this.SelectPage(selectedTab);
            this.StateHasChanged();
        }
    }

    internal void AddPage(TabBase tabItem)
    {
        this.TabItems.Add(tabItem);

        if (this.TabItems.Count == 1)
        {
            this.SelectedItem = tabItem;
        }

        this.StateHasChanged();
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if (!firstRender)
        {
            return;
        }

        const string TabPrefix = "Tab";

        if (this.NavigationManager == null)
        {
            return;
        }

        string query = this.NavigationManager.ToAbsoluteUri(this.NavigationManager.Uri).Query;

        var queryDictionary = System.Web.HttpUtility.ParseQueryString(query ?? string.Empty);

        if (queryDictionary.Count == 0)
        {
            return;
        }

        var keyValuePairs = queryDictionary
            .ToPairs()
            .Where(kvp => kvp.Key.Contains(TabPrefix));

        foreach (var keyValuePair in keyValuePairs)
        {
            string kvpValue = keyValuePair.Value;

            bool hasMatchingTab = TabItems.Any(t => t.Text == kvpValue);

            if (hasMatchingTab)
            {
                this.SelectTab(kvpValue);
                this.StateHasChanged();
                return;
            }
        }
    }

    protected string GetClass(TabBase tabItem)
    {
        bool isSelected = tabItem == this.SelectedItem;

        string iconClass = $"tab {tabItem.IconClass}";

        iconClass += isSelected ? " selected" : string.Empty;

        return iconClass;
    }

    protected void SelectPage(TabBase tabItem)
    {
        this.SelectedItem = tabItem;
    }
}

public static class NameValueCollectionExtension
{
    public static IEnumerable<KeyValuePair<string, string>> ToPairs(this NameValueCollection collection)
    {
        ArgumentNullException.ThrowIfNull(collection);

        return collection.Cast<string>().Select(key => new KeyValuePair<string, string>(key, collection[key]));
    }
}
