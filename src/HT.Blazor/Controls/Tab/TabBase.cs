﻿using Microsoft.AspNetCore.Components;

namespace HT.Blazor.Controls.Tab;

public class TabBase : ComponentBase
{
    [Parameter]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public RenderFragment IconContent { get; set; }

    [Parameter]
    public string Text { get; set; }

    [Parameter]
    public string IconClass { get; set; }

    [CascadingParameter]
    public TabsBase Parent { get; set; }

    protected override void OnInitialized()
    {
        if (this.Parent == null)
        {
            throw new Exception("Tab Item must exist within a Tab Control");
        }

        this.Parent.AddPage(this);

        base.OnInitialized();
    }
}
