﻿@inherits TabsBase

<div class="tabs" role="group">
    <div class="container">
        @foreach (var tabItem in TabItems)
        {
                <span class="@GetClass(tabItem)" @onclick=@( () => SelectPage(tabItem) )>
                @tabItem.IconContent
                @tabItem.Text
                </span>
        }
    </div>
</div>

<CascadingValue Value="this">
    <div class="container">
        @ChildContent
    </div>
</CascadingValue>