﻿using Microsoft.AspNetCore.Components;

namespace HT.Blazor.Controls.Grid.Sorting;

public class GridSortingBase<TItem> : ComponentBase
{
    [Parameter] public Grid<TItem> Grid { get; set; }

    public string SortableItemSelected { get; set; }


    protected override void OnInitialized()
    {
        base.OnInitialized();

        var firstSortable = this.Grid.GridOptions.SortableItems.FirstOrDefault();
        if (firstSortable != null)
        {
            this.SortableItemSelected = firstSortable.PropertyName;
        }
    }

    public async void OnSortableItemSelectedAsync(ChangeEventArgs e)
    {
        this.SortableItemSelected = e.Value.ToString();
        await this.Grid.GetGridDataAsync();
    }

    public async Task ToggleSortDirection()
    {
        this.Grid.GridOptions.SortAsc = !this.Grid.GridOptions.SortAsc;
        await this.Grid.GetGridDataAsync();
    }

    public void Reset()
    {
        var firstSortable = this.Grid.GridOptions.SortableItems.FirstOrDefault();
        if (firstSortable != null)
        {
            this.SortableItemSelected = firstSortable.PropertyName;
        }

        this.Refresh();
    }

    public void Refresh()
    {
        this.StateHasChanged();
    }
}
