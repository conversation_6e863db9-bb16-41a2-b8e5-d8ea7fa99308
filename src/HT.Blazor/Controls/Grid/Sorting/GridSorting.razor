﻿@typeparam TItem
@inherits GridSortingBase<TItem>

@if (Grid.GridOptions.SortableItems.Any())
{
    <div class="top-sorting">
        <label>Sort</label>
        <select value=@SortableItemSelected @onchange="OnSortableItemSelectedAsync" title="Sort By">
            @foreach (var item in Grid.GridOptions.SortableItems)
            {
                <option value="@item.PropertyName">@item.DisplayName</option>
            }
        </select>

        <button @onclick=@ToggleSortDirection class="button tertiary small" title="Sort Ascending or Descending">
            @if (Grid.GridOptions.SortAsc)
            {
                <HT.Blazor.Controls.Icon.IconAscending />
            }
            else
            {
                <HT.Blazor.Controls.Icon.IconDescending />
            }
        </button>
    </div>  
}
