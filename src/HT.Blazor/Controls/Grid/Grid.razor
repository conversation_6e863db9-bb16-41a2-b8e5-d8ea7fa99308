﻿@using HT.Blazor.Controls.Grid.Paging
@using HT.Blazor.Controls.Grid.Results
@typeparam TItem
@inherits GridBase<TItem>


<div class="grid-flex">


    <div class="button secondary show-mobile-filters" @onclick=ToggleFilters>Filters</div>
    @if (ShowMobileFilters) {
        <div class="mobile-filters">
            <div class="title">Filters <span class="button small tertiary" @onclick=ToggleFilters>Close Filters</span></div>
            <HT.Blazor.Controls.Grid.Filters.GridFilters @ref="GridFilters" Grid="this" />
        </div>
    }


    <div class="adv-filters">
        <HT.Blazor.Controls.Grid.Filters.GridFilters @ref="GridFilters" Grid="this" />
    </div>



    <div class="grid-main">
        <div class="grid-header">

            <div class="sort-filter">

                <HT.Blazor.Controls.Grid.Sorting.GridSorting @ref="GridSorting" Grid="this" />

                <span class="button drop small secondary" title="Reset/Refresh Options">
                    <HT.Blazor.Controls.Icon.IconRefresh />
                    <ul>
                        <li><div @onclick="@this.Reset">Reset Filters and Sorting</div></li>
                        <li><div @onclick="this.GetGridDataAsync">Refresh Data</div></li>
                    </ul>
                </span>
            </div>
                        
            <GridPaging @ref="GridPaging" Grid="this" />
        </div>
        @if (this.IsLoading)
        {
            <HT.Blazor.Controls.Loading.Loading />
        } else
        {
            <GridResults Grid="this" />
        }


    </div>
</div>



