﻿using HT.Blazor.Models.Grid;
using HT.Blazor.Models.Grid.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using System.Linq.Dynamic.Core;
using System.Text;

namespace HT.Blazor.Controls.Grid.Helpers;

public static class GridHelper
{
    public static async Task<GridResultModel<T>> CreateGridAsync<T>(IQueryable<T> query, GridParametersModel gridParams)
    {
        T totalsResult;

        query = Search(query, gridParams);

        query = GridFilterHelper.ApplyFiltering(query, gridParams);

        query = ApplySorting(query, gridParams);

#pragma warning disable S6966 // Awaitable method should be used
        int totalCount = (query is IAsyncEnumerable<T>) ? await query.CountAsync() : query.Count();
#pragma warning restore S6966 // Awaitable method should be used

        if (!gridParams.Exporting)
        {
            query = ApplyPaging(query, gridParams, totalCount);
        }

#pragma warning disable S6966 // Awaitable method should be used
        var results = (query is IAsyncEnumerable<T>) ? await query.ToListAsync() : query.ToList();
#pragma warning restore S6966 // Awaitable method should be used

        return new GridResultModel<T>(results, totalCount);
    }

    public static async Task<GridResultModel<T>> GetGridTotalsAsync<T>(IQueryable<T> query, GridParametersModel gridParams)
    {
        query = Search(query, gridParams);

        query = GridFilterHelper.ApplyFiltering(query, gridParams);

#pragma warning disable S6966 // Awaitable method should be used
        var results = (query is IAsyncEnumerable<T>) ? await query.ToListAsync() : query.ToList();
#pragma warning restore S6966 // Awaitable method should be used

        return new GridResultModel<T>(results, 0);
    }

    public static IQueryable<T> Search<T>(IQueryable<T> gridResults, GridParametersModel gridParams)
    {
        if (gridParams.SearchQueries.Count > 0 && gridParams.SearchQueries.Any(s => !string.IsNullOrEmpty(s.SearchQuery)))
        {
            var searchPredicate = new StringBuilder();
            int counter = 0;
            foreach (var searchItem in gridParams.SearchQueries.Where(s => !string.IsNullOrEmpty(s.SearchQuery)))
            {
                if (counter > 0)
                {
                    searchPredicate.Append(" or ");
                }

                if (searchItem.PropertyType == PropertyType.IntType)
                {
                    searchPredicate.Append($"{searchItem.PropertyName}.ToString().Contains(@0)");
                }
                else
                {
                    searchPredicate.Append($"({searchItem.PropertyName} != null && {searchItem.PropertyName}.ToLower().Contains(@0))");
                }

                gridResults = gridResults.Where(searchPredicate.ToString(), searchItem.SearchQuery);
                counter++;
            }
        }

        return gridResults;
    }

    public static IQueryable<T> ApplySorting<T>(IQueryable<T> query, GridParametersModel gridParams)
    {
        if (!string.IsNullOrEmpty(gridParams.SortBy))
        {
            query = query.OrderBy(gridParams.SortBy + (gridParams.SortAsc ? " Asc" : " Desc"));
        }

        return query;
    }

    private static IQueryable<T> ApplyPaging<T>(IQueryable<T> query, GridParametersModel gridParams, int totalCount)
    {
        int skipAmount = gridParams.ItemsPerPage * (gridParams.SelectedPage - 1);

        if (totalCount < skipAmount)
        {
            gridParams.SelectedPage = 1;
            skipAmount = gridParams.ItemsPerPage * (gridParams.SelectedPage - 1);
        }

        query = query.Skip(skipAmount).Take(gridParams.ItemsPerPage);


        return query;
    }

}
