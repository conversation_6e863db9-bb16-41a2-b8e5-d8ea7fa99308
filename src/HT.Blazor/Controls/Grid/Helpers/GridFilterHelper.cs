﻿using HT.Blazor.Models.Grid;
using HT.Blazor.Models.Grid.Enums;
using System.Linq.Dynamic.Core;

namespace HT.Blazor.Controls.Grid.Helpers;

public static class GridFilterHelper
{
    public static IQueryable<T> ApplyFiltering<T>(IQueryable<T> query, GridParametersModel gridParams)
    {
        foreach (var item in gridParams.FilteredItems)
        {
            if (item.PropertyType == (int)PropertyType.IntType)
            {
                query = FilterBy_IntegerValues(query, item);
            }

            if (item.PropertyType == (int)PropertyType.GuidType)
            {
                query = FilterBy_GuidValues(query, item);
            }

            if (item.PropertyType == (int)PropertyType.DateTime)
            {
                query = FilterBy_NullableDateValues(query, item);
            }

            if (item.PropertyType == (int)PropertyType.DecimalRange)
            {
                query = FilterBy_DecimalRangeValues(query, item);
            }

            if (item.PropertyType == (int)PropertyType.Boolean)
            {
                query = FilterBy_BooleanValues(query, item);
            }
        }

        return query;
    }

    private static IQueryable<T> FilterBy_BooleanValues<T>(IQueryable<T> gridResults, GridFilterItemModel item)
    {
        bool[] values = item.Values.Select(x => Convert.ToBoolean(x.Id)).ToArray();

        var predicateBuilder = BuildQueryPredicate<bool>(item, values);

        gridResults = gridResults.Where(predicateBuilder.PredicateString, predicateBuilder.Values);

        return gridResults;
    }

    private static IQueryable<T> FilterBy_IntegerValues<T>(IQueryable<T> gridResults, GridFilterItemModel item)
    {
        int[] values = item.Values.Select(x => Convert.ToInt32(x.Id)).ToArray();

        var predicateBuilder = BuildQueryPredicate<int>(item, values);

        gridResults = gridResults.Where(predicateBuilder.PredicateString, predicateBuilder.Values);

        return gridResults;
    }

    private static IQueryable<T> FilterBy_GuidValues<T>(IQueryable<T> gridResults, GridFilterItemModel item)
    {
        var values = item.Values.Select(x => new Guid(x.Id)).ToArray();

        var predicateBuilder = BuildQueryPredicate<Guid>(item, values);

        gridResults = gridResults.Where(predicateBuilder.PredicateString, predicateBuilder.Values);

        return gridResults;
    }

    private static IQueryable<T> FilterBy_NullableDateValues<T>(IQueryable<T> gridResults, GridFilterItemModel item)
    {
        string predicateString = $"{item.PropertyName} >= \"{item.StartDate.Value.DateTime:yyyy-MM-dd HH:mm:ss}\" and {item.PropertyName} < \"{item.EndDate.Value.DateTime:yyyy-MM-dd HH:mm:ss}\"";

        gridResults = gridResults.Where(predicateString);

        return gridResults;
    }

    private static IQueryable<T> FilterBy_DecimalRangeValues<T>(IQueryable<T> gridResults, GridFilterItemModel item)
    {
        string predicateString = $"{item.PropertyName} >= \"{item.DecimalStart}\" and {item.PropertyName} <= \"{item.DecimalEnd}\"";

        gridResults = gridResults.Where(predicateString);

        return gridResults;
    }

    private static PredicateBuilder<T> BuildQueryPredicate<T>(GridFilterItemModel item, T[] values)
    {
        return QueryByContains<T>(item, values);
    }

    private static PredicateBuilder<T> QueryByContains<T>(GridFilterItemModel item, T[] values)
    {
        string[] names = item.PropertyName.Split('.');

        string predicate = names.Length == 1 ? "@0.Contains(" + item.PropertyName + ")" : $"{names[0]}.Any(x => @0.Contains({names[1]}))";

        return new PredicateBuilder<T>
        {
            PredicateString = predicate,
            Values = values.ToList(),
        };
    }

    private sealed class PredicateBuilder<T>
    {
        public string PredicateString { get; init; }

        public List<T> Values { get; init; }
    }
}
