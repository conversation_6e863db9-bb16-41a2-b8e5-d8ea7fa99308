﻿using HT.Blazor.Controls.Grid.Filters;
using HT.Blazor.Controls.Grid.Paging;
using HT.Blazor.Controls.Grid.Sorting;
using HT.Blazor.Models.Grid;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Text;

namespace HT.Blazor.Controls.Grid;

public class GridBase<TItem> : ComponentBase
{
    [Inject] public IJSRuntime JSRuntime { get; set; }

    [Parameter] public GridOptionsModel GridOptions { get; set; }

    [Parameter] public Func<GridParametersModel, Task<GridResultModel<TItem>>> GetDataAsync { get; set; }

    [Parameter] public RenderFragment HeaderTemplate { get; set; }

    [Parameter] public RenderFragment<TItem> ResultsTemplate { get; set; }

    [Parameter] public RenderFragment BodyTemplate { get; set; }

    [Parameter] public Func<TItem, Task> ClickGridItemAsync { get; set; }

    [Parameter] public bool FilterOnInput { get; set; } = true;

    [Parameter] public Func<IEnumerable<TItem>, GridExportModel> GetExportDataMapping { get; set; }

    public IEnumerable<TItem> Results { get; set; }
    public IEnumerable<TItem> ExportResults { get; set; }

    public int TotalCount { get; set; }

    public bool Exporting { get; set; }

    [Parameter] public bool NoHov { get; set; }

    // Multi-select functionality
    [Parameter] public bool EnableRowSelection { get; set; } = false;
    [Parameter] public EventCallback<List<TItem>> OnSelectedItemsChanged { get; set; }
    [Parameter] public Func<TItem, bool> IsRowSelectable { get; set; }

    public List<TItem> SelectedItems { get; set; } = new();
    public bool IsSelectAllChecked { get; set; }
    public bool IsSelectAllIndeterminate { get; set; }

    public GridSorting<TItem> GridSorting { get; set; }

    public GridPaging<TItem> GridPaging { get; set; }

    public GridFilters<TItem> GridFilters { get; set; }

    public bool IsLoading { get; set; }

    public bool ShowMobileFilters { get; set; }

    public async Task ToggleFilters()
    {
        ShowMobileFilters = !ShowMobileFilters;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await this.GetGridDataAsync();
        }
    }

    public async Task GetGridDataAsync()
    {
        this.IsLoading = true;
        this.StateHasChanged();

        var request = new GridParametersModel
        {
            ItemsPerPage = this.GridOptions.ItemsPerPage,
            SelectedPage = this.GridPaging.SelectedPage,

            SortBy = this.GridSorting.SortableItemSelected,
            SortAsc = this.GridOptions.SortAsc,

            SearchQueries = this.GridOptions.SearchableItems,
            FilteredItems = this.GridFilters.GetSelectedFilters(),

            Exporting = this.Exporting
        };

        var data = await this.GetDataAsync.Invoke(request);

        if (this.Exporting)
        {
            this.ExportResults = data.Results ?? new List<TItem>();
        }
        else
        {
            this.Results = data.Results ?? new List<TItem>();
            
            // Update selection state when results change
            if (EnableRowSelection)
            {
                UpdateSelectAllState();
            }
        }


        this.TotalCount = data.TotalCount;
        this.GridPaging.Refresh();

        this.IsLoading = false;
        this.StateHasChanged();
    }

    public async Task Reset()
    {
        await this.GridFilters.ResetFilters();
        await this.GetGridDataAsync();
    }

    public async Task ExportToCSV()
    {
        this.Exporting = true;
        await this.GetGridDataAsync();

        var gridExportModel = this.GetExportDataMapping.Invoke(this.ExportResults);
        byte[] fileContents = Encoding.ASCII.GetBytes(gridExportModel.CSVData);

        await JSRuntime.InvokeAsync<string>("exportAsFile", Convert.ToBase64String(fileContents), gridExportModel.FileName);

        this.Exporting = false;
        this.ExportResults = new List<TItem>();
    }

    // Selection management methods
    public async Task ToggleSelectAll()
    {
        if (this.Results == null) return;

        var selectableItems = this.Results.Where(item => IsRowSelectable?.Invoke(item) != false).ToList();

        // Toggle based on current state - if all are selected, deselect all; otherwise select all
        var allSelected = selectableItems.All(item => SelectedItems.Contains(item));

        if (allSelected)
        {
            // Deselect all items in current results
            foreach (var item in selectableItems)
            {
                SelectedItems.Remove(item);
            }
        }
        else
        {
            // Select all selectable items
            foreach (var item in selectableItems)
            {
                if (!SelectedItems.Contains(item))
                {
                    SelectedItems.Add(item);
                }
            }
        }

        UpdateSelectAllState();
        await OnSelectedItemsChanged.InvokeAsync(SelectedItems);
        StateHasChanged();
    }

    public async Task ToggleItemSelection(TItem item)
    {
        if (!SelectedItems.Remove(item))
        {
            SelectedItems.Add(item);
        }

        UpdateSelectAllState();
        await OnSelectedItemsChanged.InvokeAsync(SelectedItems);
        StateHasChanged();
    }

    public void UpdateSelectAllState()
    {
        if (this.Results == null)
        {
            IsSelectAllChecked = false;
            IsSelectAllIndeterminate = false;
            return;
        }

        var selectableItems = this.Results.Where(item => IsRowSelectable?.Invoke(item) != false).ToList();
        var selectedSelectableItems = selectableItems.Where(item => SelectedItems.Contains(item)).ToList();

        if (selectedSelectableItems.Count == 0)
        {
            IsSelectAllChecked = false;
            IsSelectAllIndeterminate = false;
        }
        else if (selectedSelectableItems.Count == selectableItems.Count)
        {
            IsSelectAllChecked = true;
            IsSelectAllIndeterminate = false;
        }
        else
        {
            IsSelectAllChecked = false;
            IsSelectAllIndeterminate = true;
        }
    }

    public void ClearSelection()
    {
        SelectedItems.Clear();
        UpdateSelectAllState();
        StateHasChanged();
    }

    public bool IsItemSelected(TItem item)
    {
        return SelectedItems.Contains(item);
    }


}
