@using HT.Blazor.Controls.Fields.CheckboxField
@typeparam TItem
@inherits GridResultsBase<TItem>

@if (this.Grid.BodyTemplate != null)
{
    <table class="table">
        <thead>
            <tr>
                @if (Grid.EnableRowSelection)
                {
                    <th style="width: 40px;">
                        <input type="checkbox" 
                               checked="@Grid.IsSelectAllChecked"
                               @onchange="@((e) => Grid.ToggleSelectAll())"
                               @ref="selectAllCheckbox" />
                    </th>
                }
                @this.Grid.HeaderTemplate
            </tr>
        </thead>
        @if (this.Grid.Results != null)
        {
            <tbody>
                @this.Grid.BodyTemplate
            </tbody>
        }
    </table>
}
else
{
    <table class="table">
        <thead>
            <tr>
                @if (Grid.EnableRowSelection)
                {
                    <th style="width: 40px;">
                        <input type="checkbox" 
                               checked="@Grid.IsSelectAllChecked"
                               @onchange="@((e) => Grid.ToggleSelectAll())"
                               @ref="selectAllCheckbox" />
                    </th>
                }
                @this.Grid.HeaderTemplate
            </tr>
        </thead>
        @if (this.Grid.Results != null)
        {
            <tbody>
                @foreach (var item in this.Grid.Results)
                {
                    <tr class="@(this.Grid.NoHov ? "nohov": "")" @onclick="(() => ClickItem(item))">
                        @if (Grid.EnableRowSelection)
                        {
                            <td @onclick:stopPropagation="true" style="width: 40px;">
                                @if (Grid.IsRowSelectable?.Invoke(item) != false)
                                {
                                    <input type="checkbox"
                                           checked="@Grid.IsItemSelected(item)"
                                           @onchange="@((e) => Grid.ToggleItemSelection(item))" />
                                }
                            </td>
                        }
                        @this.Grid.ResultsTemplate(item)
                    </tr>
                }
            </tbody>
        }
    </table>
}

@if(this.Grid.TotalCount == 0)
{
    <div class="no-results">No results found</div>
}

@if (this.Grid.Results == null) {
<HT.Blazor.Controls.Loading.Loading />
}