﻿@using HT.Blazor.Models.Grid.Enums;
@typeparam TItem
@inherits GridFiltersBase<TItem>

@if (this.Grid.GridOptions.Filters.Any() || this.Grid.GridOptions.SearchableItems.Any())
{

    @if (this.Grid.GridOptions.Filters.Any())
    {
        foreach (var filter in Grid.GridOptions.Filters)
        {
            @if (filter.PropertyType == (int)PropertyType.DateTime)
            {
                <HT.Blazor.Controls.Grid.Filters.DateTimeRange.DateTimeRange GridFiltersControl="this" FilterItem="filter" />
            }

            @if (filter.PropertyType == (int)PropertyType.IntType 
                || filter.PropertyType == (int)PropertyType.GuidType
                || filter.PropertyType == (int)PropertyType.Boolean)
            {
                <HT.Blazor.Controls.Grid.Filters.MulipleSelect.MulipleSelectFilter GridFiltersControl="this" FilterItem="filter" />
            }

            @if (filter.PropertyType == (int)PropertyType.DecimalRange)
            {
                <HT.Blazor.Controls.Grid.Filters.DecimalRange.DecimalRange GridFiltersControl="this" FilterItem="filter" />
            }
        }
    }

    @if (this.Grid.GridOptions.SearchableItems.Any())
    {
        @foreach (var searchItem in this.Grid.GridOptions.SearchableItems)
        {
            <div class="search b">
                <label>@searchItem.DisplayName</label>
                <input type="text"
                       @bind-value="@searchItem.SearchQuery"
                       @onkeydown="SearchQueryKeyDown"
                       @oninput="@(e => UpdateSearchQuery(e, searchItem))" />
            </div>
        }

        <div class="button small secondary" @onclick="RefreshFilters">Search</div>
    }
}
