﻿using HT.Blazor.Models.Grid;
using Microsoft.AspNetCore.Components;

namespace HT.Blazor.Controls.Grid.Filters.DecimalRange;

public class DecimalRangeBase<TItem> : ComponentBase
{
    [Parameter] public GridFilters<TItem> GridFiltersControl { get; set; }

    [Parameter] public GridFilterItemModel FilterItem { get; set; }

    public async Task ValueEntered()
    {
        if (this.GridFiltersControl.Grid.FilterOnInput)
        {
            this.GridFiltersControl.Grid.GridPaging.SelectedPage = 1;
            await this.GridFiltersControl.RefreshFilters();
        }
    }
}
