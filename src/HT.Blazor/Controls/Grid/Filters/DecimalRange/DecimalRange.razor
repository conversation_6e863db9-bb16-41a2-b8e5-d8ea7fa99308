﻿@typeparam TItem
@inherits DecimalRangeBase<TItem>

<div class="multiselect b">
    <label>@FilterItem.DisplayName</label>
    <ul>
            <li>
                <input type="text" @bind="@this.FilterItem.DecimalStart" @onfocusout="(() => ValueEntered())" />
                <span> - </span>
                <input type="text" @bind="@this.FilterItem.DecimalEnd" @onfocusout="(() => ValueEntered())" />
            </li>
        
    </ul>
</div>