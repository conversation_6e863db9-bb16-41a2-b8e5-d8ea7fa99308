﻿using HT.Blazor.Models.Field;
using HT.Blazor.Models.Grid;
using Microsoft.AspNetCore.Components;

namespace HT.Blazor.Controls.Grid.Filters.MulipleSelect;

public class MulipleSelectFilterBase<TItem> : ComponentBase
{
    [Parameter] public GridFilters<TItem> GridFiltersControl { get; set; }

    [Parameter] public GridFilterItemModel FilterItem { get; set; }

    private List<DropdownListItem> filtersList = [];

    public bool HideSearch { get; set; }

    protected override void OnInitialized()
    {
        this.filtersList = this.FilterItem.Values.ToList();
        this.HideSearch = FilterItem.Values.Count <= 5;
    }

    public async Task ValueClicked(DropdownListItem selectListItem)
    {
        selectListItem.Selected = !selectListItem.Selected;

        if (this.GridFiltersControl.Grid.FilterOnInput)
        {
            this.GridFiltersControl.Grid.GridPaging.SelectedPage = 1;
            await this.GridFiltersControl.RefreshFilters();
        }
    }

    private string _searchQuery = string.Empty;

    public string SearchQuery
    {
        get => this._searchQuery;
        set
        {
            if (this._searchQuery != value)
            {
                this._searchQuery = value;

                this.FilterItem.Values = this.filtersList
                    .Where(q => q.Name.Contains(this._searchQuery, StringComparison.CurrentCultureIgnoreCase))
                    .ToList();
            }
        }
    }
}
