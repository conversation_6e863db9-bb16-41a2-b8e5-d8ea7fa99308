﻿@typeparam TItem
@inherits MulipleSelectFilterBase<TItem>

<div class="multiselect b">
<label>@FilterItem.DisplayName</label>
<ul>

    @if (!HideSearch)
    {
        <input @bind-value=@SearchQuery @bind-value:event="oninput" type="text" placeholder="Search" />
    }

    @foreach (var value in FilterItem.Values)
    {
        <li>
            <input type="checkbox" @bind="@value.Selected" @onclick="(() => ValueClicked(value))" />
            <label @onclick="(() => ValueClicked(value))">@value.Name</label>
        </li>
    }
</ul>
</div>