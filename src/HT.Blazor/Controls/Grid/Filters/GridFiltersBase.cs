﻿using HT.Blazor.Models.Grid;
using HT.Blazor.Models.Grid.Enums;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;

namespace HT.Blazor.Controls.Grid.Filters;

public class GridFiltersBase<TItem> : ComponentBase
{
    [Parameter] public Grid<TItem> Grid { get; set; }

    public bool IsFiltersShowing { get; set; }

    public void FilterButtonClicked()
    {
        this.IsFiltersShowing = !this.IsFiltersShowing;
    }

    public async Task ResetFilters()
    {
        foreach (var filter in this.Grid.GridOptions.Filters.Where(s => s.Values != null && s.Values.Any(a => a.Selected)))
        {
            foreach (var item in filter.Values)
            {
                item.Selected = false;
            }
        }

        foreach (var item in this.Grid.GridOptions.SearchableItems)
        {
            item.SearchQuery = null;

        }

        foreach (var filter in this.Grid.GridOptions.Filters)
        {
            filter.StartDate = null;
            filter.EndDate = null;
            filter.DecimalStart = null;
            filter.DecimalEnd = null;
        }

        this.Grid.GridPaging.SelectedPage = 1;

        this.StateHasChanged();
    }

    public async Task RefreshFilters()
    {
        if (this.Grid.ShowMobileFilters)
        {
            await this.Grid.ToggleFilters();
        }

        this.Grid.GridPaging.SelectedPage = 1;

        await this.Grid.GetGridDataAsync();
    }

    public List<GridFilterItemModel> GetSelectedFilters()
    {
        var filters = new List<GridFilterItemModel>();

        foreach (var filter in this.Grid.GridOptions.Filters.Where(s => s.Values != null && s.Values.Any(a => a.Selected)))
        {
            filters.Add(new GridFilterItemModel
            {
                PropertyName = filter.PropertyName,
                Values = filter.Values.Where(s => s.Selected).ToList(),
                PropertyType = filter.PropertyType,
                DisplayName = filter.DisplayName,

            });
        }

        foreach (var filter in this.Grid.GridOptions.Filters.Where(s => s.PropertyType == (int)PropertyType.DateTime && s.StartDate != null && s.EndDate != null))
        {
            filters.Add(new GridFilterItemModel
            {
                PropertyName = filter.PropertyName,
                PropertyType = filter.PropertyType,
                DisplayName = filter.DisplayName,
                StartDate = filter.StartDate,
                EndDate = filter.EndDate,
            });
        }

        foreach (var filter in this.Grid.GridOptions.Filters.Where(s => s.PropertyType == (int)PropertyType.DecimalRange && s.DecimalRangeOk))
        {
            filters.Add(new GridFilterItemModel
            {
                PropertyName = filter.PropertyName,
                PropertyType = filter.PropertyType,
                DisplayName = filter.DisplayName,
                DecimalStart = filter.DecimalStart,
                DecimalEnd = filter.DecimalEnd,
            });
        }

        return filters;
    }

    public void UpdateSearchQuery(ChangeEventArgs e, GridSearchItemModel searchItem)
    {
        searchItem.SearchQuery = e.Value.ToString();
    }

    public async Task SearchQueryKeyDown(KeyboardEventArgs e)
    {
        if (e.Code == "Enter" || e.Code == "NumpadEnter")
        {
            this.Grid.GridPaging.SelectedPage = 1;
            await this.Grid.GetGridDataAsync();
        }
    }
}
