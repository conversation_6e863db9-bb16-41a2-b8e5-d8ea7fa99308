﻿using BlazorDateRangePicker;
using HT.Blazor.Models.Grid;
using Microsoft.AspNetCore.Components;

namespace HT.Blazor.Controls.Grid.Filters.DateTimeRange;

public class DateTimeRangeBase<TItem> : ComponentBase
{
    [Parameter] public GridFilters<TItem> GridFiltersControl { get; set; }

    [Parameter] public GridFilterItemModel FilterItem { get; set; }

    public async Task OnRangeSelect(DateRange range)
    {
        this.GridFiltersControl.Grid.GridPaging.SelectedPage = 1;
        await this.GridFiltersControl.RefreshFilters();
    }

}
