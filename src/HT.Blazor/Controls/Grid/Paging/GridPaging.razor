﻿@using HT.Blazor.Models.Grid;
@typeparam TItem
@inherits GridPagingBase<TItem>

<div class="paginator">
    <select value=@Grid.GridOptions.ItemsPerPage @onchange="OnItemsPerPageChangedAsync" title="Records per Page" disabled="@Grid.GridOptions.NoPaging">
        <option value="5">5</option>
        <option value="10">10</option>
        <option value="25">25</option>
        <option value="50">50</option>
        <option value="100">100</option>
    </select>

    <div class="paginator-range-actions">
        <span class="paginator-range">@this.SelectedPage / @this.PageCount</span>
        <div class="paginator-range-buttons">
            <button class="first-page" title="First Page" @onclick=@(async () => await NavigateToPage(PagingAction.First)) disabled=@(SelectedPage <= 1)>«</button>
            <button class="navigate-before" title="Previous Page" @onclick=@(async () => await NavigateToPage(PagingAction.Previous)) disabled=@(SelectedPage <= 1)>←</button>

            @foreach (var pageItem in DisplayedPages)
            {
                <button class="@(pageItem == SelectedPage ? "selected" : "") page-number" @onclick=@(async () => await NavigateToPage(PagingAction.SpecificPage, pageItem))>@pageItem</button>
            }

            <button class="navigate-next" title="Next Page" @onclick="@(async () => await NavigateToPage(PagingAction.Next))" disabled="@(SelectedPage == PageCount)">→</button>
            <button class="last-page" title="Last Page" @onclick=@(async () => await NavigateToPage(PagingAction.Last)) disabled=@(SelectedPage == PageCount)>»</button>
        </div>
    </div>

    <div class="results-total"><span>@Grid.TotalCount</span> <span>Results</span></div>
</div>