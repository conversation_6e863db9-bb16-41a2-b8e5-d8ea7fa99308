﻿using HT.Blazor.Models.Grid;
using Microsoft.AspNetCore.Components;

namespace HT.Blazor.Controls.Grid.Paging;

public class GridPagingBase<TItem> : ComponentBase
{
    [Parameter] public Grid<TItem> Grid { get; set; }

    public int SelectedPage { get; set; } = 1;
    public List<int> DisplayedPages { get; set; } = [];
    public int PageCount => (int)Math.Ceiling((double)this.Grid.TotalCount / this.Grid.GridOptions.ItemsPerPage);

    public async void OnItemsPerPageChangedAsync(ChangeEventArgs e)
    {
        this.Grid.GridOptions.ItemsPerPage = Convert.ToInt32(e.Value);
        await this.Grid.GetGridDataAsync();
    }

    public void CalculateDisplayedPages()
    {
        this.DisplayedPages.Clear();
        int pageCount = 1;

        if (this.PageCount <= 5)
        {
            while (pageCount <= this.PageCount)
            {
                this.DisplayedPages.Add(pageCount);
                pageCount += 1;
            }
        }
        else if (this.SelectedPage <= 3)
        {
            while (pageCount <= 5)
            {
                this.DisplayedPages.Add(pageCount);
                pageCount += 1;
            }
        }
        else
        {
            pageCount = this.SelectedPage - 2;
            int pageEnd = this.SelectedPage + 2;

            if (pageEnd == this.PageCount + 1)
            {
                pageCount = this.SelectedPage - 3;
                pageEnd = this.PageCount;
            }

            if (pageEnd == this.PageCount + 2)
            {
                pageCount = this.SelectedPage - 4;
                pageEnd = this.PageCount;
            }

            while (pageCount <= pageEnd)
            {
                this.DisplayedPages.Add(pageCount);
                pageCount += 1;
            }
        }
    }

    public async Task NavigateToPage(PagingAction direction, int selectedPage = 1)
    {
        switch (direction)
        {
            case PagingAction.Default:
                break;
            case PagingAction.First:
                this.SelectedPage = 1;
                break;
            case PagingAction.Previous:
                this.SelectedPage--;
                break;
            case PagingAction.Next:
                this.SelectedPage++;
                break;
            case PagingAction.Last:
                this.SelectedPage = this.PageCount;
                break;
            case PagingAction.SpecificPage:
                this.SelectedPage = selectedPage;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(direction), direction, null);
        }

        this.CalculateDisplayedPages();
        await this.Grid.GetGridDataAsync();
    }

    public int MaxPageSize => 100;

    public void Reset()
    {
        this.SelectedPage = 1;
    }

    public void Refresh()
    {
        this.CalculateDisplayedPages();
        this.StateHasChanged();
    }


}
