﻿namespace HT.Blazor.Models.Grid;

public class GridOptionsModel
{
    public List<GridSearchItemModel> SearchableItems { get; set; } = [];

    public List<GridFilterItemModel> Filters { get; set; } = [];

    public bool SortAsc { get; set; }

    public List<GridSortableItemModel> SortableItems { get; set; } = [];

    public bool NoPaging { get; set; }

    public int ItemsPerPage { get; set; } = 25;
}
