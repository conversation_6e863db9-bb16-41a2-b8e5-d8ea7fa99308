﻿namespace HT.Blazor.Models.Grid;

public class GridParametersModel
{
    public int SelectedPage { get; set; }
    public int ItemsPerPage { get; set; }

    public string SearchQuery { get; set; }
    public List<GridSearchItemModel> SearchQueries { get; set; } = [];
    public List<string> SearchableItems { get; set; } = [];

    public string SortBy { get; set; }
    public bool SortAsc { get; set; }

    public List<GridFilterItemModel> FilteredItems { get; set; } = [];

    public Guid ParameterId { get; set; }

    public List<Guid> ParameterIds { get; set; }

    public bool Exporting { get; set; }
}
