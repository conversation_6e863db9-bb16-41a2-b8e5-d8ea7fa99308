﻿using HT.Blazor.Models.Field;

namespace HT.Blazor.Models.Grid;

public class GridFilterItemModel
{
    public string DisplayName { get; set; }
    public string PropertyName { get; set; }
    public int PropertyType { get; set; }
    public List<DropdownListItem> Values { get; set; }


    public DateTimeOffset? StartDate { get; set; }
    public DateTimeOffset? EndDate { get; set; }

    public decimal? DecimalStart { get; set; }
    public decimal? DecimalEnd { get; set; }

    public bool DecimalRangeOk => DecimalStart != null && DecimalEnd != null && DecimalStart <= DecimalEnd;

}
