﻿namespace HT.Blazor.Models.Form;

public class FormFieldIdentifierModel
{
    public string FieldName { get; set; }

    public Guid Id { get; set; }

    public string ObjectNamespace { get; set; }

    public FormFieldIdentifierModel()
    { }

    public FormFieldIdentifierModel(string fieldName)
    {
        this.FieldName = fieldName;
    }

    public FormFieldIdentifierModel(string objectNamespace, string fieldName)
    {
        this.ObjectNamespace = objectNamespace;
        this.FieldName = fieldName;
    }

    public FormFieldIdentifierModel(Guid id, string objectNamespace, string fieldName)
    {
        this.Id = id;
        this.ObjectNamespace = objectNamespace;
        this.FieldName = fieldName;
    }
}
