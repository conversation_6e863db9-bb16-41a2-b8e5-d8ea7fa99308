﻿namespace HT.Blazor.Models.Form;

public class FormResponseModel<T>
{
    public bool Success => !Errors.Any();

    public T Data { get; set; }

    public List<FormFieldErrorModel> Errors { get; set; } = [];

    public FormResponseModel()
    {
    }

    public FormResponseModel(T data)
    {
        this.Data = data;
    }

    public FormResponseModel<T> AddError(string message)
    {
        Errors.Add(new FormFieldErrorModel(true, message, new FormFieldIdentifierModel("")));

        return this;
    }

    public FormResponseModel<T> AddError(string message, FormFieldIdentifierModel fieldIdentifier)
    {
        Errors.Add(new FormFieldErrorModel(true, message, fieldIdentifier));

        return this;
    }
}
