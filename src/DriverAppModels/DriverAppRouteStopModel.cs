﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace DriverAppModels;

public class DriverAppRouteStopModel
{
    public Guid Id { get; set; }

    public int StopNumber { get; set; }

    public bool Checked { get; set; }

    public Guid? HaulierStopId { get; set; }

    public string AddressName { get; set; }

    public string DeliveryAddressLine1 { get; set; }

    public string DeliveryAddressLine2 { get; set; }

    public string DeliveryAddressLine3 { get; set; }

    public string DeliveryAddressLine4 { get; set; }

    public string DeliveryAddressPostcode { get; set; }

    public string PhoneNumber { get; set; }

    public string MobileNumber { get; set; }

    public string Time { get; set; }

    public string PhoneNumbers
    {
        get
        {
            string phoneNumbers = string.Empty;

            if (!string.IsNullOrWhiteSpace(PhoneNumber) && !string.IsNullOrWhiteSpace(MobileNumber))
            {
                phoneNumbers = $"{PhoneNumber} / {MobileNumber}";
            }
            else if (string.IsNullOrWhiteSpace(PhoneNumber) && !string.IsNullOrWhiteSpace(MobileNumber))
            {
                phoneNumbers = MobileNumber;
            }
            else if (!string.IsNullOrWhiteSpace(PhoneNumber) && string.IsNullOrWhiteSpace(MobileNumber))
            {
                phoneNumbers = PhoneNumber;
            }

            return phoneNumbers;
        }
    }

    public string FullDeliveryAddress => $"{DeliveryAddressLine1}, {DeliveryAddressLine2}, {DeliveryAddressLine3}, {DeliveryAddressLine4}, {DeliveryAddressPostcode}";

    public decimal? DeliveryAddressLatitude { get; set; }
    public decimal? DeliveryAddressLongitude { get; set; }

    public int Status { get; set; }

    public string Notes { get; set; }

    public bool PhotoRequired { get; set; }

    public bool SignatureRequired { get; set; }

    public int Type { get; set; }

    public string TypeString => ((HaulierStopType)Type).GetDescription();

    public List<PhotoModel> Photos { get; set; } = new List<PhotoModel>();
}

public class PhotoModel
{
    public string Url { get; set; }
    public string PhotoName { get; set; }
}

public enum HaulierStopType
{
    None = 0,
    [Description("Drop Off")]
    DropOff = 1,
    [Description("Pick Up")]
    PiclUp = 2,
}

public static class EnumExtensions
{
    public static string GetDescription(this Enum value)
    {
        var fieldInfo = value.GetType().GetField(value.ToString());
        if (fieldInfo == null)
        {
            return value.ToString();
        }

        var attribute = fieldInfo.GetCustomAttribute<DescriptionAttribute>();
        return attribute?.Description ?? value.ToString();
    }
}
