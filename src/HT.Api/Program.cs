using System.Globalization;
using System.Text;
using DinkToPdf;
using DinkToPdf.Contracts;
using HT.Api.Services;
using HT.Api.Services.Common.ActivityLog;
using HT.Api.Services.Common.Address;
using HT.Api.Services.Common.AzureStorage;
using HT.Api.Services.Common.Email;
using HT.Api.Services.Common.Google.Maps;
using HT.Api.Services.Common.PDF;
using HT.Api.Services.Common.RouteXL;
using HT.Api.Services.Customer;
using HT.Api.Services.Dashboard;
using HT.Api.Services.DriverAppService;
using HT.Api.Services.ETicket;
using HT.Api.Services.Haulier;
using HT.Api.Services.HaulierRoute;
using HT.Api.Services.Hubs;
using HT.Api.Services.Order;
using HT.Api.Services.Purchase;
using HT.Api.Services.Round;
using HT.Api.Services.Route;
using HT.Api.Services.Setting;
using HT.Api.Services.Supplier;
using HT.Api.Services.Weighbridge.Contract;
using HT.Api.Services.Weighbridge.Job;
using HT.Database;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Localization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.IdentityModel.Tokens;

namespace HT.Api;

public static class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        ConfigureServices(builder);

        var app = builder.Build();

        ConfigurePipelines(app);

        app.Run();
    }

    public static void ConfigureServices(WebApplicationBuilder builder)
    {
        builder.Services.Configure<RequestLocalizationOptions>(options =>
        {
            options.DefaultRequestCulture = new RequestCulture("en-GB");
        });

        builder.Services.AddDbContext<DatabaseContext>(o => o.UseSqlServer(builder.Configuration["ConnectionStrings:DefaultConnection"]));

        builder.Services.AddControllers();
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen();
        builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = false,
                ValidateAudience = false,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Security:Code"]!)),
            };
        });

        builder.Services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();

        // Common  
        builder.Services.AddScoped<ActivityLogService>();
        builder.Services.AddScoped<PortalEventService>();
        builder.Services.AddScoped<CurrentUserService>();
        builder.Services.AddScoped<AzureStorageService>();
        builder.Services.AddScoped<UrlToPdf>();
        builder.Services.AddScoped<EmailService>();
        builder.Services.AddScoped<EmailInvoiceService>();
        builder.Services.AddScoped<FavouriteAddressService>();
        builder.Services.AddScoped<ForgotPasswordService>();
        builder.Services.AddScoped<NewUserEmailService>();
        builder.Services.AddSingleton<IConverter>(new SynchronizedConverter(new PdfTools()));
        builder.Services.AddScoped<GoogleRoutesService>();
        builder.Services.AddScoped<GoogleGeocodeService>();
        builder.Services.AddScoped<DriverAppService>();


        // Services
        builder.Services.AddScoped<GetDashboardService>();
        builder.Services.AddScoped<GetDashboardSummaryService>();
        builder.Services.AddScoped<GetOrdersDashboardSummaryService>();
        builder.Services.AddScoped<GetSiteDashboardService>();

        builder.Services.AddScoped<CustomerSearchService>();
        builder.Services.AddScoped<AddressService>();
        builder.Services.AddScoped<ETicketService>();
        builder.Services.AddScoped<ETicketPricingService>();
        builder.Services.AddScoped<ContractService>();
        builder.Services.AddScoped<ContractSearchService>();
        builder.Services.AddScoped<ContractAllowanceService>();
        builder.Services.AddScoped<ContractPricingService>();

        builder.Services.AddScoped<OrderService>();
        builder.Services.AddScoped<OrderLineService>();

        builder.Services.AddScoped<CustomerPaymentService>();

        builder.Services.AddScoped<HaulierService>();
        builder.Services.AddScoped<HaulierRouteService>();
        builder.Services.AddScoped<HaulierRouteCopyStopService>();
        builder.Services.AddScoped<HaulierRouteProductService>();
        builder.Services.AddScoped<JobService>();
        builder.Services.AddScoped<ProductService>();
        builder.Services.AddScoped<ProductPriceService>();
        builder.Services.AddScoped<ProofOfDeliveryService>();
        builder.Services.AddScoped<PurchaseService>();
        builder.Services.AddScoped<OwingsByTagReportService>();
        builder.Services.AddScoped<ReportService>();
        builder.Services.AddScoped<RoundService>();
        builder.Services.AddScoped<RoundOrderService>();
        builder.Services.AddScoped<RouteService>();
        builder.Services.AddScoped<SupplierService>();
        builder.Services.AddScoped<OrderService>();

        builder.Services.AddScoped<WebsitePageService>();
        builder.Services.AddScoped<WebsiteMediaService>();
        builder.Services.AddScoped<WebsiteLayoutService>();
        builder.Services.AddScoped<GenerateWebsitePageService>();

        builder.Services.AddScoped<CustomerService>();

        builder.Services.AddScoped<WebsiteActivityService>();
        builder.Services.AddScoped<RouteXlService>();

        builder.Services.AddSignalR();
    }

    public static void ConfigurePipelines(WebApplication app)
    {
        app.UseRequestLocalization(new RequestLocalizationOptions
        {
            DefaultRequestCulture = new RequestCulture("en-GB"),
            SupportedCultures = [new CultureInfo("en-GB")],
            SupportedUICultures = [new CultureInfo("en-GB")],
        });

        using (var scope = app.Services.CreateScope())
        {
            var db = scope.ServiceProvider.GetRequiredService<DatabaseContext>();
            db.Database.Migrate();
        }

        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseHttpsRedirection();

        app.UseAuthorization();

        app.MapControllers();
        app.MapHub<UserHub>(UserHub.HubUrl);
    }
}
