﻿using System.Xml;

namespace HT.Api.Common.Extensions;

public class PageData
{
    public string Url { get; set; }
    public DateTime LastModified { get; set; }
}

public static class SitemapGenerator
{
    public static byte[] GenerateSitemapBytes(List<PageData> pages)
    {
        // Create a new XML document for the sitemap
        var doc = new XmlDocument();

        // Create the root element
        var root = doc.CreateElement("urlset");
        root.SetAttribute("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9");
        doc.AppendChild(root);

        // Loop through the pages and add them to the sitemap
        foreach (var page in pages)
        {
            AddUrl(root, page.Url, page.LastModified);
        }

        // Convert the XML document to a byte array
        byte[] sitemapBytes;
        using (var stream = new MemoryStream())
        {
            doc.Save(stream);
            sitemapBytes = stream.ToArray();
        }

        return sitemapBytes;
    }

    private static void AddUrl(XmlElement root, string loc, DateTime lastMod)
    {
        var url = root.OwnerDocument.CreateElement("url");
        var locElem = root.OwnerDocument.CreateElement("loc");
        locElem.InnerText = loc;
        url.AppendChild(locElem);

        var lastModElem = root.OwnerDocument.CreateElement("lastmod");
        lastModElem.InnerText = lastMod.ToString("yyyy-MM-ddTHH:mm:sszzz");
        url.AppendChild(lastModElem);

        root.AppendChild(url);
    }
}
