﻿using Microsoft.EntityFrameworkCore;

namespace HT.Api.Common.Extensions;

public static class ChangeTrackerExtensions
{
    public static string GetChangedProperties<T>(this DbContext context, T entity)
        where T : class
    {
        var entry = context.Entry(entity);
        var changedProperties = new List<string>();

        if (entry.State == EntityState.Modified)
        {
            var original = entry.OriginalValues;
            var current = entry.CurrentValues;

            foreach (var propertyEntry in original.Properties)
            {
                string propertyName = propertyEntry.Name;
                object originalValue = original[propertyEntry];
                object currentValue = current[propertyEntry];

                if (!Equals(originalValue, currentValue))
                {
                    string propertyChangeString = $"Property: {propertyName}, OriginalValue: {originalValue}, NewValue: {currentValue}";
                    changedProperties.Add(propertyChangeString);
                }
            }

            if (changedProperties.Count > 0)
            {
                return string.Join(Environment.NewLine, changedProperties);
            }
        }

        return null;
    }
}
