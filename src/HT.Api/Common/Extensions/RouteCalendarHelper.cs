﻿using HT.Shared.Models.Route.Calendar;

namespace HT.Api.Common.Extensions;

public static class RouteCalendarHelper
{
    public static List<RouteCalendarDayModel> GetSevenDays(DateTime startDate)
    {
        List<RouteCalendarDayModel> sevenDays = [];

        for (int i = 0; i < 7; i++)
        {
            sevenDays.Add(new RouteCalendarDayModel
            {
                Date = startDate.AddDays(i),
            });
        }

        return sevenDays;
    }
}
