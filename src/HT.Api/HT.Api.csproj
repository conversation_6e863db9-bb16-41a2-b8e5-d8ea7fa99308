﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<ItemGroup>
	  <None Remove="libwkhtmltox.dll" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="libwkhtmltox.dll">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </EmbeddedResource>
	</ItemGroup>

	<ItemGroup>

		<!-- Swagger -->
		<PackageReference Include="BlazorDateRangePicker" Version="5.3.0" />
		<PackageReference Include="CsvHelper" Version="33.0.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="7.1.0" />

		<!-- Database -->
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>

		<!-- JWT -->
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />

		<!-- OpenAI -->
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.0" />
		<PackageReference Include="OpenAI" Version="1.11.0" />

		<!-- Azure Storage -->
		<PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
		
		<!-- PDF -->
		<PackageReference Include="DinkToPdf" Version="1.0.8" />

		<!-- Markdown -->
		<PackageReference Include="Markdig" Version="0.38.0" />

		<!-- Email -->
		<PackageReference Include="Postmark" Version="5.2.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
		<PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.0.2" />

	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\DriverAppModels\DriverAppModels.csproj" />
		<ProjectReference Include="..\HT.Database\HT.Database.csproj" />
		<ProjectReference Include="..\HT.Shared\HT.Shared.csproj" />
	</ItemGroup>

</Project>
