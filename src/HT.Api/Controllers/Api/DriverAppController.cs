﻿using HT.Api.Services;
using HT.Api.Services.Common.AzureStorage;
using HT.Api.Services.DriverAppService;
using HT.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers.Api;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class DriverAppController(DriverAppService driverAppService, DatabaseContext db, CurrentUserService currentUserService, AzureStorageService azureStorageService) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> ListRoutesApp()
    {
        return Ok(await driverAppService.ListDriverAppRoutesAsync());
    }

    [HttpPost]
    public async Task<IActionResult> ViewRoute(Guid id)
    {
        return Ok(await driverAppService.ViewRouteAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> UploadPhoto(Guid stopId, [FromForm] IFormFile file)
    { 
        if(file == null)
        {
            file = HttpContext.Request.Form.Files[0];
        }

        var haulierStopFile = new HaulierStopFile
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            HaulierStopId = stopId,
            UploadedUserId = currentUserService.UserId,
            UploadedDate = DateTime.UtcNow,
        };
        db.HaulierStopFiles.Add(haulierStopFile);

        string fileExtension = Path.GetExtension(file.FileName).ToLower();
        string contentType = file.ContentType;

        if (string.IsNullOrEmpty(contentType))
        {
            contentType = fileExtension switch
            {
                ".jpg" => "image/jpeg",
                ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".pdf" => "application/pdf",
                _ => "application/octet-stream",
            };
        }

        haulierStopFile.Name = file.FileName;
        haulierStopFile.AzureStoragePath = "stops/" + (haulierStopFile.Id + "/media/" + haulierStopFile.Id + "/" + file.FileName).ToLower();
        haulierStopFile.AzureStorageURL = await azureStorageService.UploadFileToBlobAsync(
                                                               "website",
                                                               haulierStopFile.AzureStoragePath,
                                                               file.OpenReadStream(),
                                                               contentType);

        await db.SaveChangesAsync();

        return Ok(new { Url = $"/uploads/{file.FileName}" });
    }

    [HttpPost]
    public async Task<IActionResult> UploadPhotoSignature(Guid stopId, string name, [FromForm] IFormFile file)
    {
        if (file == null)
        {
            file = HttpContext.Request.Form.Files[0];
        }

        var haulierStopFile = new HaulierStopFile
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            HaulierStopId = stopId,
            UploadedUserId = currentUserService.UserId,
            UploadedDate = DateTime.UtcNow,
            IsSignature = true,
            SignatureName = name,
        };
        db.HaulierStopFiles.Add(haulierStopFile);

        string fileExtension = Path.GetExtension(file.FileName).ToLower();
        string contentType = file.ContentType;

        if (string.IsNullOrEmpty(contentType))
        {
            contentType = fileExtension switch
            {
                ".jpg" => "image/jpeg",
                ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".pdf" => "application/pdf",
                _ => "application/octet-stream",
            };
        }

        haulierStopFile.Name = file.FileName;
        haulierStopFile.AzureStoragePath = "stops/" + (haulierStopFile.Id + "/media/" + haulierStopFile.Id + "/" + file.FileName).ToLower();
        haulierStopFile.AzureStorageURL = await azureStorageService.UploadFileToBlobAsync(
                                                               "website",
                                                               haulierStopFile.AzureStoragePath,
                                                               file.OpenReadStream(),
                                                               contentType);

        await db.SaveChangesAsync();

        return Ok(new { Url = $"/uploads/{file.FileName}" });
    }

}
