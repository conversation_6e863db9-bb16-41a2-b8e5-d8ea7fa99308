﻿using HT.Api.Services;
using HT.Api.Services.Common.Address;
using HT.Api.Services.Common.Email;
using HT.Api.Services.Common.PDF;
using HT.Api.Services.HaulierRoute;
using HT.Api.Services.Weighbridge.Job;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using HT.Shared.Models.ETicket;
using HT.Shared.Models.HaulierRoute;
using HT.Shared.Models.Invoice;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.Invoice;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class InvoiceController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly UrlToPdf _urlToPDF;
    private readonly EmailInvoiceService _emailInvoiceService;
    private readonly AddressService _addressService;
    private readonly ProofOfDeliveryService _proofOfDeliveryService;
    private readonly JobService _jobService;

    public InvoiceController(DatabaseContext db,
        CurrentUserService currentUserService,
        UrlToPdf urlToPDF,
        EmailInvoiceService emailInvoiceService,
        AddressService addressService,
        ProofOfDeliveryService proofOfDeliveryService,
        JobService jobService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _urlToPDF = urlToPDF;
        _emailInvoiceService = emailInvoiceService;
        _addressService = addressService;
        _proofOfDeliveryService = proofOfDeliveryService;
        _jobService = jobService;
    }

    [HttpPost]
    public async Task<IActionResult> ListSentInvoices([FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.Invoices
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.Status == (int)InvoiceStatus.Sent)
                   .Select(s => new InvoiceListItemModel
                   {
                       Id = s.Id,
                       DateTime = s.DateTime,
                       CustomerName = s.Customer.Name,
                       ContractName = s.Contract.Name,
                       InvoiceNumber = s.InvoiceNumber,
                       InvoiceCompanyNumber = s.InvoiceCompanyNumber,
                       Lines = s.InvoiceLines.Count,
                       Total = s.Total,
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListGeneratedInvoices([FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.Invoices
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.Status == (int)InvoiceStatus.Created)
                   .Select(s => new InvoiceListItemModel
                   {
                       Id = s.Id,
                       DateTime = s.DateTime,
                       CustomerName = s.Customer.Name,
                       ContractName = s.Contract.Name,
                       InvoiceNumber = s.InvoiceNumber,
                       InvoiceCompanyNumber = s.InvoiceCompanyNumber,
                       Lines = s.InvoiceLines.Count,
                       Total = s.Total,
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> GenerateInvoices([FromBody] InvoiceGenerateModel invoiceGenerateModel)
    {
        var eTicketsQuery = _db.ETickets
                               .Where(w => w.Type == (int)ETicketType.Waste)
                               .AsQueryable();

        if (invoiceGenerateModel.SiteId != Guid.Empty)
        {
            eTicketsQuery = eTicketsQuery.Where(w => w.SiteId == invoiceGenerateModel.SiteId);
        }

        if (invoiceGenerateModel.GenerateFor == 2)
        {
            eTicketsQuery = eTicketsQuery.Where(w => w.EntryDateTime >= invoiceGenerateModel.StartDate && w.EntryDateTime <= invoiceGenerateModel.EndDate);
        }

        var eTickets = await eTicketsQuery.Where(w => w.TenantId == _currentUserService.TenantId)
                                   .Where(s => s.Status == (int)ETicketStatus.Closed)
                                   .Select(s => new
                                   {
                                       ETicketId = s.Id,
                                       s.CustomerId,
                                       s.ContractId,
                                       s.EntryDateTime,

                                       s.TicketNumber,
                                       s.SiteETicketNumber,

                                       s.ProductPrice,
                                       ProductName = s.Contract.SitePermitProductLink.Product.Name,
                                       s.Contract.SitePermitProductLink.Product.ProductCode,

                                       s.HasSiteSurcharge,
                                       SurchargeName = s.SiteSurcharge.Name,
                                       s.SurchargePrice,
                                       s.NetWeight,

                                       s.TotalPrice,

                                       CompanyCode = s.Contract.SitePermit.Site.Company.Code,

                                       s.PricePerTonne,
                                       s.OrderNumber
                                   })
                                   .ToListAsync();


        int lastInvoiceNumber = (await _db.Invoices.Where(w => w.TenantId == _currentUserService.TenantId)
                                            .OrderByDescending(s => s.InvoiceNumber)
                                            .FirstOrDefaultAsync())?.InvoiceNumber ?? 0;

        var groupedETickets = eTickets.GroupBy(s => new { s.ContractId, s.CustomerId }).ToArray();

        foreach (var grouped in groupedETickets)
        {
            lastInvoiceNumber++;

            var invoice = new HT.Database.Invoice
            {
                Id = Guid.NewGuid(),
                TenantId = _currentUserService.TenantId,
                CustomerId = grouped.FirstOrDefault().CustomerId,
                ContractId = grouped.FirstOrDefault().ContractId,

                InvoiceNumber = lastInvoiceNumber,
                InvoiceCompanyNumber = grouped.FirstOrDefault().CompanyCode + "-" + lastInvoiceNumber,
                CreatedDateTime = DateTime.Now,
                DateTime = invoiceGenerateModel.InvoiceDate.Value.Date,
                Status = (int)InvoiceStatus.Created,
                OrderNumbers = string.Join(", ", grouped.Select(s => s.OrderNumber).Distinct())
            };
            _db.Invoices.Add(invoice);

            int lineNumber = 1;
            foreach (var eticket in grouped.OrderBy(o => o.TicketNumber))
            {
                var invoiceLine = new InvoiceLine
                {
                    Id = Guid.NewGuid(),
                    TenantId = _currentUserService.TenantId,
                    InvoiceId = invoice.Id,
                    ETicketId = eticket.ETicketId,
                    LineNumber = lineNumber,
                    Code = eticket.ProductCode,
                    Description = eticket.SiteETicketNumber + " - " + eticket.ProductName + " - " + eticket.EntryDateTime?.ToString("dd/MM/yyyy"),
                    Quantity = 1,
                    UnitPrice = eticket.ProductPrice,
                    UnitTotal = eticket.ProductPrice,
                    VATRate = 20.0m,
                    VATTotal = Math.Round(eticket.ProductPrice * 0.2m, 2),
                    Total = Math.Round(eticket.ProductPrice * 1.2m, 2)
                };

                if (eticket.PricePerTonne != null)
                {
                    invoiceLine.UnitPrice = eticket.PricePerTonne!.Value;
                    invoiceLine.Quantity = eticket.NetWeight / 1000;
                }

                _db.InvoiceLines.Add(invoiceLine);
                lineNumber++;

                if (eticket.HasSiteSurcharge)
                {
                    var isurchargeLine = new InvoiceLine
                    {
                        Id = Guid.NewGuid(),
                        TenantId = _currentUserService.TenantId,
                        InvoiceId = invoice.Id,
                        ETicketId = eticket.ETicketId,
                        LineNumber = lineNumber,
                        //Code
                        Description = eticket.SiteETicketNumber + " " + eticket.SurchargeName,
                        Quantity = 1,
                        UnitPrice = eticket.SurchargePrice,
                        UnitTotal = eticket.SurchargePrice,
                        VATRate = 20.0m,
                        VATTotal = Math.Round(eticket.SurchargePrice * 0.2m, 2),
                        Total = Math.Round(eticket.SurchargePrice * 1.2m, 2)
                    };
                    _db.InvoiceLines.Add(isurchargeLine);
                    lineNumber++;
                }
            }

            invoice.UnitTotal = Math.Round(invoice.InvoiceLines.Sum(s => s.UnitTotal), 2);
            invoice.VATTotal = Math.Round(invoice.InvoiceLines.Sum(s => s.VATTotal), 2);
            invoice.Total = Math.Round(invoice.InvoiceLines.Sum(s => s.Total), 2);
        }

        var eTicketDatabaseRecords = await _db.ETickets.Where(w => eTickets.Select(s => s.ETicketId).Contains(w.Id)).ToListAsync();

        foreach (var item in eTicketDatabaseRecords)
        {
            item.Status = (int)ETicketStatus.Invoiced;
        }

        await _db.SaveChangesAsync();

        return Ok(invoiceGenerateModel);
    }

    [HttpPost]
    public async Task<IActionResult> GenerateProductInvoices([FromBody] InvoiceGenerateModel invoiceGenerateModel)
    {
        var eTicketsQuery = _db.ETickets
                               .Where(w => w.Type == (int)ETicketType.Product)
                               .AsQueryable();

        if (invoiceGenerateModel.SiteId != Guid.Empty)
        {
            eTicketsQuery = eTicketsQuery.Where(w => w.SiteId == invoiceGenerateModel.SiteId);
        }

        if (invoiceGenerateModel.GenerateFor == 2)
        {
            eTicketsQuery = eTicketsQuery.Where(w => w.EntryDateTime >= invoiceGenerateModel.StartDate && w.EntryDateTime <= invoiceGenerateModel.EndDate);
        }

        var eTickets = await eTicketsQuery.Where(w => w.TenantId == _currentUserService.TenantId)
                                   .Where(s => s.Status == (int)ETicketStatus.Closed)
                                   .Select(s => new
                                   {
                                       ETicketId = s.Id,
                                       s.CustomerId,
                                       s.ContractId,
                                       s.TicketNumber,
                                       s.SiteETicketNumber,
                                       CompanyCode = s.Contract.SitePermit.Site.Company.Code,


                                       s.ProductPrice,

                                       s.ProductId,
                                       BoughtProduct = s.Product.Name,
                                       BoughtProductCode = s.Product.ProductCode,
                                       s.Tonnes,

                                       s.TotalPrice,
                                       s.VATPercent,
                                       s.OrderNumber
                                   })
                                   .ToListAsync();


        int lastInvoiceNumber = (await _db.Invoices.Where(w => w.TenantId == _currentUserService.TenantId)
                                            .OrderByDescending(s => s.InvoiceNumber)
                                            .FirstOrDefaultAsync())?.InvoiceNumber ?? 0;

        var groupedETickets = eTickets.GroupBy(s => new { s.ContractId, s.CustomerId, s.ProductId }).ToArray();

        foreach (var grouped in groupedETickets)
        {
            lastInvoiceNumber++;

            var invoice = new HT.Database.Invoice
            {
                Id = Guid.NewGuid(),
                TenantId = _currentUserService.TenantId,
                CustomerId = grouped.FirstOrDefault().CustomerId,
                ContractId = grouped.FirstOrDefault().ContractId,

                InvoiceNumber = lastInvoiceNumber,
                InvoiceCompanyNumber = grouped.FirstOrDefault().CompanyCode + "-" + lastInvoiceNumber,
                CreatedDateTime = DateTime.Now,
                DateTime = invoiceGenerateModel.InvoiceDate.Value.Date,
                Status = (int)InvoiceStatus.Created,
                OrderNumbers = string.Join(", ", grouped.Select(s => s.OrderNumber).Distinct())
            };
            _db.Invoices.Add(invoice);

            int lineNumber = 1;
            foreach (var eticket in grouped.OrderBy(o => o.TicketNumber))
            {
                var invoiceLine = new InvoiceLine
                {
                    Id = Guid.NewGuid(),
                    TenantId = _currentUserService.TenantId,
                    InvoiceId = invoice.Id,
                    ETicketId = eticket.ETicketId,
                    LineNumber = lineNumber,
                    Code = eticket.BoughtProductCode,
                    Description = eticket.SiteETicketNumber + " " + eticket.BoughtProduct + " (Tonnes)",
                    Quantity = eticket.Tonnes,
                    UnitPrice = eticket.ProductPrice,
                    UnitTotal = eticket.TotalPrice,
                    VATRate = eticket.VATPercent,
                };

                invoiceLine.VATTotal = (invoiceLine.VATRate > 0 && invoiceLine.UnitTotal > 0) ? Math.Round(invoiceLine.UnitTotal * invoiceLine.VATRate / 100, 2) : 0;
                invoiceLine.Total = Math.Round(invoiceLine.UnitTotal + invoiceLine.VATTotal, 2);

                _db.InvoiceLines.Add(invoiceLine);
                lineNumber++;
            }

            invoice.UnitTotal = Math.Round(invoice.InvoiceLines.Sum(s => s.UnitTotal), 2);
            invoice.VATTotal = Math.Round(invoice.InvoiceLines.Sum(s => s.VATTotal), 2);
            invoice.Total = Math.Round(invoice.InvoiceLines.Sum(s => s.Total), 2);
        }

        var eTicketDatabaseRecords = await _db.ETickets.Where(w => eTickets.Select(s => s.ETicketId).Contains(w.Id)).ToListAsync();

        foreach (var item in eTicketDatabaseRecords)
        {
            item.Status = (int)ETicketStatus.Invoiced;
        }

        await _db.SaveChangesAsync();

        return Ok(invoiceGenerateModel);
    }

    [HttpPost]
    public async Task<IActionResult> GenerateRouteStopInvoices([FromBody] InvoiceGenerateModel invoiceGenerateModel)
    {
        var stopsQuery = _db.HaulierStops.AsQueryable();

        if (invoiceGenerateModel.GenerateFor == 2)
        {
            stopsQuery = stopsQuery.Where(w => w.Route.RouteDate >= invoiceGenerateModel.StartDate && w.Route.RouteDate <= invoiceGenerateModel.EndDate);
        }

        var stops = await stopsQuery.Where(w => w.TenantId == _currentUserService.TenantId)
                                    .Where(w => w.ContractLink != null)
                                    .Where(s => s.Status == (int)HaulierStopStatus.Completed)
                                    .Where(s => s.Type != (int)HaulierStopType.TipStop)
                                    .Select(s => new
                                    {
                                        HaulierStopId = s.Id,
                                        s.ContractLink.CustomerId,
                                        s.ContractId,
                                        ContractName = s.ContractLink.Contract.Name,

                                        s.Tonnes,
                                        s.PricePerLoad,
                                        s.PricePerTonne,
                                        s.OrderNumber,

                                        s.StopNumber,
                                        s.Route.RouteDate
                                    })
                                   .ToListAsync();


        var groupedStops = stops.GroupBy(s => new { s.ContractId, s.CustomerId, s.OrderNumber }).ToArray();

        int lastInvoiceNumber = (await _db.Invoices.Where(w => w.TenantId == _currentUserService.TenantId)
                                            .OrderByDescending(s => s.InvoiceNumber)
                                            .FirstOrDefaultAsync())?.InvoiceNumber ?? 0;

        foreach (var grouped in groupedStops)
        {
            lastInvoiceNumber++;

            var invoice = new HT.Database.Invoice
            {
                Id = Guid.NewGuid(),
                TenantId = _currentUserService.TenantId,
                CustomerId = grouped.FirstOrDefault().CustomerId.Value,
                ContractId = grouped.FirstOrDefault().ContractId,

                InvoiceNumber = lastInvoiceNumber,
                InvoiceCompanyNumber = "GER-" + lastInvoiceNumber,
                CreatedDateTime = DateTime.Now,
                DateTime = invoiceGenerateModel.InvoiceDate.Value.Date,
                Status = (int)InvoiceStatus.Created,
                OrderNumbers = string.Join(", ", grouped.Select(s => s.OrderNumber).Distinct())
            };
            _db.Invoices.Add(invoice);

            int lineNumber = 1;
            foreach (var eticket in grouped)
            {
                decimal quantity = 1;
                decimal unitPrice = 0m;
                decimal unitTotal = 0m;

                if (eticket.PricePerLoad != 0)
                {
                    unitPrice = eticket.PricePerLoad.Value;
                    unitTotal = eticket.PricePerLoad.Value;
                }

                if (eticket.PricePerTonne != 0)
                {
                    unitPrice = eticket.PricePerTonne.Value;
                    unitTotal = eticket.PricePerTonne.Value * eticket.Tonnes;
                    quantity = eticket.Tonnes;
                }

                var invoiceLine = new InvoiceLine
                {
                    Id = Guid.NewGuid(),
                    TenantId = _currentUserService.TenantId,
                    InvoiceId = invoice.Id,
                    HaulierStopId = eticket.HaulierStopId,
                    Code = "4009",
                    LineNumber = lineNumber,
                    Description = eticket.StopNumber + " " + eticket.ContractName + " " + eticket.RouteDate.ToString("dd/MM/yyyy"),
                    Quantity = quantity,
                    UnitPrice = unitPrice,
                    UnitTotal = unitTotal,
                    VATRate = 20.0m,
                    VATTotal = Math.Round(unitTotal * 0.2m, 2),
                    Total = Math.Round(unitTotal * 1.2m, 2)
                };
                _db.InvoiceLines.Add(invoiceLine);
                lineNumber++;
            }

            invoice.UnitTotal = Math.Round(invoice.InvoiceLines.Sum(s => s.UnitTotal), 2);
            invoice.VATTotal = Math.Round(invoice.InvoiceLines.Sum(s => s.VATTotal), 2);
            invoice.Total = Math.Round(invoice.InvoiceLines.Sum(s => s.Total), 2);
        }

        var eTicketDatabaseRecords = await _db.HaulierStops.Where(w => stops.Select(s => s.HaulierStopId).Contains(w.Id)).ToListAsync();

        foreach (var item in eTicketDatabaseRecords)
        {
            item.Status = (int)HaulierStopStatus.Invoiced;
        }

        await _db.SaveChangesAsync();

        return Ok(invoiceGenerateModel);
    }

    [HttpPost]
    public async Task<IActionResult> GenerateJobInvoices([FromBody] InvoiceGenerateModel invoiceGenerateModel)
    {
        var jobsQuery = _db.Jobs.AsQueryable();

        if (invoiceGenerateModel.GenerateFor == 2)
        {
            jobsQuery = jobsQuery.Where(w => w.EntryDateTime >= invoiceGenerateModel.StartDate && w.EntryDateTime <= invoiceGenerateModel.EndDate);
        }

        var jobLins = await jobsQuery.Where(w => w.TenantId == _currentUserService.TenantId)
                                    .Where(s => s.Status == (int)JobStatus.Closed)
                                    .SelectMany(s => s.Products)
                                    .Select(s => new
                                    {
                                        s.JobId,
                                        s.Job.CustomerId,
                                        s.PONumber,
                                        s.ProductId,


                                        s.UnitPrice,
                                        s.VATRate,
                                        s.Total,
                                        s.UnitTotal,
                                        s.VATTotal,
                                        Tonnes = s.NetWeight / 1000,

                                        s.Product.ProductCode,
                                        ProductName = s.Product.Name,

                                        s.Job.JobNumber,
                                        s.Job.EntryDateTime

                                    })
                                   .ToListAsync();


        var groupedStops = jobLins.GroupBy(s => new { s.ProductId, s.CustomerId, s.PONumber }).ToArray();

        int lastInvoiceNumber = (await _db.Invoices.Where(w => w.TenantId == _currentUserService.TenantId)
                                            .OrderByDescending(s => s.InvoiceNumber)
                                            .FirstOrDefaultAsync())?.InvoiceNumber ?? 0;

        foreach (var grouped in groupedStops)
        {
            lastInvoiceNumber++;

            var invoice = new HT.Database.Invoice
            {
                Id = Guid.NewGuid(),
                TenantId = _currentUserService.TenantId,
                CustomerId = grouped.FirstOrDefault().CustomerId.Value,
                InvoiceNumber = lastInvoiceNumber,
                InvoiceCompanyNumber = "JOB-" + lastInvoiceNumber,
                CreatedDateTime = DateTime.Now,
                DateTime = invoiceGenerateModel.InvoiceDate.Value.Date,
                Status = (int)InvoiceStatus.Created,
                OrderNumbers = string.Join(", ", grouped.Select(s => s.PONumber).Distinct()),
            };
            _db.Invoices.Add(invoice);

            int lineNumber = 1;
            foreach (var eticket in grouped)
            {
                var invoiceLine = new InvoiceLine
                {
                    Id = Guid.NewGuid(),
                    TenantId = _currentUserService.TenantId,
                    InvoiceId = invoice.Id,
                    JobId = eticket.JobId,
                    Code = eticket.ProductCode,
                    LineNumber = lineNumber,
                    Description = "JOB-" + eticket.JobNumber + " " + eticket.ProductName + " " + eticket.EntryDateTime.Value.ToString("dd/MM/yyyy"),
                    Quantity = eticket.Tonnes,
                    UnitPrice = eticket.UnitPrice,
                    UnitTotal = eticket.UnitTotal,
                    VATRate = eticket.VATRate,
                    VATTotal = eticket.VATTotal,
                    Total = eticket.Total
                };
                _db.InvoiceLines.Add(invoiceLine);
                lineNumber++;
            }

            invoice.UnitTotal = Math.Round(invoice.InvoiceLines.Sum(s => s.UnitTotal), 2);
            invoice.VATTotal = Math.Round(invoice.InvoiceLines.Sum(s => s.VATTotal), 2);
            invoice.Total = Math.Round(invoice.InvoiceLines.Sum(s => s.Total), 2);
        }

        var jobRecords = await _db.Jobs.Where(w => jobLins.Select(s => s.JobId).Contains(w.Id)).ToListAsync();

        foreach (var item in jobRecords)
        {
            item.Status = (int)JobStatus.Invoiced;
        }

        await _db.SaveChangesAsync();

        return Ok(invoiceGenerateModel);
    }


    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> GetInvoice(Guid id)
    {
        var invoiceModel = await _db.Invoices
                               .Where(i => i.Id == id)
                               .Select(s => new InvoiceModel
                               {
                                   Id = s.Id,
                                   Status = s.Status,

                                   DateTime = s.DateTime,
                                   DueDate = s.DateTime.AddDays(s.Customer.PaymentTerms),

                                   ContractName = s.Contract.Name,
                                   OrderNumbers = s.OrderNumbers,
                                   CustomerName = s.Customer.Name,
                                   CustomerAddressId = s.Customer.CustomerAddressId,

                                   ContractAddressLine1 = s.Contract.AddressLine1,
                                   ContractAddressLine2 = s.Contract.AddressLine2,
                                   ContractAddressLine3 = s.Contract.AddressLine3,
                                   ContractAddressLine4 = s.Contract.AddressLine4,
                                   ContractAddressPostcode = s.Contract.AddressPostcode,

                                   InvoiceNumber = s.InvoiceNumber,
                                   InvoiceCompanyNumber = s.InvoiceCompanyNumber,
                                   Lines = s.InvoiceLines.Select(ss => new InvoiceLineModel
                                   {
                                       LineNumber = ss.LineNumber,
                                       Code = ss.Code,
                                       Description = ss.Description,
                                       Quantity = ss.Quantity,
                                       Total = ss.Total,
                                       UnitPrice = ss.UnitPrice,
                                       UnitTotal = ss.UnitTotal,
                                       VATRate = ss.VATRate,
                                       VATTotal = ss.VATTotal,

                                       ETickectId = ss.ETicketId,
                                       HaulierStopId = ss.HaulierStopId,
                                       HaulierStopRouteId = ss.HaulierStop.RouteId,
                                       JobId = ss.JobId
                                   }).OrderBy(o => o.LineNumber).ToList(),

                                   VATTotal = s.VATTotal,
                                   UnitTotal = s.UnitTotal,
                                   Total = s.Total,

                                   CompanyCode = s.Contract.SitePermit.Site.Company.Code,
                                   CompanyName = s.Contract.SitePermit.Site.Company.Name,
                                   CompanyAddressLine1 = s.Contract.SitePermit.Site.Company.AddressLine1,
                                   CompanyAddressLine2 = s.Contract.SitePermit.Site.Company.AddressLine2,
                                   CompanyAddressLine3 = s.Contract.SitePermit.Site.Company.AddressLine3,
                                   CompanyAddressLine4 = s.Contract.SitePermit.Site.Company.AddressLine4,
                                   CompanyAddressPostcode = s.Contract.SitePermit.Site.Company.AddressPostcode,
                                   CompanyPhone = s.Contract.SitePermit.Site.Company.Phone,
                                   CompanyEmail = s.Contract.SitePermit.Site.Company.Email,
                                   CompanyWebsite = s.Contract.SitePermit.Site.Company.Website,
                                   CompanyVATNumber = s.Contract.SitePermit.Site.Company.VATNumber,
                                   CompanyBankName = s.Contract.SitePermit.Site.Company.BankName,
                                   CompanyBankSort = s.Contract.SitePermit.Site.Company.BankSort,
                                   CompanyBankAccountNumber = s.Contract.SitePermit.Site.Company.BankAccountNumber,
                                   CompanyNumber = s.Contract.SitePermit.Site.Company.CompanyNumber,
                                   RegisterdOfficeAddressLine1 = s.Contract.SitePermit.Site.Company.RegisterdOfficeAddressLine1,
                                   RegisterdOfficeAddressLine2 = s.Contract.SitePermit.Site.Company.RegisterdOfficeAddressLine2,
                                   RegisterdOfficeAddressLine3 = s.Contract.SitePermit.Site.Company.RegisterdOfficeAddressLine3,
                                   RegisterdOfficeAddressLine4 = s.Contract.SitePermit.Site.Company.RegisterdOfficeAddressLine4,
                                   RegisterdOfficeAddressPostcode = s.Contract.SitePermit.Site.Company.RegisterdOfficeAddressPostcode,

                                   PaymentTerms = s.Customer.PaymentTerms,
                               })
                               .FirstOrDefaultAsync();

        if (invoiceModel.Lines.Any(a => a.JobId != null))
        {
            var company = await _db.Companies.FirstOrDefaultAsync(c => c.Id == new Guid("cc5a1d73-02ab-4df9-b1ff-18ddb196a47c"));
            if (company != null)
            {
                invoiceModel.CompanyCode = company.Code;
                invoiceModel.CompanyName = company.Name;
                invoiceModel.CompanyAddressLine1 = company.AddressLine1;
                invoiceModel.CompanyAddressLine2 = company.AddressLine2;
                invoiceModel.CompanyAddressLine3 = company.AddressLine3;
                invoiceModel.CompanyAddressLine4 = company.AddressLine4;
                invoiceModel.CompanyAddressPostcode = company.AddressPostcode;
                invoiceModel.CompanyPhone = company.Phone;
                invoiceModel.CompanyEmail = company.Email;
                invoiceModel.CompanyWebsite = company.Website;
                invoiceModel.CompanyVATNumber = company.VATNumber;
                invoiceModel.CompanyBankName = company.BankName;
                invoiceModel.CompanyBankSort = company.BankSort;
                invoiceModel.CompanyBankAccountNumber = company.BankAccountNumber;
                invoiceModel.CompanyNumber = company.CompanyNumber;
                invoiceModel.RegisterdOfficeAddressLine1 = company.RegisterdOfficeAddressLine1;
                invoiceModel.RegisterdOfficeAddressLine2 = company.RegisterdOfficeAddressLine2;
                invoiceModel.RegisterdOfficeAddressLine3 = company.RegisterdOfficeAddressLine3;
                invoiceModel.RegisterdOfficeAddressLine4 = company.RegisterdOfficeAddressLine4;
                invoiceModel.RegisterdOfficeAddressPostcode = company.RegisterdOfficeAddressPostcode;
            }
        }

        invoiceModel.CustomerAddress = await _addressService.GetAddressAsync(invoiceModel.CustomerAddressId);

        return Ok(invoiceModel);
    }

    [AllowAnonymous]
    public async Task<ActionResult> PrintPdf(Guid id)
    {
        /// string url = $"https://localhost:7068/invoice/print/{id}";
        string url = $"https://app-weigh-technology.azurewebsites.net/invoice/print/{id}";

        byte[] pdfBytes = await _urlToPDF.BuildPdf(url);

        var invoice = await _db.Invoices.Where(s => s.Id == id).Select(s => new
        {
            CustomerCode = s.Customer.Code,
            s.InvoiceCompanyNumber
        }).FirstOrDefaultAsync();

        return File(pdfBytes, "application/pdf", $"{invoice.CustomerCode}-Invoice-{invoice.InvoiceCompanyNumber}.pdf");
    }


    [AllowAnonymous]
    public async Task<ActionResult> PrintETicketsPdf(Guid id)
    {
        /// string url = $"https://localhost:7068/invoice/printetickets/{id}";
        string url = $"https://app-weigh-technology.azurewebsites.net/invoice/printetickets/{id}";

        byte[] pdfBytes = await _urlToPDF.BuildPdf(url);

        var invoice = await _db.Invoices.Where(s => s.Id == id).Select(s => new
        {
            CustomerCode = s.Customer.Code,
            s.InvoiceCompanyNumber
        }).FirstOrDefaultAsync();

        return File(pdfBytes, "application/pdf", $"{invoice.CustomerCode}-ETickets-{invoice.InvoiceCompanyNumber}.pdf");
    }

    [AllowAnonymous]
    public async Task<ActionResult> PrintPODsPdf(Guid id)
    {
        //string url = $"https://localhost:7068/invoice/printpods/{id}";
        string url = $"https://app-weigh-technology.azurewebsites.net/invoice/printpods/{id}";

        byte[] pdfBytes = await _urlToPDF.BuildPdf(url);

        var invoice = await _db.Invoices.Where(s => s.Id == id).Select(s => new
        {
            CustomerCode = s.Customer.Code,
            s.InvoiceCompanyNumber
        }).FirstOrDefaultAsync();

        return File(pdfBytes, "application/pdf", $"{invoice.CustomerCode}-POD-{invoice.InvoiceCompanyNumber}.pdf");
    }

    [AllowAnonymous]
    public async Task<ActionResult> PrintJobsPdf(Guid id)
    {
        //string url = $"https://localhost:7068/invoice/printjobs/{id}";
        string url = $"https://app-weigh-technology.azurewebsites.net/invoice/printjobs/{id}";

        byte[] pdfBytes = await _urlToPDF.BuildPdf(url);

        var invoice = await _db.Invoices.Where(s => s.Id == id).Select(s => new
        {
            CustomerCode = s.Customer.Code,
            s.InvoiceCompanyNumber
        }).FirstOrDefaultAsync();

        return File(pdfBytes, "application/pdf", $"{invoice.CustomerCode}-Jobs-{invoice.InvoiceCompanyNumber}.pdf");
    }

    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> GetPODs(Guid id)
    {
        var podIds = await _db.InvoiceLines.Where(i => i.InvoiceId == id)
                                             .Where(w => w.HaulierStopId != null && w.HaulierStop.ProofOfDeliveryId != null)
                                             .Select(s => s.HaulierStop.ProofOfDeliveryId)
                                             .ToListAsync();

        var pods = new List<ProofOfDeliveryModel>();

        foreach (var podId in podIds)
        {
            var pod = await _proofOfDeliveryService.GetProofOfDeliveryAsync(podId.Value);
            pods.Add(pod);
        }

        return Ok(pods);
    }

    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> GetJobs(Guid id)
    {
        var jobIds = await _db.InvoiceLines.Where(i => i.InvoiceId == id)
                                             .Where(w => w.JobId != null)
                                             .Select(s => s.JobId)
                                             .ToListAsync();

        var pods = new List<JobPrintModel>();

        foreach (var podId in jobIds)
        {
            var pod = await _jobService.GetPrintJobAsync(podId.Value);
            pods.Add(pod);
        }

        return Ok(pods);
    }


    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> GetInvoiceETickets(Guid id)
    {
        var eTickets = await _db.InvoiceLines.Where(i => i.InvoiceId == id)
                                        .Select(s => s.ETicket)
                                        .Select(s => new ETicketPrintModel
                                        {
                                            TicketNumber = s.TicketNumber,
                                            SiteETicketNumber = s.SiteETicketNumber,

                                            Registration = s.HaulierVehicle.Registration,
                                            CarrierLicence = s.HaulierVehicle.Haulier.CarrierLicence,
                                            VehicleType = s.HaulierVehicle.HaulierVehicleType.Name,

                                            Customer = s.Customer.Name,
                                            SiteName = s.Site.Name,

                                            HaulierName = s.HaulierVehicle.Haulier.Name,
                                            PermitLicenceNumber = s.Contract.SitePermit.LicenceNumber,
                                            PermitName = s.Contract.SitePermit.Name,
                                            ContractNumber = s.Contract.ContractNumber,
                                            OrderNumber = s.OrderNumber,

                                            ContractName = s.Contract.Name,
                                            ContractBorough = s.Contract.Borough,
                                            ProductName = s.Contract.SitePermitProductLink.Product.Name + " - " + s.Contract.SitePermitProductLink.Product.Name,

                                            EntryWeight = s.EntryWeight,
                                            EntryDateTime = s.EntryDateTime,
                                            ExitWeight = s.ExitWeight,
                                            ExitDateTime = s.ExitDateTime,

                                            NetWeight = s.NetWeight,


                                            DriverName = s.DriverName,
                                            TransferNote = s.TransferNote,
                                            Remarks = s.Remarks,
                                            HazardousRemarks = s.HazardousRemarks,

                                            ContractSiteAddress = s.Contract.AddressLine1 + " " + s.Contract.AddressLine2 + " " + s.Contract.AddressLine3 + " " + s.Contract.AddressLine4 + " " + s.Contract.AddressPostcode
                                        })
                                        .OrderBy(o => o.TicketNumber)
                                        .ToListAsync();

        return Ok(eTickets);
    }

    [AllowAnonymous]
    public async Task<ActionResult> SendInvoice(Guid id)
    {
        var invoice = await _db.Invoices.FirstOrDefaultAsync(x => x.Id == id);
        invoice.Status = (int)InvoiceStatus.Sent;
        await _db.SaveChangesAsync();

        await _emailInvoiceService.SendInvoiceEmail(id);

        return Ok(new FormResponseModel<string>());
    }

    public async Task<ActionResult> MarkAsSent(Guid id)
    {
        var invoice = await _db.Invoices.FirstOrDefaultAsync(x => x.Id == id);
        invoice.Status = (int)InvoiceStatus.Sent;
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<string>());
    }

    [HttpPost]
    public async Task<IActionResult> DeleteGeneratedInvoices()
    {
        var invoices = await _db.Invoices
                                .Include(i => i.InvoiceLines).ThenInclude(i => i.ETicket)
                                .Include(i => i.InvoiceLines).ThenInclude(i => i.HaulierStop)
                                .Include(i => i.InvoiceLines).ThenInclude(i => i.Job)
                                .Where(s => s.TenantId == _currentUserService.TenantId &&
                                            s.Status == (int)InvoiceStatus.Created)
                                .ToListAsync();

        foreach (var eticket in invoices.SelectMany(s => s.InvoiceLines).Where(w => w.ETicket != null))
        {
            eticket.ETicket.Status = (int)ETicketStatus.Closed;
        }

        foreach (var eticket in invoices.SelectMany(s => s.InvoiceLines).Where(w => w.HaulierStop != null))
        {
            eticket.HaulierStop.Status = (int)HaulierStopStatus.Completed;
        }

        foreach (var eticket in invoices.SelectMany(s => s.InvoiceLines).Where(w => w.JobId != null))
        {
            eticket.Job.Status = (int)JobStatus.Closed;
        }

        _db.InvoiceLines.RemoveRange(invoices.SelectMany(s => s.InvoiceLines));
        _db.Invoices.RemoveRange(invoices);

        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<string>());
    }


}
