using System.Security.Claims;
using HT.Api.Services;
using HT.Api.Services.Common.ActivityLog;
using HT.Api.Services.Common.Email;
using HT.Database;
using HT.Shared.Common;
using HT.Shared.Enums;
using HT.Shared.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.System;

[ApiController]
[Route("[controller]/[action]")]
public class AccountController(
    DatabaseContext db,
    CurrentUserService currentUserService,
    ActivityLogService activityLogService,
    ForgotPasswordService forgotPasswordService,
    IConfiguration configuration) : ControllerBase
{
    [HttpGet]
    public async Task<IActionResult> ServerRunning()
    {
        return Ok(new { Running = true });
    }

    [HttpPost]
    public async Task<IActionResult> ForgotPassword(string email)
    {
        var result = new LoginResultModel
        {
            Success = true,
        };

        var user = await db.Users
                    .Where(s => s.Email.ToLower() == email.ToLower())
                    .Where(s => s.Status == (int)GenericStatus.Active)
                    .FirstOrDefaultAsync();

        if (user == null)
        {
            result.Success = false;

            return Ok(result);
        }

        user.PasswordResetToken = Guid.NewGuid().ToString();
        user.PasswordResetExpiry = DateTime.Now.AddDays(3);
        await db.SaveChangesAsync();

        await forgotPasswordService.SendForgotPasswordEmail(user.Id);

        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> Login([FromBody] LoginPostModel loginPostModel)
    {
        if (string.IsNullOrEmpty(loginPostModel.Email) || string.IsNullOrEmpty(loginPostModel.Password))
        {
            var failedLogin = new LoginResultModel
            {
                Success = false,
                ApiToken = "Login failed",
            };

            return Ok(failedLogin);
        }

        var user = await db.Users.FirstOrDefaultAsync(s => s.Email.ToLower() == loginPostModel.Email.ToLower() &&
                                         s.Password.ToLower() == loginPostModel.Password.ToLower());

        if (user == null)
        {
            var failedLogin = new LoginResultModel
            {
                Success = false,
                ApiToken = "Login failed",
            };

            return Ok(failedLogin);
        }

        var loginResultModel = new LoginResultModel
        {
            Success = true,
            UserEmail = user.Email,
            UserId = user.Id,
        };

        var claims = new[]
{
            new Claim("TenantId", (user.TenantId??Guid.Empty).ToString()),
            new Claim("UserId", user.Id.ToString()),
        };

        loginResultModel.ApiToken = TokenUtility.CreateToken(secret: configuration["Security:Code"],
                                                             claims: claims,
                                                             expiryDate: new DateTime(2030, 01, 01));

        loginResultModel.UserType = user.UserType;

        await activityLogService.Log(tenantId: user.TenantId,
                                      userId: user.Id,
                                      activityLogType: Shared.Enums.ActivityLogType.LoggedIn,
                                      message: null,
                                      primaryObjectId: null);

        return Ok(loginResultModel);
    }

    [HttpPost]
    public async Task<bool> ChangePasswordCheck(string token)
    {
        bool isBadToken = !await db.Users.AnyAsync(t => t.PasswordResetToken == token);

        return isBadToken;
    }

    [HttpPost]
    public async Task<bool> ChangePassword([FromBody] ChangePasswordModel model)
    {
        var user = await db.Users.FirstOrDefaultAsync(u => u.PasswordResetToken == model.Token);

        if (user == null)
        {
            return false;
        }

        if (user.PasswordResetExpiry < DateTime.Now)
        {
            return false;
        }

        user.Password = model.Password;
        user.PasswordResetToken = null;
        user.PasswordResetExpiry = null;

        await db.SaveChangesAsync();

        return true;
    }

    [HttpPost]
    public async Task<IActionResult> SwitchTenant(Guid id)
    {
        var userId = currentUserService.UserId;

        var loginResultModel = new LoginResultModel
        {
            Success = true,
            UserEmail = "<EMAIL>",
            UserId = userId,
        };

        var claims = new[]
        {
            new Claim("TenantId", id.ToString()),
            new Claim("UserId", userId.ToString()),
        };


        loginResultModel.ApiToken = TokenUtility.CreateToken(secret: configuration["Security:Code"],
                                                             claims: claims,
                                                             expiryDate: new DateTime(2030, 01, 01));
        loginResultModel.UserType = 1;

        await activityLogService.Log(tenantId: id,
                                      userId: loginResultModel.UserId,
                                      activityLogType: Shared.Enums.ActivityLogType.SwitchedTenant,
                                      message: null,
                                      primaryObjectId: null);

        return Ok(loginResultModel);
    }
}
