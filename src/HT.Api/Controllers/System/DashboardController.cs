using HT.Api.Services.Dashboard;
using HT.Database;
using HT.Shared.Models.Dashboard;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class DashboardController : ControllerBase
{
    private readonly GetDashboardService _getDashboardService;
    private readonly GetDashboardSummaryService _getDashboardSummaryService;
    private readonly GetOrdersDashboardSummaryService _getOrdersDashboardSummaryService;
    private readonly GetSiteDashboardService _getSiteDashboardService;

    public DashboardController(GetDashboardService getDashboardService,
        GetDashboardSummaryService getDashboardSummaryService,
        GetOrdersDashboardSummaryService getOrdersDashboardSummaryService,
        GetSiteDashboardService getSiteDashboardService)
    {
        _getDashboardService = getDashboardService;
        _getDashboardSummaryService = getDashboardSummaryService;
        _getOrdersDashboardSummaryService = getOrdersDashboardSummaryService;
        _getSiteDashboardService = getSiteDashboardService;
    }

    [HttpPost]
    public async Task<IActionResult> GetDashboard()
    {
        var result = await _getDashboardService.GetDashboardAsync();
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> GetDashboardSummary(DashboardFiltersModel dashboardFiltersModel)
    {
        var result = await _getDashboardSummaryService.GetDashboardSummaryAsync(dashboardFiltersModel);
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> GetOrdersDashboardSummary(DashboardFiltersModel dashboardFiltersModel)
    {
        var result = await _getOrdersDashboardSummaryService.GetOrdersDashboardSummaryAsync(dashboardFiltersModel);
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> GetSiteDashboard(DashboardFiltersModel dashboardFiltersModel)
    {
        var result = await _getSiteDashboardService.GetSiteDashboardAsync(dashboardFiltersModel);
        return Ok(result);
    }
}
