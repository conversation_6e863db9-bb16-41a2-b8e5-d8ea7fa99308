using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers.System;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class TenantController : ControllerBase
{
    private readonly DatabaseContext _db;

    public TenantController(DatabaseContext db)
    {
        _db = db;
    }

    [HttpPost]
    public async Task<IActionResult> CreateTenant([FromBody] CreateTenantModel TenantCreateModel)
    {
        _db.Tenants.Add(new Tenant
        {
            Id = TenantCreateModel.Id,
            Name = TenantCreateModel.Name,
            LogoUrl = TenantCreateModel.LogoUrl,
            SecondaryColour = TenantCreateModel.SecondaryColour,
            PrimaryColour = TenantCreateModel.PrimaryColour,
        });
        await _db.SaveChangesAsync();

        return Ok(TenantCreateModel);
    }

    [HttpPost]
    public async Task<IActionResult> ListTenants(bool sbm, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.Tenants.Where(s => s.WebsiteModule == sbm).Select(s => new TenantListItemModel
        {
            Id = s.Id,
            Production = s.Production,
            Name = s.Name,
            LogoUrl = s.LogoUrl,
            SecondaryColour = s.SecondaryColour,
            PrimaryColour = s.PrimaryColour,
            FleetModule = s.FleetModule,
            OrderModule = s.OrderModule,
            RouteModule = s.RouteModule,
            WeighbridgeModule = s.WeighbridgeModule,
            WorkforceModule = s.WorkforceModule
        }).AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }
}
