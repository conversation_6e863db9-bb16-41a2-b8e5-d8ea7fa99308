using HT.Api.Services;
using HT.Blazor.Models.Grid;
using HT.Shared.Models.Dashboard;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;


namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class ReportController : ControllerBase
{
    private readonly ReportService _reportService;

    public ReportController(ReportService reportService)
    {
        _reportService = reportService;
    }

    [HttpPost]
    public async Task<IActionResult> GetPurchasesReport(ReportFiltersModel reportFiltersModel)
    {
        return Ok(await _reportService.GetPurchasesReportAsync(reportFiltersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListETickets([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _reportService.ListETicketsAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListInvoices([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _reportService.ListInvoicesAsync(gridParametersModel));
    }


    [HttpPost]
    public async Task<IActionResult> ListProductsSold([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _reportService.ListProductsSoldAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListProductsSoldIndividually([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _reportService.ListProductsSoldIndividuallyAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListProductsSoldGrouped([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _reportService.ListProductsSoldGroupedAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetProductsSoldIndividuallyTotals([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _reportService.GetProductsSoldIndividuallyTotalsAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListTotalTonnage([FromBody] ReportFiltersModel reportFiltersModel)
    {
        return Ok(await _reportService.ListTotalTonnageAsync(reportFiltersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListHaulierStops([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _reportService.ListHaulierStopsAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListJobs([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _reportService.ListJobsAsync(gridParametersModel));
    }
}
