using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Export;
using HT.Shared.Models.Invoice;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text;
using HT.Api.Services.Common.AzureStorage;

namespace HT.Api.Controllers.System;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class ExportController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly AzureStorageService _azureStorageService;

    public ExportController(DatabaseContext db, CurrentUserService currentUserService, AzureStorageService azureStorageService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _azureStorageService = azureStorageService;
    }

    [HttpPost]
    public async Task<IActionResult> ListExports([FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.Exports
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Select(s => new ExportListItemModel
                   {
                       Id = s.Id,
                       Type = (ExportType)s.Type,
                       CreatedDate = s.CreatedDate,
                       AzureStorageURL = s.AzureStorageURL,
                       Lines = s.LineCount
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> CreateExport()
    {
        var invoices = await _db.Invoices.Where(w => w.TenantId == _currentUserService.TenantId)
                                         .Where(w => !w.Exported)
                                         .Where(w => w.Status == (int)InvoiceStatus.Sent)
                                         .Select(i => new
                                         {
                                             i.Id,
                                             Type = "SI",
                                             AccountReference = i.Customer.Code,
                                             NominalAccountReference = i.InvoiceLines.Any(a => a.ETicket.Type == (int)ETicketType.Waste) ? i.Contract.SitePermitProductLink.Product.ProductCode : i.InvoiceLines.FirstOrDefault().ETicket.Product.ProductCode,
                                             DepartmentCode = 1,
                                             Date = i.DateTime.ToShortDateString(),
                                             Reference = i.InvoiceCompanyNumber,
                                             Details = "",
                                             NetAmount = i.UnitTotal,
                                             TaxCode = "T1",
                                             TaxAmount = i.VATTotal,

                                             IsJobsInvoice = i.InvoiceLines.Any(a => a.JobId != null),
                                             JobsCode = i.InvoiceLines.Any(a => a.JobId != null) ? (i.InvoiceLines.FirstOrDefault().JobId != null ? i.InvoiceLines.FirstOrDefault().Job.Products.FirstOrDefault().Product.ProductCode : "") : "",

                                             IsRouteInvoice = i.InvoiceLines.Any(a => a.HaulierStop != null),
                                             RouteCode = i.InvoiceLines.Any(a => a.HaulierStop != null) ? (i.InvoiceLines.FirstOrDefault().HaulierStop != null ? i.InvoiceLines.FirstOrDefault().HaulierStop.Contract.SitePermitProductLink.Product.ProductCode : "") : "",

                                         }).OrderBy(i => i.Reference)
                                    .ToListAsync();

        var csv = new StringBuilder();
        csv.AppendLine("Type,AccountReference,NominalAccountReference,DepartmentCode,Date,Reference,Details,NetAmount,TaxCode,TaxAmount");

        foreach (var invoice in invoices)
        {
            string line = string.Format("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9}",
                invoice.Type,
                invoice.AccountReference,
                invoice.IsJobsInvoice ? invoice.JobsCode : (invoice.IsRouteInvoice ? invoice.RouteCode : invoice.NominalAccountReference),
                invoice.DepartmentCode,
                invoice.Date,
                invoice.Reference,
                invoice.Details,
                invoice.NetAmount,
                invoice.TaxCode,
                invoice.TaxAmount
            );
            csv.AppendLine(line);
        }

        string sageExport = csv.ToString();
        string fileName = string.Format("export_{0}.csv", DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss"));

        var export = new Export
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            CreatedDate = DateTime.Now,
            Type = (int)ExportType.Sage,
            LineCount = invoices.Count,
        };

        export.AzureStoragePath = export.Id + "/" + fileName;

        byte[] bytes = Encoding.UTF8.GetBytes(sageExport);
        var stream = new MemoryStream(bytes);
        stream.Seek(0, SeekOrigin.Begin);

        export.AzureStorageURL = await _azureStorageService.UploadFileToBlobAsync(
                                                               "main",
                                                               export.AzureStoragePath,
                                                               stream,
                                                               "text/csv");

        await stream.DisposeAsync();
        _db.Exports.Add(export);

        var invoiceIds = invoices.Select(i => i.Id).ToList();

        var invoicesToUpdate = await _db.Invoices.Where(w => invoiceIds.Contains(w.Id)).ToListAsync();

        foreach (var invoice in invoicesToUpdate)
        {
            invoice.Exported = true;
        }

        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<string>());
    }

}
