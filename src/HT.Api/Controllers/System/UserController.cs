﻿using HT.Api.Services;
using HT.Api.Services.Common.Email;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using HT.Shared.Models.User;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.System;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class UserController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly NewUserEmailService _newUserEmailService;

    public UserController(
        DatabaseContext db,
        CurrentUserService currentUserService,
        NewUserEmailService newUserEmailService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _newUserEmailService = newUserEmailService;
    }


    [HttpPost]
    public async Task<IActionResult> CreateUser([FromBody] UserCreateModel userCreateModel)
    {
        var formResponseModel = new FormResponseModel<UserCreateModel>(userCreateModel);

        bool userExists = await _db.Users.AnyAsync(a => a.Email == userCreateModel.Email);

        if (userExists)
        {
            formResponseModel.AddError("Email already in use", new FormFieldIdentifierModel("Email"));
            return Ok(formResponseModel);
        }

        userCreateModel.Id = Guid.NewGuid();

        var user = new User
        {
            Id = userCreateModel.Id,
            TenantId = _currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            FirstName = userCreateModel.FirstName,
            Surname = userCreateModel.Surname,
            Email = userCreateModel.Email,
            UserType = userCreateModel.UserType,
            Password = userCreateModel.Password,
        };
        _db.Users.Add(user);

        foreach (var site in userCreateModel.UserSiteAccessItems)
        {
            if (site.Selected)
            {
                var userSiteLink = new UserSiteLink
                {
                    Id = Guid.NewGuid(),
                    TenantId = _currentUserService.TenantId,
                    SiteId = site.SiteId,
                    UserId = user.Id
                };
                _db.UserSiteLinks.Add(userSiteLink);
            }
        }

        if (userCreateModel.HaulierVehicleId != null)
        {
            var vehicle = await _db.HaulierVehicles.FirstAsync(v => v.Id == userCreateModel.HaulierVehicleId);
            vehicle.DriverUserId = user.Id;
        }

        await _db.SaveChangesAsync();

        await _newUserEmailService.SendNewUserEmailAsync(user.Id, userCreateModel.Password);

        return Ok(formResponseModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateUser([FromBody] UserModel userModel)
    {
        var formResponseModel = new FormResponseModel<UserModel>(userModel);

        var user = await _db.Users
            .Include(i => i.UserSiteLinks)
            .Include(i => i.HaulierVehicle)
            .Where(w => w.TenantId == _currentUserService.TenantId)
            .Where(w => w.Id == userModel.Id)
            .FirstOrDefaultAsync();

        bool emailChanged = user.Email != userModel.Email;

        user.Status = (int)GenericStatus.Active;
        user.FirstName = userModel.FirstName;
        user.Surname = userModel.Surname;
        user.Email = userModel.Email;
        user.UserType = userModel.UserType;
        user.Password = userModel.Password;

        _db.UserSiteLinks.RemoveRange(user.UserSiteLinks);

        foreach (var site in userModel.UserSiteAccessItems)
        {
            if (site.Selected)
            {
                var userSiteLink = new UserSiteLink
                {
                    Id = Guid.NewGuid(),
                    TenantId = _currentUserService.TenantId,
                    SiteId = site.SiteId,
                    UserId = user.Id
                };
                _db.UserSiteLinks.Add(userSiteLink);
            }

        }

        if (userModel.UserType == (int)UserType.Driver && user.HaulierVehicle.Id != userModel.HaulierVehicleId)
        {
            user.HaulierVehicle.DriverUserId = null;

            if (userModel.HaulierVehicleId != null)
            {
                var vehicle = await _db.HaulierVehicles.FirstAsync(v => v.Id == userModel.HaulierVehicleId);
                vehicle.DriverUserId = user.Id;
            }
        }

        await _db.SaveChangesAsync();

        if (emailChanged)
        {
            await _newUserEmailService.SendNewUserEmailAsync(user.Id, userModel.Password);
        }

        return Ok(formResponseModel);
    }

    [HttpPost]
    public async Task<IActionResult> GetUser(Guid id)
    {
        var userModel = await _db.Users
                               .Where(i => i.Id == id)
                               .Select(s => new UserModel
                               {
                                   Id = s.Id,
                                   Status = s.Status,
                                   Email = s.Email,
                                   UserType = s.UserType,
                                   FirstName = s.FirstName,
                                   Surname = s.Surname,
                                   Password = s.Password,
                                   HaulierId = s.HaulierVehicle != null ? s.HaulierVehicle.HaulierId : null,
                                   HaulierVehicleId = s.HaulierVehicle != null ? s.HaulierVehicle.Id : null,

                                   SiteIds = s.UserSiteLinks.Select(l => l.SiteId).ToList()
                               })
                               .FirstOrDefaultAsync();

        userModel.UserSiteAccessItems = await GetSiteAccessList();

        foreach (var item in userModel.UserSiteAccessItems)
        {
            item.Selected = userModel.SiteIds.Contains(item.SiteId);
        }

        return Ok(userModel);
    }

    [HttpPost]
    public async Task<IActionResult> ListUsers([FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.Users
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Select(s => new UserListItemModel
                   {
                       Id = s.Id,
                       Status = s.Status,
                       Email = s.Email,
                       Role = ((UserType)s.UserType).ToString(),
                       SiteAccess = s.UserSiteLinks.Count
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetUserSiteAccessItems()
    {
        var q = await GetSiteAccessList();

        return Ok(q);
    }

    private async Task<List<UserSiteAccessItem>> GetSiteAccessList()
    {
        return await _db.Sites
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Select(s => new UserSiteAccessItem
                   {
                       SiteId = s.Id,
                       SiteName = s.Name,
                       Selected = false
                   })
                   .AsQueryable()
                   .ToListAsync();
    }
}
