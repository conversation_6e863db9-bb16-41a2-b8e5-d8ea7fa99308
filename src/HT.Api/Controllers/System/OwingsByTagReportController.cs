using HT.Api.Services;
using HT.Blazor.Models.Grid;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;


namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class OwingsByTagReportController : ControllerBase
{
    private readonly OwingsByTagReportService _reportService;

    public OwingsByTagReportController(OwingsByTagReportService reportService)
    {
        _reportService = reportService;
    }

    [HttpPost]
    public async Task<IActionResult> ListOwings([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _reportService.ListOwingsAsync(gridParametersModel));
    }
}
