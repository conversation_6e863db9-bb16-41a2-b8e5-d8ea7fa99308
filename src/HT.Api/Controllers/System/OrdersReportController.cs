using HT.Api.Services;
using HT.Blazor.Models.Grid;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;


namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class OrdersReportController : ControllerBase
{
    private readonly OrdersReportService _reportService;

    public OrdersReportController(OrdersReportService reportService)
    {
        _reportService = reportService;
    }

    [HttpPost]
    public async Task<IActionResult> ListOrders([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _reportService.ListOrdersAsync(gridParametersModel));
    }
}
