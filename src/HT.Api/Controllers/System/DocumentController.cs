using HT.Api.Services;
using HT.Api.Services.Common.AzureStorage;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.Document;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.System;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class DocumentController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly AzureStorageService _azureStorageService;

    public DocumentController(DatabaseContext db, CurrentUserService currentUserService, AzureStorageService azureStorageService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _azureStorageService = azureStorageService;
    }

    [HttpPost]
    public async Task<IActionResult> UploadDocumentFile(DocumentUploadPage documentUploadPage, Guid itemId, Guid userId)
    {
        var file = HttpContext.Request.Form.Files[0];

        var document = new Document
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            FileName = file.FileName,
            Name = file.FileName,
            UploadedDate = DateTime.Now,
            UploadedUserId = userId,
        };

        document.AzureStoragePath = document.Id + "/" + file.FileName;
        document.AzureStorageURL = await _azureStorageService.UploadFileToBlobAsync(
                                                               "main",
                                                               document.AzureStoragePath,
                                                               file.OpenReadStream(),
                                                               "application/octet-stream");

        if (documentUploadPage == DocumentUploadPage.Customer)
        {
            document.CustomerId = itemId;
        }

        if (documentUploadPage == DocumentUploadPage.Contract)
        {
            document.ContractId = itemId;
        }

        if (documentUploadPage == DocumentUploadPage.Haulier)
        {
            document.HaulierId = itemId;
        }

        if (documentUploadPage == DocumentUploadPage.Purchase)
        {
            document.PurchaseId = itemId;
        }

        if (documentUploadPage == DocumentUploadPage.ETicket)
        {
            document.ETicketId = itemId;
        }

        if (documentUploadPage == DocumentUploadPage.Job)
        {
            document.JobId = itemId;
        }

        _db.Documents.Add(document);
        await _db.SaveChangesAsync();

        return Ok(new DocumentUploadModel
        {
            Id = document.Id
        });
    }

    [HttpPost]
    public async Task<IActionResult> GetDocument(Guid id)
    {
        var DocumentModel = await _db.Documents
                               .Where(i => i.Id == id)
                               .Select(s => new DocumentModel
                               {
                                   Id = s.Id,
                                   AzureStorageURL = s.AzureStorageURL,
                                   Name = s.Name,
                                   UploadedDate = s.UploadedDate,
                                   UserEmail = s.UploadedUser.Email,
                               })
                               .FirstOrDefaultAsync();

        return Ok(DocumentModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateDocument(DocumentModel DocumentModel)
    {
        var Document = await _db.Documents.FirstOrDefaultAsync(s => s.Id == DocumentModel.Id);
        Document.Name = DocumentModel.Name;
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<DocumentModel>(DocumentModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListDocuments([FromBody] GridParametersModel gridParametersModel, DocumentUploadPage documentUploadPage, Guid itemId)
    {
        var q = _db.Documents
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Select(s => new DocumentListItemModel
                   {
                       Id = s.Id,
                       FileName = s.FileName,

                       CustomerId = s.CustomerId,
                       ContractId = s.ContractId,
                       HaulierId = s.HaulierId,
                       PurchaseId = s.PurchaseId,
                       ETicketId = s.ETicketId,

                       Name = s.Name,
                       UploadedDate = s.UploadedDate,
                   })
                   .AsQueryable();

        if (documentUploadPage == DocumentUploadPage.Customer)
        {
            q = q.Where(w => w.CustomerId == itemId);
        }

        if (documentUploadPage == DocumentUploadPage.Contract)
        {
            q = q.Where(w => w.ContractId == itemId);
        }

        if (documentUploadPage == DocumentUploadPage.Haulier)
        {
            q = q.Where(w => w.HaulierId == itemId);
        }

        if (documentUploadPage == DocumentUploadPage.Purchase)
        {
            q = q.Where(w => w.PurchaseId == itemId);
        }

        if (documentUploadPage == DocumentUploadPage.ETicket)
        {
            q = q.Where(w => w.ETicketId == itemId);
        }

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteDocument(Guid id)
    {
        var document = await _db.Documents.FirstOrDefaultAsync(d => d.Id == id);
        _db.Documents.Remove(document);
        await _db.SaveChangesAsync();

        await _azureStorageService.DeleteBlobAsync("main", document.AzureStoragePath);

        return Ok(new FormResponseModel<bool>(true));
    }
}
