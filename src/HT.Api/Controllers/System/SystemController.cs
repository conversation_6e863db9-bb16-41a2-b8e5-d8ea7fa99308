using HT.Api.Services;
using HT.Database;
using HT.Shared.Models.Site;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.System;

[ApiController]
[Route("[controller]/[action]")]
public class SystemController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public SystemController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> GetSiteSettings()
    {
        var siteSettingsModel = await _db.Tenants
                                   .Where(i => i.Id == _currentUserService.TenantId)
                                   .Select(s => new SiteSettingsModel
                                   {
                                       Name = s.Name,
                                       LogoUrl = s.LogoUrl,
                                       PrimaryColour = s.PrimaryColour,
                                       SecondaryColour = s.SecondaryColour,
                                       WorkforceModule = s.WorkforceModule,
                                       WeighbridgeModule = s.WeighbridgeModule,
                                       RouteModule = s.RouteModule,
                                       OrderModule = s.OrderModule,
                                       FleetModule = s.FleetModule,
                                       PurchaseLedgerModule = s.PurchaseLedgerModule,
                                       WebsiteModule = s.WebsiteModule,
                                       HaulierModule = s.HaulierModule,
                                   })
                                   .FirstOrDefaultAsync();

        if (siteSettingsModel != null && siteSettingsModel.WebsiteModule)
        {
            var defaultDomain = await _db.WebsiteDomains.FirstOrDefaultAsync(w => w.Website.TenantId == _currentUserService.TenantId && w.Default);

            siteSettingsModel.DefaultWebsiteUrl = defaultDomain?.Domain ?? string.Empty;
        }

        return Ok(siteSettingsModel ?? new SiteSettingsModel
        {
            Name = "",
            LogoUrl = "",
            PrimaryColour = "",
            SecondaryColour = ""
        });
    }
}
