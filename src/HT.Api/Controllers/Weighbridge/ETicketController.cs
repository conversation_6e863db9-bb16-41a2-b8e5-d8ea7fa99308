﻿using HT.Api.Services.ETicket;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class ETicketController : ControllerBase
{
    private readonly ETicketService _eTicketService;

    public ETicketController(ETicketService eTicketService)
    {
        _eTicketService = eTicketService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateETicket([FromBody] CreateETicketModel createETicketModel)
    {
        return Ok(await _eTicketService.CreateETicketAsync(createETicketModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListETickets([FromBody] GridParametersModel gridParametersModel, Guid? contractId, Guid? customerId)
    {
        return Ok(await _eTicketService.ListETicketsAsync(gridParametersModel, contractId, customerId));
    }

    [HttpPost]
    public async Task<IActionResult> GetETicket(Guid id)
    {
        return Ok(await _eTicketService.GetETicketAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> GetPrintETicket(Guid id)
    {
        return Ok(await _eTicketService.GetPrintETicketAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateETicket(ETicketModel eTicketModel)
    {
        return Ok(await _eTicketService.UpdateETicketAsync(eTicketModel));
    }

    [HttpPost]
    public async Task<IActionResult> VoidETicket(ETicketModel eTicketModel)
    {
        return Ok(await _eTicketService.VoidETicketAsync(eTicketModel));
    }

    [HttpPost]
    public async Task<IActionResult> CloseETicket(ETicketModel eTicketModel)
    {
        return Ok(await _eTicketService.CloseETicketAsync(eTicketModel));
    }

    [HttpPost]
    public async Task<IActionResult> BulkCloseETickets(ETicketBulkCloseModel eTicketBulkCloseModel)
    {
        return Ok(await _eTicketService.BulkCloseETicketsAsync(eTicketBulkCloseModel));
    }

    [HttpPost]
    public async Task<IActionResult> ReOpenTicket(ETicketModel eTicketModel)
    {
        return Ok(await _eTicketService.ReOpenTicketAsync(eTicketModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetCustomersFromVehicle(Guid id)
    {
        return Ok(await _eTicketService.GetCustomersFromVehicleAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> ListVehiclesDropdown()
    {
        return Ok(await _eTicketService.ListVehiclesDropdownAsync());
    }

    [HttpPost]
    public async Task<IActionResult> GetETicketContractModel(Guid contractId)
    {
        return Ok(await _eTicketService.GetETicketContractModelAsync(contractId));
    }

    [HttpPost]
    public async Task<IActionResult> ListSurchargesDropdown(Guid siteId)
    {
        return Ok(await _eTicketService.ListSurchargesDropdownAsync(siteId));
    }

    [HttpPost]
    public async Task<IActionResult> ListProductsDropdown()
    {
        return Ok(await _eTicketService.ListProductsDropdownAsync());
    }

}
