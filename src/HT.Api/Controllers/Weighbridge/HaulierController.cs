using HT.Api.Services.Haulier;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.Haulier;
using HT.Shared.Models.HaulierVehicle;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class HaulierController(HaulierService haulierService) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> CreateHaulier([FromBody] HaulierCreateModel HaulierCreateModel)
    {
        return Ok(await haulierService.CreateHaulierAsync(HaulierCreateModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetHaulier(Guid id)
    {
        return Ok(await haulierService.GetHaulierAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateHaulier(HaulierModel HaulierModel)
    {
        return Ok(await haulierService.UpdateHaulierAsync(HaulierModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteHaulier(Guid id)
    {
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> ListHauliers([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await haulierService.ListHauliersAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListHaulierVehicleDriverDropdown()
    {
        return Ok(await haulierService.ListHaulierVehicleDriverDropdownAsync());
    }

    [HttpPost]
    public async Task<IActionResult> ListHaulierVehicleDropdown()
    {
        return Ok(await haulierService.ListHaulierVehicleDropdownAsync());
    }

    [HttpPost]
    public async Task<IActionResult> ListHauliersDropdown()
    {
        return Ok(await haulierService.ListHauliersDropdownAsync());
    }

    /// <summary>
    /// Gets the driver, haulier and vehicle details
    /// </summary>
    /// <param name="driverUserId"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> GetDriverDetailsAsync(Guid driverUserId)
    {
        return Ok(await haulierService.GetDriverDetailsAsync(driverUserId));
    }

    /// <summary>
    /// Gets the grid details for Haulier Drivers
    /// </summary>
    /// <param name="gridParametersModel"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> GetHauliersGrid([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await haulierService.GetHauliersGridAsync(gridParametersModel));
    }
}
