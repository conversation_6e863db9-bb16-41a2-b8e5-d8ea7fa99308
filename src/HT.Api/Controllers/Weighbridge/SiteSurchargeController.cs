﻿using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Site;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.Weighbridge;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class SiteSurchargeController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public SiteSurchargeController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateSiteSurcharge([FromBody] SiteSurchargeCreateModel SiteSurchargeCreateModel)
    {
        SiteSurchargeCreateModel.Id = Guid.NewGuid();

        _db.SiteSurcharges.Add(new SiteSurcharge
        {
            Id = SiteSurchargeCreateModel.Id,
            TenantId = _currentUserService.TenantId,
            SiteId = SiteSurchargeCreateModel.SiteId,
            Status = (int)GenericStatus.Active,
            Name = SiteSurchargeCreateModel.Name,
            Price = SiteSurchargeCreateModel.Price
        });
        await _db.SaveChangesAsync();

        var formResponseModel = new FormResponseModel<SiteSurchargeCreateModel>(SiteSurchargeCreateModel);
        return Ok(formResponseModel);
    }

    [HttpPost]
    public async Task<IActionResult> GetSiteSurcharge(Guid id)
    {
        var SiteSurchargeModel = await _db.SiteSurcharges
                              .Where(i => i.Id == id)
                              .Select(s => new SiteSurchargeModel
                              {
                                  Id = s.Id,
                                  Status = s.Status,
                                  Name = s.Name,
                                  Price = s.Price,
                              })
                              .FirstOrDefaultAsync();

        return Ok(SiteSurchargeModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateSiteSurcharge(SiteSurchargeModel SiteSurchargeModel)
    {
        var SiteSurcharge = await _db.SiteSurcharges.FirstOrDefaultAsync(s => s.Id == SiteSurchargeModel.Id);

        SiteSurcharge.Status = SiteSurchargeModel.Status;
        SiteSurcharge.Name = SiteSurchargeModel.Name;
        SiteSurcharge.Price = SiteSurchargeModel.Price;

        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<SiteSurchargeModel>(SiteSurchargeModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteSiteSurcharge(Guid id)
    {
        return Ok();
    }


    [HttpPost]
    public async Task<IActionResult> ListSiteSurcharges(Guid siteId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.SiteSurcharges
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.SiteId == siteId)
                   .Select(s => new SiteSurchargeListItemModel
                   {
                       Id = s.Id,
                       Status = s.Status,
                       Name = s.Name,
                       Price = s.Price
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

}
