using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class ContractAllowanceController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly ContractAllowanceService _contractAllowanceService;

    public ContractAllowanceController(DatabaseContext db, CurrentUserService currentUserService, ContractAllowanceService contractAllowanceService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _contractAllowanceService = contractAllowanceService;
    }

    [HttpPost]
    public async Task<IActionResult> GetContractAllowanceOverall(Guid contractId)
    {
        var contractAllowanceOverallModel = await _contractAllowanceService.GetContractAllowanceOverallAsync(contractId);

        return Ok(contractAllowanceOverallModel);
    }

    [HttpPost]
    public async Task<IActionResult> ListContractAllowances([FromBody] GridParametersModel gridParametersModel, Guid contractId)
    {
        var q = _db.ContractAllowances
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.ContractId == contractId)
                   .Select(s => new ContractAllowanceListItemModel
                   {
                       Id = s.Id,
                       DateTime = s.DateTime,
                       Message = s.Message,
                       AllowanceAmountKG = s.AllowanceAmountTonnes,
                       User = s.User.Email
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> CreateContractAllowance([FromBody] ContractAllowanceCreateModel contractAllowanceCreateModel)
    {
        _db.ContractAllowances.Add(new ContractAllowance
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            UserId = _currentUserService.UserId,
            DateTime = DateTime.Now,
            ContractId = contractAllowanceCreateModel.ContractId,
            AllowanceAmountTonnes = contractAllowanceCreateModel.AllowanceAmountKG,
            Message = contractAllowanceCreateModel.Message
        });
        await _db.SaveChangesAsync();

        await _contractAllowanceService.UpdateContractAllowanceAsync(contractAllowanceCreateModel.ContractId);

        return Ok(contractAllowanceCreateModel);
    }



}