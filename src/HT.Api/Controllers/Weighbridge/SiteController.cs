﻿using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.Site;
using HT.Shared.Models.User;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.Weighbridge;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class SiteController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public SiteController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateSite([FromBody] SiteCreateModel SiteCreateModel)
    {
        SiteCreateModel.Id = Guid.NewGuid();

        _db.Sites.Add(new Site
        {
            Id = SiteCreateModel.Id,
            TenantId = _currentUserService.TenantId,
            Code = SiteCreateModel.Code,
            Name = SiteCreateModel.Name,
        });
        await _db.SaveChangesAsync();

        var formResponseModel = new FormResponseModel<SiteCreateModel>(SiteCreateModel);
        return Ok(formResponseModel);
    }

    [HttpPost]
    public async Task<IActionResult> GetSite(Guid id)
    {
        var SiteModel = await _db.Sites
                              .Where(i => i.Id == id)
                              .Select(s => new SiteModel
                              {
                                  Id = s.Id,
                                  Code = s.Code,
                                  Name = s.Name,
                                  AddressLine1 = s.AddressLine1,
                                  AddressLine2 = s.AddressLine2,
                                  AddressLine3 = s.AddressLine3,
                                  AddressLine4 = s.AddressLine4,
                                  AddressPostcode = s.AddressPostcode,
                                  HasRecycling = s.HasRecycling,
                              })
                              .FirstOrDefaultAsync();

        return Ok(SiteModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateSite(SiteModel SiteModel)
    {
        var Site = await _db.Sites.FirstOrDefaultAsync(s => s.Id == SiteModel.Id);

        Site.Code = SiteModel.Code;
        Site.Name = SiteModel.Name;

        Site.AddressLine1 = SiteModel.AddressLine1;
        Site.AddressLine2 = SiteModel.AddressLine2;
        Site.AddressLine3 = SiteModel.AddressLine3;
        Site.AddressLine4 = SiteModel.AddressLine4;
        Site.AddressPostcode = SiteModel.AddressPostcode;
        Site.HasRecycling = SiteModel.HasRecycling;

        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<SiteModel>(SiteModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteSite(Guid id)
    {
        return Ok();
    }


    [HttpPost]
    public async Task<IActionResult> ListSites([FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.Sites
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Select(s => new SiteListItemModel
                   {
                       Id = s.Id,
                       Code = s.Code,
                       Name = s.Name,
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListSitesForETicket()
    {
        var q = await _db.Sites
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Select(s => new SiteListItemModel
                   {
                       Id = s.Id,
                       Name = s.Name,
                   })
                   .ToListAsync();

        return Ok(q);
    }

    [HttpPost]
    public async Task<IActionResult> ListSitesDropdown(bool includeHaulierSites = false)
    {
        var userSecurity = await _currentUserService.GetUserSecurityAsync();

        var sitesQuery = _db.Sites
                         .Where(w => w.TenantId == _currentUserService.TenantId)
                         .AsQueryable();

        if (!includeHaulierSites)
        {
            sitesQuery = sitesQuery.Where(w => !w.HasRecycling);
        }

        if (userSecurity.UserType is not ((int)UserType.SuperUser) and not ((int)UserType.Admin))
        {
            sitesQuery = sitesQuery.Where(s => userSecurity.Sites.Contains(s.Id));
        }

        var sites = await sitesQuery.Select(s => new DropdownListItem { Id = s.Id.ToString(), Name = s.Name }).ToListAsync();

        return Ok(sites);
    }

}
