using HT.Api.Services.Customer;
using HT.Api.Services.Weighbridge.Contract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers.Customer;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class ContractSearchController(ContractSearchService contractSearchService) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> SearchHaulierContracts(string searchTerm)
    {
        return Ok(await contractSearchService.SearchHaulierContractAsync(searchTerm));
    }
}
