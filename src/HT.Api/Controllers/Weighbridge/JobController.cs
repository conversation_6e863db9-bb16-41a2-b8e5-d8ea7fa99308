﻿using HT.Api.Services.ETicket;
using HT.Api.Services.Haulier;
using HT.Api.Services.Weighbridge.Job;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class JobController(JobService jobService) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> CreateJob()
    {
        return Ok(await jobService.CreateJobAsync());
    }

    [HttpPost]
    public async Task<IActionResult> ListJobs([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await jobService.ListJobsAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetJob(Guid id)
    {
        return Ok(await jobService.GetJobAsync(id));
    }

    //[HttpPost]
    //public async Task<IActionResult> GetPrintETicket(Guid id)
    //{
    //    return Ok(await _eTicketService.GetPrintETicketAsync(id));
    //}

    [HttpPost]
    public async Task<IActionResult> UpdateJob([FromBody] JobModel jobModel)
    {
        return Ok(await jobService.UpdateJobAsync(jobModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetProductPrice([FromBody] JobProductModel productModel, Guid? customerId, JobType jobType)
    {
        return Ok(await jobService.GetProductPriceAsync(productModel, customerId, jobType));
    }

    [HttpPost]
    public async Task<IActionResult> VoidJob(JobModel jobModel)
    {
        return Ok(await jobService.UpdateStatusAsync(jobModel, Shared.Enums.JobStatus.Void));
    }

    [HttpPost]
    public async Task<IActionResult> CloseJob([FromBody] JobModel jobModel)
    {
        return Ok(await jobService.UpdateStatusAsync(jobModel, Shared.Enums.JobStatus.Closed));
    }

    [HttpPost]
    public async Task<IActionResult> BulkCloseJobs(JobBulkCloseModel jobBulkCloseModel)
    {
        return Ok(await jobService.BulkCloseJobsAsync(jobBulkCloseModel));
    }

    [HttpPost]
    public async Task<IActionResult> ReOpenJob(JobModel jobModel)
    {
        return Ok(await jobService.UpdateStatusAsync(jobModel, Shared.Enums.JobStatus.Saved));
    }

    [HttpPost]
    public async Task<IActionResult> GetPrintJob(Guid id)
    {
        return Ok(await jobService.GetPrintJobAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> ListInternalTransferLocationDropdown()
    {
        return Ok(await jobService.ListInternalTransferLocationDropdownAsync());
    }
}
