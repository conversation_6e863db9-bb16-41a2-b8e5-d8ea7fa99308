using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.HaulierVehicleType;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class HaulierVehicleTypeController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public HaulierVehicleTypeController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateHaulierVehicleType([FromBody] HaulierVehicleTypeCreateModel haulierVehicleTypeCreateModel)
    {
        var haulierVehicle = new HaulierVehicleType
        {
            Id = HaulierVehicleTypeCreateModel.Id,
            TenantId = _currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            Name = haulierVehicleTypeCreateModel.Name
        };

        _db.HaulierVehicleTypes.Add(haulierVehicle);
        await _db.SaveChangesAsync();

        return Ok(haulierVehicleTypeCreateModel);
    }

    [HttpPost]
    public async Task<IActionResult> GetHaulierVehicleType(Guid haulierVehicleTypeId)
    {
        var HaulierVehicleTypeModel = await _db.HaulierVehicleTypes
                                               .Where(i => i.Id == haulierVehicleTypeId)
                                               .Select(s => new HaulierVehicleTypeModel
                                               {
                                                   Id = s.Id,
                                                   Name = s.Name,
                                               })
                                               .FirstOrDefaultAsync();

        return Ok(HaulierVehicleTypeModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateHaulierVehicleType(HaulierVehicleTypeModel HaulierVehicleTypeModel)
    {
        var HaulierVehicleType = await _db.HaulierVehicleTypes.FirstOrDefaultAsync(s => s.Id == HaulierVehicleTypeModel.Id);
        HaulierVehicleType.Name = HaulierVehicleTypeModel.Name;
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<HaulierVehicleTypeModel>(HaulierVehicleTypeModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteHaulierVehicleType(Guid id)
    {
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> ListHaulierVehicleTypes([FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.HaulierVehicleTypes.Where(w => w.TenantId == _currentUserService.TenantId)
                                       .Select(s => new HaulierVehicleTypeListItemModel
                                       {
                                           Id = s.Id,
                                           Name = s.Name
                                       })
                                       .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

}
