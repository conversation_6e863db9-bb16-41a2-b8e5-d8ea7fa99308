﻿using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Site;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class SitePermitController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public SitePermitController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateSitePermit(SitePermitCreateModel sitePermitCreateModel)
    {
        var contractLink = new SitePermit
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            SiteId = sitePermitCreateModel.SiteId,
            Status = (int)GenericStatus.Active,
            LicenceNumber = sitePermitCreateModel.LicenceNumber,
            Name = sitePermitCreateModel.Name,
            TonsPerYear = sitePermitCreateModel.TonsPerYear,
        };
        _db.SitePermits.Add(contractLink);
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<SitePermitCreateModel>(sitePermitCreateModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetSitePermit(Guid id)
    {
        var SitePermitModel = await _db.SitePermits
                              .Where(i => i.Id == id)
                              .Select(s => new SitePermitModel
                              {
                                  Id = s.Id,
                                  Name = s.Name,
                                  Status = s.Status,
                                  LicenceNumber = s.LicenceNumber,
                                  TonsPerYear = s.TonsPerYear,
                              })
                              .FirstOrDefaultAsync();

        return Ok(SitePermitModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateSitePermit(SitePermitModel SitePermitModel)
    {
        var SitePermit = await _db.SitePermits.FirstOrDefaultAsync(s => s.Id == SitePermitModel.Id);

        SitePermit.Name = SitePermitModel.Name;
        SitePermit.Status = SitePermitModel.Status;
        SitePermit.LicenceNumber = SitePermitModel.LicenceNumber;
        SitePermit.TonsPerYear = SitePermitModel.TonsPerYear;

        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<SitePermitModel>(SitePermitModel));
    }


    [HttpPost]
    public async Task<IActionResult> ListSitePermits(Guid siteId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.SitePermits
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.SiteId == siteId)
                   .Select(s => new SitePermitListItemModel
                   {
                       Id = s.Id,
                       Status = s.Status,
                       LicenceNumber = s.LicenceNumber,
                       Name = s.Name,
                       TonsPerYear = s.TonsPerYear
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListSitePermitProducts(Guid sitePermitId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.SitePermitProductLinks
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.SitePermitId == sitePermitId)
                   .Select(s => new SitePermitProductListItemModel
                   {
                       Id = s.Id,
                       Name = s.Product.Name,
                       EWCCode = s.Product.EWCCode
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> AssignSitePermitProduct(SitePermitProductAssignModel sitePermitProductAssignModel)
    {
        var contractLink = new SitePermitProductLink
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            SitePermitId = sitePermitProductAssignModel.SitePermitId,
            ProductId = sitePermitProductAssignModel.ProductId,
        };
        _db.SitePermitProductLinks.Add(contractLink);
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<SitePermitProductAssignModel>(sitePermitProductAssignModel));
    }


    [HttpPost]
    public async Task<IActionResult> ListProductsDropdown()
    {
        var q = await _db.Products.Where(w => w.TenantId == _currentUserService.TenantId).Select(s => new DropdownListItem { Id = s.Id.ToString(), Name = s.Name }).ToListAsync();

        return Ok(q);
    }

}
