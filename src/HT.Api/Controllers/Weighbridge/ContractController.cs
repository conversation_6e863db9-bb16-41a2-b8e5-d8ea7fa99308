using HT.Api.Services;
using HT.Api.Services.Common.Address;
using HT.Api.Services.ETicket;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using HT.Shared.Models.Contract;
using HT.Shared.Models.User;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class ContractController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly ContractPricingService _contractPricingService;
    private readonly ContractService _contractService;
    private readonly AddressService _addressService;
    private readonly ETicketPricingService _eTicketPricingService;
    private readonly ContractAllowanceService _contractAllowanceService;

    public ContractController(DatabaseContext db,
        CurrentUserService currentUserService,
        ContractPricingService contractPricingService,
        ContractService contractService,
        AddressService addressService,
        ETicketPricingService eTicketPricingService,
        ContractAllowanceService contractAllowanceService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _contractPricingService = contractPricingService;
        _contractService = contractService;
        _addressService = addressService;
        _eTicketPricingService = eTicketPricingService;
        _contractAllowanceService = contractAllowanceService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateContract([FromBody] ContractCreateModel ContractCreateModel)
    {
        ContractCreateModel.Id = Guid.NewGuid();

        int ticketNumber = (await _db.Contracts
            .Where(s => s.TenantId == _currentUserService.TenantId)
            .OrderByDescending(s => s.ContractNumber)
            .FirstOrDefaultAsync())?.ContractNumber ?? 0;

        bool siteHasRecycling = await _db.SitePermits
            .Where(w => w.Id == ContractCreateModel.SitePermitId)
            .Select(s => s.Site.HasRecycling)
            .FirstAsync();

        _db.Contracts.Add(new Contract
        {
            Id = ContractCreateModel.Id,
            TenantId = _currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            ContractNumber = ticketNumber + 1,
            Name = ContractCreateModel.Name,
            SitePermitId = ContractCreateModel.SitePermitId,
            IsHaulierContract = siteHasRecycling,
        });
        await _db.SaveChangesAsync();

        return Ok(ContractCreateModel);
    }

    [HttpPost]
    public async Task<IActionResult> GetContract(Guid id)
    {
        var ContractModel = await _db.Contracts
                               .Where(i => i.Id == id)
                               .Select(s => new ContractModel
                               {
                                   Id = s.Id,
                                   Status = s.Status,
                                   ContractNumber = s.ContractNumber,
                                   Name = s.Name,
                                   OrderNumber = s.OrderNumber,
                                   AnalysisEnquiryNumber = s.AnalysisEnquiryNumber,
                                   AddressLine1 = s.AddressLine1,
                                   AddressLine2 = s.AddressLine2,
                                   AddressLine3 = s.AddressLine3,
                                   AddressLine4 = s.AddressLine4,
                                   AddressPostcode = s.AddressPostcode,
                                   Longitude = s.AddressLongitude ?? 0,
                                   Latitude = s.AddressLatitude ?? 0,
                                   SitePermitId = s.SitePermitId,
                                   SitePermitProductLinkId = s.SitePermitProductLinkId,
                                   SiteEmail = s.SiteEmail,
                                   Borough = s.Borough,
                                   TotalAllowance = s.OverallTotalAllowance,
                                   IsHaulierContract = s.IsHaulierContract,
                                   TipAddress = s.TipAddress != null
                                        ? new Shared.Models.Address.AddressModel(s.TipAddress)
                                        : new Shared.Models.Address.AddressModel(),
                               })
                               .FirstOrDefaultAsync();

        return Ok(ContractModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateContract(ContractModel ContractModel)
    {
        var Contract = await _db.Contracts
            .Include(i => i.TipAddress)
            .FirstOrDefaultAsync(s => s.Id == ContractModel.Id);

        Contract.Status = ContractModel.Status;

        Contract.Name = ContractModel.Name;
        Contract.OrderNumber = ContractModel.OrderNumber;

        Contract.AnalysisEnquiryNumber = ContractModel.AnalysisEnquiryNumber;
        Contract.AddressLine1 = ContractModel.AddressLine1;
        Contract.AddressLine2 = ContractModel.AddressLine2;
        Contract.AddressLine3 = ContractModel.AddressLine3;
        Contract.AddressLine4 = ContractModel.AddressLine4;
        Contract.AddressPostcode = ContractModel.AddressPostcode;
        Contract.AddressLongitude = ContractModel.Longitude;
        Contract.AddressLatitude = ContractModel.Latitude;
        Contract.SitePermitId = ContractModel.SitePermitId;
        Contract.SitePermitProductLinkId = ContractModel.SitePermitProductLinkId;
        Contract.SiteEmail = ContractModel.SiteEmail;
        Contract.Borough = ContractModel.Borough;
        Contract.OverallTotalAllowance = ContractModel.TotalAllowance;

        if (ContractModel.TipAddress.HasAddress)
        {
            ContractModel.TipAddress.Id = Guid.NewGuid();
            var addressId = await _addressService.CreateUpdateAddressAsync(ContractModel.TipAddress);
            Contract.TipAddressId = addressId;
        }

        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<ContractModel>(ContractModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteContract(Guid id)
    {
        var contract = await _db.Contracts
            .Include(i => i.ETickets)
            .Include(i => i.ContractLinks)
            .FirstOrDefaultAsync(s => s.Id == id);

        if (contract.ETickets.Any())
        {
            contract.Status = (int)GenericStatus.Deleted;
            await _db.SaveChangesAsync();
            return Ok(new FormResponseModel<string>());
        }

        _db.ContractLinks.RemoveRange(contract.ContractLinks);
        _db.Contracts.Remove(contract);
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<string>());
    }

    [HttpPost]
    public async Task<IActionResult> ListContracts([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _contractService.ListContracts(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListContractCustomers(Guid id, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.ContractLinks
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.ContractId == id && w.Contract.Status != (int)GenericStatus.Deleted)
                   .Select(s => new ContractCustomerListItemModel
                   {
                       Id = s.Id,
                       Code = s.Customer.Code,
                       Name = s.Customer.Name,
                       Price = s.Price,
                       ProductName = s.Product != null ? s.Product.Name : null,
                       PricePerTonne = s.PricePerTonne,
                       PricePerLoad = s.PricePerLoad
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }


    [HttpPost]
    public async Task<IActionResult> ListContractCustomersAssign()
    {
        var q = await _db.Customers.Where(w => w.TenantId == _currentUserService.TenantId)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = s.Code + " - " + s.Name,
            })
            .ToListAsync();

        return Ok(q);
    }

    [HttpPost]
    public async Task<IActionResult> ListContractsForCustomerDropdown(Guid customerId, Guid siteId, Guid? mustHaveContractId)
    {
        var mainQuery = await _db.ContractLinks
            .Where(w => w.TenantId == _currentUserService.TenantId)
            .Where(w => w.CustomerId == customerId)
            .Where(w => w.Contract.Status == (int)GenericStatus.Active)
            .Where(w => w.Contract.SitePermit.SiteId == siteId)
            .Select(s => new DropdownListItem
            {
                Id = s.Contract.Id.ToString(),
                Name = $"{s.Contract.ContractNumber} - {s.Contract.Name}"
            })
            .ToListAsync();

        if (mustHaveContractId.HasValue)
        {
            var mustHaveItem = await _db.ContractLinks
                .Where(w => w.Contract.Id == mustHaveContractId.Value)
                .Select(s => new DropdownListItem
                {
                    Id = s.Contract.Id.ToString(),
                    Name = $"{s.Contract.ContractNumber} - {s.Contract.Name}"
                })
                .FirstOrDefaultAsync();

            if (mustHaveItem != null && !mainQuery.Any(q => q.Id == mustHaveItem.Id))
            {
                mainQuery.Add(mustHaveItem);
            }
        }

        return Ok(mainQuery);
    }


    [HttpPost]
    public async Task<IActionResult> ListSitePermits()
    {
        var userSecurity = await _currentUserService.GetUserSecurityAsync();

        var q = _db.SitePermits.Where(w => w.TenantId == _currentUserService.TenantId)
                                   .Where(w => w.Status == (int)GenericStatus.Active)
                                   .AsQueryable();

        if (userSecurity.UserType is not ((int)UserType.SuperUser) and not ((int)UserType.Admin))
        {
            q = q.Where(s => userSecurity.Sites.Contains(s.SiteId));
        }

        var query = await q.Select(s => new DropdownListItem
        {
            Id = s.Id.ToString(),
            Name = s.LicenceNumber + " - " + s.Name,
        })
        .ToListAsync();

        return Ok(query);
    }


    [HttpPost]
    public async Task<IActionResult> ListSitePermitProducts(Guid sitePermitId)
    {
        var q = await _db.SitePermitProductLinks
                                                .Where(w => w.TenantId == _currentUserService.TenantId)
                                                .Where(w => w.SitePermitId == sitePermitId)
                                                .Select(s => new DropdownListItem
                                                {
                                                    Id = s.Id.ToString(),
                                                    Name = s.Product.EWCCode + " - " + s.Product.Name
                                                })
                                                .ToListAsync();

        return Ok(q);
    }

    #region Assign 

    [HttpPost]
    public async Task<IActionResult> AssignCustomer(ContractCustomerAssignModel contractCustomerAssignModel)
    {
        var contractLink = new ContractLink
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            CustomerId = contractCustomerAssignModel.CustomerId,
            ContractId = contractCustomerAssignModel.ContractId,
            Price = contractCustomerAssignModel.Price,
            PricePerTonne = contractCustomerAssignModel.PricePerTon,
            PricePerLoad = contractCustomerAssignModel.PricePerLoad,
            AnalysisEnquiryNumber = contractCustomerAssignModel.AnalysisEnquiryNumber,
            OrderNumber = contractCustomerAssignModel.OrderNumber,
            ProductId = contractCustomerAssignModel.ProductId,
        };
        _db.ContractLinks.Add(contractLink);
        await _db.SaveChangesAsync();

        await _contractPricingService.UpdateContractETicketsPricing(contractCustomerAssignModel.ContractId);

        return Ok(new FormResponseModel<ContractCustomerAssignModel>(contractCustomerAssignModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetAssignCustomer(Guid id)
    {
        var ContractModel = await _db.ContractLinks
                               .Where(i => i.Id == id)
                               .Select(s => new ContractCustomerAssignModel
                               {
                                   Id = s.Id,
                                   ContractId = s.ContractId,
                                   CustomerId = s.CustomerId.Value,
                                   PricePerTon = s.PricePerTonne,
                                   PricePerLoad = s.PricePerLoad,
                                   AnalysisEnquiryNumber = s.AnalysisEnquiryNumber,
                                   OrderNumber = s.OrderNumber,
                                   ProductId = s.ProductId,
                               })
                               .FirstOrDefaultAsync();

        return Ok(ContractModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateAssignCustomer(ContractCustomerAssignModel contractCustomerAssignModel)
    {
        var contractAssign = await _db.ContractLinks.FirstOrDefaultAsync(s => s.Id == contractCustomerAssignModel.Id);

        contractAssign.CustomerId = contractCustomerAssignModel.CustomerId;
        contractAssign.PricePerLoad = contractCustomerAssignModel.PricePerLoad;
        contractAssign.PricePerTonne = contractCustomerAssignModel.PricePerTon;
        contractAssign.AnalysisEnquiryNumber = contractCustomerAssignModel.AnalysisEnquiryNumber;
        contractAssign.OrderNumber = contractCustomerAssignModel.OrderNumber;
        contractAssign.ProductId = contractCustomerAssignModel.ProductId;

        await _db.SaveChangesAsync();

        await _contractPricingService.UpdateContractETicketsPricing(contractCustomerAssignModel.ContractId);

        return Ok(new FormResponseModel<ContractCustomerAssignModel>(contractCustomerAssignModel));
    }

    #endregion


    [HttpPost]
    public async Task<IActionResult> AddJob(ContractAddJobModel contractAddJobModel)
    {
        int ticketNumber = (await _db.Contracts
            .Where(s => s.TenantId == _currentUserService.TenantId)
            .OrderByDescending(s => s.ContractNumber)
            .FirstOrDefaultAsync())?.ContractNumber ?? 0;

        bool siteHasRecycling = await _db.SitePermits
            .Where(w => w.Id == new Guid("8f00f887-baff-4152-9b0b-f209306b7dfb"))
            .Select(s => s.Site.HasRecycling)
            .FirstAsync();

        var contract = new Contract
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            ContractNumber = ticketNumber + 1,
            Name = contractAddJobModel.Name,
            SitePermitId = new Guid("8f00f887-baff-4152-9b0b-f209306b7dfb"), //contractAddJobModel.SitePermitId,
            IsHaulierContract = siteHasRecycling,
            SitePermitProductLinkId = contractAddJobModel.ProductId,
            OrderNumber = contractAddJobModel.OrderNumber,

            AddressLine1 = contractAddJobModel.AddressLine1,
            AddressLine2 = contractAddJobModel.AddressLine2,
            AddressLine3 = contractAddJobModel.AddressLine3,
            AddressLine4 = contractAddJobModel.AddressLine4,
            AddressPostcode = contractAddJobModel.AddressPostcode,
            Borough = contractAddJobModel.Borough,
        };

        _db.Contracts.Add(contract);

        var contractLink = new ContractLink
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            CustomerId = contractAddJobModel.CustomerId,
            ContractId = contract.Id,
            //Price = contractCustomerAssignModel.Price,
            PricePerTonne = contractAddJobModel.PricePerTonne,
            PricePerLoad = contractAddJobModel.PricePerLoad,
            //AnalysisEnquiryNumber = contractCustomerAssignModel.AnalysisEnquiryNumber,
            //OrderNumber = contractCustomerAssignModel.OrderNumber,
            //ProductId = contractAddJobModel.ProductId,
        };
        _db.ContractLinks.Add(contractLink);

        var eTicket = await _db.ETickets.FirstAsync(s => s.Id == contractAddJobModel.ETicketId);
        eTicket.ContractId = contract.Id;
        eTicket.OrderNumber = contractAddJobModel.OrderNumber;
        await _db.SaveChangesAsync();

        await _eTicketPricingService.UpdatePriceAsync(contractAddJobModel.ETicketId);

        await _contractAllowanceService.UpdateContractAllowanceAsync(contract.Id);

        return Ok(new FormResponseModel<bool>());
    }

}
