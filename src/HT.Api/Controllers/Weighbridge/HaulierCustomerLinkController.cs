using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.HaulierVehicle;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class HaulierCustomerLinkController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public HaulierCustomerLinkController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> AssignHaulierCustomerLink(HaulierCustomerLinkAssignModel customerAssignSiteModel)
    {
        var haulierCustomerLink = new HaulierCustomerLink
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            CustomerId = customerAssignSiteModel.CustomerId,
            HaulierId = customerAssignSiteModel.HaulierId
        };
        _db.HaulierCustomerLinks.Add(haulierCustomerLink);
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<HaulierCustomerLinkAssignModel>(customerAssignSiteModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetHaulierCustomerLink(Guid haulierCustomerLinkId)
    {
        var HaulierCustomerLinkModel = await _db.HaulierCustomerLinks
                               .Where(i => i.Id == haulierCustomerLinkId)
                               .Select(s => new HaulierCustomerLinkModel
                               {
                                   Id = s.Id,
                                   Name = s.Customer.Name
                               })
                               .FirstOrDefaultAsync();

        return Ok(HaulierCustomerLinkModel);
    }

    [HttpPost]
    public async Task<IActionResult> ListHaulierCustomerLinks(Guid haulierId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.HaulierCustomerLinks.Where(w => w.TenantId == _currentUserService.TenantId)
                                   .Where(w => w.HaulierId == haulierId)
                                   .Select(s => new HaulierCustomerLinkListItemModel
                                   {
                                       Id = s.Id,
                                       Name = s.Customer.Name
                                   })
                                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListCustomersDropdown(Guid haulierId)
    {
        var q = await _db.Customers
                         .Where(w => w.TenantId == _currentUserService.TenantId)
                         .Select(s => new DropdownListItem
                         {
                             Id = s.Id.ToString(),
                             Name = s.Name
                         }).ToListAsync();

        return Ok(q);
    }


}
