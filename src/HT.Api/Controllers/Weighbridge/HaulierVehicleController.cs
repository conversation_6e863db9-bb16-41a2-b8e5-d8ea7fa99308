using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.HaulierVehicle;
using HT.Shared.Models.User;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.Weighbridge;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class HaulierVehicleController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public HaulierVehicleController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateHaulierVehicle([FromBody] HaulierVehicleCreateModel haulierVehicleCreateModel)
    {
        var response = new FormResponseModel<HaulierVehicleCreateModel>();

        //string existingVehicle = await _db.HaulierVehicles
        //                    .Where(v => v.Registration.ToLower().Trim() == haulierVehicleCreateModel.Registration.ToLower().Trim())
        //                    .Select(s => s.Haulier.Name)
        //                    .FirstOrDefaultAsync();

        //if (!string.IsNullOrWhiteSpace(existingVehicle))
        //{
        //    response.AddError($"A vehicle with this registration already exists for {existingVehicle}.");
        //    return Ok(response);
        //}

        haulierVehicleCreateModel.Id = Guid.NewGuid();

        _db.HaulierVehicles.Add(new HaulierVehicle
        {
            Id = haulierVehicleCreateModel.Id,
            TenantId = _currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            HaulierId = haulierVehicleCreateModel.HaulierId,
            Registration = haulierVehicleCreateModel.Registration,
            TareWeight = haulierVehicleCreateModel.TareWeight,
            HaulierVehicleTypeId = haulierVehicleCreateModel.HaulierVehicleTypeId,
        });
        await _db.SaveChangesAsync();

        return Ok(response);
    }

    [HttpPost]
    public async Task<IActionResult> GetHaulierVehicle(Guid haulierVehicleId)
    {
        var haulierVehicleModel = await _db.HaulierVehicles
                               .Where(i => i.Id == haulierVehicleId)
                               .Select(s => new HaulierVehicleModel
                               {
                                   Id = s.Id,
                                   Registration = s.Registration,
                                   HaulierVehicleTypeId = s.HaulierVehicleTypeId,
                                   TareWeight = s.TareWeight,
                                   LastDriverName = s.LastDriverName,
                                   AssignedDriverId = s.DriverUserId,
                               })
                               .FirstOrDefaultAsync();

        return Ok(haulierVehicleModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateHaulierVehicle(HaulierVehicleModel HaulierVehicleModel)
    {
        var HaulierVehicle = await _db.HaulierVehicles.FirstOrDefaultAsync(s => s.Id == HaulierVehicleModel.Id);

        HaulierVehicle.Registration = HaulierVehicleModel.Registration;
        HaulierVehicle.TareWeight = HaulierVehicleModel.TareWeight;
        HaulierVehicle.HaulierVehicleTypeId = HaulierVehicleModel.HaulierVehicleTypeId;
        HaulierVehicle.LastDriverName = HaulierVehicleModel.LastDriverName;
        HaulierVehicle.DriverUserId = HaulierVehicleModel.AssignedDriverId;
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<HaulierVehicleModel>(HaulierVehicleModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteHaulierVehicle(Guid id)
    {
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> ListHaulierVehicles(Guid haulierId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.HaulierVehicles.Where(w => w.TenantId == _currentUserService.TenantId)
                                   .Where(w => w.HaulierId == haulierId)
                                   .Select(s => new HaulierVehicleListItemModel
                                   {
                                       Id = s.Id,
                                       Registration = s.Registration,
                                       TareWeight = s.TareWeight,
                                       VehicleType = s.HaulierVehicleType.Name,
                                       LastDriver = s.LastDriverName,
                                       AssignedDriverFirstName = s.DriverUser.FirstName,
                                       AssignedDriverSurname = s.DriverUser.Surname,
                                       AssignedDriverEmail = s.DriverUser.Email,
                                   })
                                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListActiveDriversDropdown()
    {
        var q = await _db.Users
            .Where(w => w.TenantId == _currentUserService.TenantId)
            .Where(w => w.UserType == (int)UserType.Driver)
            .Where(w => w.Status == (int)GenericStatus.Active)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = $"{s.FirstName} {s.Surname}"
            })
            .ToListAsync();

        return Ok(q);
    }

    [HttpPost]
    public async Task<IActionResult> ListVehicleTypesDropdown()
    {
        var q = await _db.HaulierVehicleTypes
            .Where(w => w.TenantId == _currentUserService.TenantId)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = s.Name
            })
            .ToListAsync();

        return Ok(q);
    }
    [HttpPost]
    public async Task<IActionResult> ListHaulierVehiclesDropdown(Guid haulierId)
    {
        var q = await _db.HaulierVehicles
            .Where(w => w.TenantId == _currentUserService.TenantId)
            .Where(w => w.HaulierId == haulierId)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = s.Registration,
            })
            .ToListAsync();

        return Ok(q);
    }
}
