using HT.Api.Services.Round;
using HT.Shared.Models;
using HT.Shared.Models.Order;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers.Round;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class RoundController : ControllerBase
{
    private readonly RoundService _roundService;
    private readonly RoundOrderService _roundOrderService;

    public RoundController(RoundService roundService,
        RoundOrderService roundOrderService)
    {
        _roundService = roundService;
        _roundOrderService = roundOrderService;
    }

    [HttpPost]
    public async Task<IActionResult> ListRoundCustomers([FromBody] CustomerRoundListGetModel model)
    {
        return Ok(await _roundService.ListRoundCustomersAsync(model));
    }

    [HttpPost]
    public async Task<IActionResult> ToggleOnStop(Guid customerId)
    {
        return Ok(await _roundService.ToggleOnStopAsync(customerId));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateRoundsStartDate([FromBody] CustomerRoundListItemModel model)
    {
        return Ok(await _roundService.UpdateRoundsStartDateAsync(model));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateCustomerRoundNotes([FromBody] CustomerRoundListItemModel model)
    {
        return Ok(await _roundService.UpdateCustomerRoundNotesAsync(model));
    }

    [HttpPost]
    public async Task<IActionResult> CreateRoundOrder([FromBody] RoundOrderCreateModel createModel)
    {
        return Ok(await _roundOrderService.CreateRoundOrderAsync(createModel));
    }

    [HttpPost]
    public async Task<IActionResult> CreateMissedRoundOrder([FromBody] MissOrderCreateModel createModel)
    {
        return Ok(await _roundService.CreateMissedRoundOrderAsync(createModel));
    }
}
