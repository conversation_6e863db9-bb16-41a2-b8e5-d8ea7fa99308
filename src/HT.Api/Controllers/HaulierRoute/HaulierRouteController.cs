﻿using HT.Api.Services.HaulierRoute;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Shared.Models.HaulierRoute;
using HT.Shared.Models.Route;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class HaulierRouteController(HaulierRouteService haulierRouteService) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> GetRouteCalendar(DateTime weekStartDate)
    {
        return Ok(await haulierRouteService.GetRouteCalendarAsync(weekStartDate));
    }

    [HttpPost]
    public async Task<IActionResult> CreateRoute([FromBody] RouteCreateModel routeCreateModel)
    {
        return Ok(await haulierRouteService.CreateRouteAsync(routeCreateModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetRoute(Guid routeId)
    {
        return Ok(await haulierRouteService.GetRouteAsync(routeId));
    }

    [HttpPost]
    public async Task<IActionResult> ListRoutes([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await haulierRouteService.ListRoutesAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteRoute(Guid id)
    {
        return Ok(await haulierRouteService.DeleteRouteAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateRouteDetails([FromBody] RouteModel routeModel)
    {
        return Ok(await haulierRouteService.UpdateRouteDetailsAsync(routeModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteStopOnRoute(Guid routeLineId)
    {
        return Ok(await haulierRouteService.DeleteStopOnRouteAsync(routeLineId));
    }


    [HttpPost]
    public async Task<IActionResult> MarkStopAsCompleted(Guid stopId, bool createProofOfDelivery)
    {
        return Ok(await haulierRouteService.MarkStopAsCompletedAsync(stopId, createProofOfDelivery));
    }

    [HttpPost]
    public async Task<IActionResult> MarkTipStopAsCompleted(Guid stopId, bool createProofOfDelivery)
    {
        return Ok(await haulierRouteService.MarkTipStopAsCompletedAsync(stopId, createProofOfDelivery));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateRouteLinesOrder([FromBody] HaulierRouteModel routeModel)
    {
        return Ok(await haulierRouteService.UpdateRouteLinesOrder(routeModel));
    }

    [HttpPost]
    public async Task<IActionResult> CreateStopAndAddToRoute([FromBody] HaulierStopModel stopModel)
    {
        return Ok(await haulierRouteService.CreateStopAndAddToRouteAsync(stopModel));
    }

    [HttpPost]
    public async Task<IActionResult> OptimiseRoute(Guid routeId)
    {
        return Ok(await haulierRouteService.OptimiseRoute(routeId));
    }

    [HttpPost]
    public async Task<IActionResult> GetHaulierStop(Guid haulierStopId)
    {
        return Ok(await haulierRouteService.GetHaulierStopAsync(haulierStopId));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateHaulierStop(HaulierStopModel haulierStopModel)
    {
        return Ok(await haulierRouteService.UpdateHaulierStopAsync(haulierStopModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListContractCustomerProducts(Guid contractId)
    {
        return Ok(await haulierRouteService.ListContractCustomerProductsAsync(contractId));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateMobileViewDetails(HaulierRouteLineModel haulierRouteLineModel)
    {
        return Ok(await haulierRouteService.UpdateMobileViewDetailsAsync(haulierRouteLineModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeletePhoto(Guid photoId)
    {
        return Ok(await haulierRouteService.DeletePhoto(photoId));
    }
}
