﻿using HT.Api.Services.HaulierRoute;
using HT.Api.Services.Order;
using HT.Shared.Models.HaulierRoute;
using HT.Shared.Models.Order;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class HaulierRouteProductController(HaulierRouteProductService haulierRouteProductService) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> CreateProductLine(Guid productId, Guid contractLinkId)
    {
        return Ok(await haulierRouteProductService.CreateProductLineAsync(productId, contractLinkId));
    }

}
