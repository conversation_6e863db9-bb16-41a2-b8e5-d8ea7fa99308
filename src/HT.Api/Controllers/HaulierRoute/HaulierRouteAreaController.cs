﻿using HT.Api.Services;
using HT.Api.Services.Common.Address;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Route.Area;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class HaulierRouteAreaController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly AddressService _addressService;

    public HaulierRouteAreaController(DatabaseContext db,
        CurrentUserService currentUserService,
        AddressService addressService
        )
    {
        _db = db;
        _currentUserService = currentUserService;
        _addressService = addressService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateRouteArea([FromBody] RouteAreaCreateModel routeAreaCreateModel)
    {
        routeAreaCreateModel.Id = Guid.NewGuid();

        var routeArea = new Database.RouteArea
        {
            Id = routeAreaCreateModel.Id,
            TenantId = _currentUserService.TenantId,

            Status = (int)RouteAreaStatus.Active,
            Name = routeAreaCreateModel.Name,
            Code = routeAreaCreateModel.Code,
            IsRoundRoute = false,
            StartAddressId = await _addressService.CreateUpdateAddressAsync(routeAreaCreateModel.StartAddress),
            EndAddressId = await _addressService.CreateUpdateAddressAsync(routeAreaCreateModel.EndAddress)
        };

        _db.RouteAreas.Add(routeArea);
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<RouteAreaCreateModel>(routeAreaCreateModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetRouteArea(Guid routeAreaId)
    {
        var orderModel = await _db.RouteAreas
                               .Where(i => i.Id == routeAreaId)
                               .Select(o => new RouteAreaModel
                               {
                                   Id = o.Id,
                                   Status = o.Status,
                                   Code = o.Code,
                                   Name = o.Name,
                                   IsRoundRoute = o.IsRoundRoute,

                                   StartAddressId = o.StartAddressId,
                                   EndAddressId = o.EndAddressId
                               })
                               .FirstOrDefaultAsync();

        orderModel.StartAddress = await _addressService.GetAddressAsync(orderModel.StartAddressId);
        orderModel.EndAddress = await _addressService.GetAddressAsync(orderModel.EndAddressId);

        return Ok(orderModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateRouteArea(RouteAreaModel routeAreaModel)
    {
        var routeAreas = await _db.RouteAreas.FirstOrDefaultAsync(s => s.Id == routeAreaModel.Id);

        routeAreas.Name = routeAreaModel.Name;
        routeAreas.Code = routeAreaModel.Code;
        routeAreas.IsRoundRoute = routeAreaModel.IsRoundRoute;

        routeAreas.StartAddressId = await _addressService.CreateUpdateAddressAsync(routeAreaModel.StartAddress);
        routeAreas.EndAddressId = await _addressService.CreateUpdateAddressAsync(routeAreaModel.EndAddress);

        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<RouteAreaModel>(routeAreaModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteRouteArea(Guid routeAreaId)
    {
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> ListRouteAreas([FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.RouteAreas
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Select(o => new RouteAreaListItemModel
                   {
                       Id = o.Id,
                       Name = o.Name,
                       Code = o.Code,
                       StartAddress = o.StartAddress.AddressPostcode,
                       EndAddress = o.EndAddress.AddressPostcode,
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

}
