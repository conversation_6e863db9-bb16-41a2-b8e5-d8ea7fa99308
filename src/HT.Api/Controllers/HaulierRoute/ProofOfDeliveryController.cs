﻿using HT.Api.Services.Common.PDF;
using HT.Api.Services.HaulierRoute;
using HT.Database;
using HT.Shared.Models.HaulierRoute;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class ProofOfDeliveryController(
    ProofOfDeliveryService proofOfDeliveryService,
    UrlToPdf urlToPDF) : ControllerBase
{
    [AllowAnonymous]
    [HttpPost]
    public async Task<IActionResult> GetProofOfDeliveryAsync(Guid id)
    {
        return Ok(await proofOfDeliveryService.GetProofOfDeliveryAsync(id));
    }

    [AllowAnonymous]
    public async Task<ActionResult> PrintPdf(Guid id, string orderNumber)
    {
        string url = $"https://app-weigh-technology.azurewebsites.net/ProofOfDeliveryPrint/{id}";
        byte[] pdfBytes = await urlToPDF.BuildPdf(url);

        return File(pdfBytes, "application/pdf", $"ProofOfDelivery-{orderNumber}.pdf");
    }

    [HttpPost]
    public async Task<ActionResult<BulkExportResult>> BulkExportPdf([FromBody] ProofOfDeliveryBulkExportModel model)
    {
        if (model.ProofOfDeliveryIds == null || !model.ProofOfDeliveryIds.Any())
        {
            return BadRequest(new BulkExportResult 
            { 
                Success = false, 
                ErrorMessage = "No Proof of Delivery IDs provided" 
            });
        }

        try
        {
            byte[] zipBytes = await proofOfDeliveryService.GenerateBulkPdfZipAsync(model.ProofOfDeliveryIds);
            string fileName = $"ProofOfDelivery-Bulk-{DateTime.Now:yyyyMMdd-HHmmss}.zip";
            
            return Ok(new BulkExportResult
            {
                FileBase64 = Convert.ToBase64String(zipBytes),
                FileName = fileName,
                Success = true
            });
        }
        catch (Exception ex)
        {
            return Ok(new BulkExportResult 
            { 
                Success = false, 
                ErrorMessage = ex.Message 
            });
        }
    }
}
