﻿using HT.Api.Services.HaulierRoute;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class HaulierRouteCopyStopController(HaulierRouteCopyStopService haulierCopyStopService) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> CopyStop(Guid stopId, int copies)
    {
        return Ok(await haulierCopyStopService.CopyStopAsync(stopId, copies));
    }
}
