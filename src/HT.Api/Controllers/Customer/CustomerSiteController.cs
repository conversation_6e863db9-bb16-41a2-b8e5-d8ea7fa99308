using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.Customer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class CustomerSiteController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public CustomerSiteController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> ListCustomerSites(Guid customerId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.CustomerSiteLinks
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.CustomerId == customerId)
                   .Select(s => new CustomerContractListItemModel
                   {
                       Id = s.Id,
                       Name = s.Site.Name,
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListSitesForCustomerDropdown(Guid customerId)
    {
        var q = await _db.Sites
                         .Where(w => w.TenantId == _currentUserService.TenantId)
                         .Select(s => new DropdownListItem
                         {
                             Id = s.Id.ToString(),
                             Name = s.Name
                         })
                         .ToListAsync();

        return Ok(q);
    }

    [HttpPost]
    public async Task<IActionResult> AssignCustomerSite(CustomerAssignSiteModel customerAssignSiteModel)
    {
        var customerSiteLink = new CustomerSiteLink
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            CustomerId = customerAssignSiteModel.CustomerId,
            SiteId = customerAssignSiteModel.SiteId,
        };
        _db.CustomerSiteLinks.Add(customerSiteLink);
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<CustomerAssignSiteModel>(customerAssignSiteModel));
    }


}
