using HT.Api.Services;
using HT.Blazor.Models.Field;
using HT.Database;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class CustomerTagController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public CustomerTagController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> ListCustomerTagsDropdown()
    {
        var q = await _db.CustomerTags.Where(w => w.TenantId == _currentUserService.TenantId)

                                   .Select(s => new DropdownListItem
                                   {
                                       Id = s.Id.ToString(),
                                       Name = s.Name
                                   })
                                   .OrderBy(o => o.Name)
                                   .ToListAsync();

        return Ok(q);
    }


}
