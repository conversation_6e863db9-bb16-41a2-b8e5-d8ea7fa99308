using HT.Api.Services.Customer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers.Customer;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class CustomerSearchController : ControllerBase
{
    private readonly CustomerSearchService _customerSearchService;

    public CustomerSearchController(CustomerSearchService customerSearchService)
    {
        _customerSearchService = customerSearchService;
    }

    [HttpPost]
    public async Task<IActionResult> SearchCustomers(string searchTerm)
    {
        return Ok(await _customerSearchService.SearchAsync(searchTerm));
    }
}
