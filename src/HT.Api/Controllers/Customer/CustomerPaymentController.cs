using HT.Api.Services;
using HT.Api.Services.Customer;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.Customer;
using HT.Shared.Models.Customer.Payment;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class CustomerPaymentController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly CustomerPaymentService _customerPaymentService;

    public CustomerPaymentController(DatabaseContext db, CurrentUserService currentUserService, CustomerPaymentService customerPaymentService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _customerPaymentService = customerPaymentService;
    }

    [HttpPost]
    public async Task<IActionResult> ListCustomerPayments(Guid customerId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.CustomerPayments
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.CustomerId == customerId)
                   .Select(o => new CustomerPaymentListItemModel
                   {
                       Id = o.Id,
                       Date = o.Date,
                       PaymentType = o.PaymentType,
                       Total = o.Total
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListCustomerPaymentsForRoute(Guid routeId)
    {
        return Ok(await _customerPaymentService.ListCustomerPaymentsForRoute(routeId));
    }

    [HttpPost]
    public async Task<IActionResult> AddCustomerPayment([FromBody] AddPaymentModel addPaymentModel)
    {
        return Ok(await _customerPaymentService.AddCustomerPaymentAsync(addPaymentModel));
    }

    [HttpPost]
    public async Task<IActionResult> ViewCustomerPayment(Guid paymentId)
    {
        var payment = await _db.CustomerPayments
                   .Where(w => w.Id == paymentId)
                   .Select(o => new ViewPaymentModel
                   {
                       Id = o.Id,
                       PaymentAmount = o.Total
                   })
                   .FirstOrDefaultAsync();

        return Ok(payment);
    }

    [HttpPost]
    public async Task<IActionResult> DeleteCustomerPayment(Guid paymentId)
    {
        var payment = await _db.CustomerPayments.FirstOrDefaultAsync(cp => cp.Id == paymentId);
        _db.CustomerPayments.Remove(payment);
        await _db.SaveChangesAsync();

        return Ok(payment);
    }
}
