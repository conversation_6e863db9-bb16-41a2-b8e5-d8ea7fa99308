using HT.Api.Services;
using HT.Api.Services.Customer;
using HT.Blazor.Models.Grid;
using HT.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers.Customer;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class CustomerController : ControllerBase
{
    private readonly CustomerService _customerService;

    public CustomerController(CustomerService customerService)
    {
        _customerService = customerService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateCustomer([FromBody] CustomerCreateModel customerCreateModel)
    {
        return Ok(await _customerService.CreateCustomer(customerCreateModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetCustomer(Guid id)
    {
        return Ok(await _customerService.GetCustomer(id));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateCustomer(CustomerModel customerModel)
    {
        return Ok(await _customerService.UpdateCustomer(customerModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteCustomer(Guid id)
    {
        return Ok(await _customerService.DeleteCustomer(id));
    }

    [HttpPost]
    public async Task<IActionResult> ListCustomers([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _customerService.ListCustomers(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListActiveCustomersDropdown()
    {
        return Ok(await _customerService.ListActiveCustomersDropdownAsync());
    }
}
