using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.Customer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class CustomerHaulierController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public CustomerHaulierController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> ListCustomerHauliers(Guid customerId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.HaulierCustomerLinks
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.CustomerId == customerId)
                   .Select(s => new CustomerContractListItemModel
                   {
                       Id = s.Id,
                       Name = s.Haulier.Name,
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }


}
