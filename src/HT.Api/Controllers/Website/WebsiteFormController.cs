﻿using HT.Database;
using HT.Shared.Models.Website.Request;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PostmarkDotNet;

namespace HT.Api.Controllers.Website;

[ApiController]
[Route("[controller]/[action]")]
public class WebsiteFormController : ControllerBase
{
    private readonly DatabaseContext _db;

    public WebsiteFormController(DatabaseContext db)
    {
        _db = db;
    }

    [HttpPost]
    public async Task<IActionResult> FormSubmit([FromBody] WebsiteFormModel getPageModel)
    {
        var website = await _db.WebsiteDomains.FirstOrDefaultAsync(a => a.Domain == getPageModel.Domain);

        if (getPageModel.FormFields.FirstOrDefault(s => s.Key == "FormName").Value == "quote")
        {
            var quote = new WebsiteQuote
            {
                Id = Guid.NewGuid(),
                WebsiteId = website.WebsiteId,
                Created = DateTime.Now,
                Name = getPageModel.FormFields.FirstOrDefault(s => s.Key == "name").Value,
                Email = getPageModel.FormFields.FirstOrDefault(s => s.Key == "email").Value,
                Phone = getPageModel.FormFields.FirstOrDefault(s => s.Key == "phone").Value,
                Message = getPageModel.FormFields.FirstOrDefault(s => s.Key == "message").Value,
            };

            _db.WebsiteQuotes.Add(quote);
            await _db.SaveChangesAsync();

            var message = new PostmarkMessage()
            {
                To = "<EMAIL>",
                From = "<EMAIL>",
                TrackOpens = true,
                Subject = "Quote Request!",
                TextBody = $"Name: {quote.Name}, Email: {quote.Email}, Phone: {quote.Phone}, Message: {quote.Message}, ",
                MessageStream = "outbound",
                Tag = "quote",
            };

            var client = new PostmarkClient("************************************");
            await client.SendMessageAsync(message);
        }

        return Ok(new WebsiteFormModel
        {
            Success = true
        });
    }



}
