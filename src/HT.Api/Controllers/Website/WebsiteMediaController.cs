﻿using HT.Api.Services.Supplier;
using HT.Blazor.Models.Grid;
using HT.Shared.Models.Website.Media.List;
using HT.Shared.Models.Website.Media.View;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers.Website;

[ApiController]
[Route("[controller]/[action]")]
public class WebsiteMediaController : ControllerBase
{
    private readonly WebsiteMediaService _websiteMediaService;

    public WebsiteMediaController(WebsiteMediaService websiteMediaService)
    {
        _websiteMediaService = websiteMediaService;
    }

    [HttpPost]
    public async Task<IActionResult> GetWebsiteMedia(Guid id)
    {
        return Ok(await _websiteMediaService.GetWebsiteMediaAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateWebsiteMedia([FromBody] ViewWebsiteMediaModel viewWebsiteMediaModel)
    {
        return Ok(await _websiteMediaService.UpdateWebsiteMediaAsync(viewWebsiteMediaModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetMediaFolders(Guid? folderId)
    {
        return Ok(await _websiteMediaService.GetMediaFolders(folderId));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteWebsiteMedia([FromBody] ViewWebsiteMediaModel viewWebsiteMediaModel)
    {
        return Ok(await _websiteMediaService.DeleteWebsiteMediaAsync(viewWebsiteMediaModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListWebsiteMedia(Guid? folderId, [FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _websiteMediaService.ListWebsitePagesAsync(folderId, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> CreateMediaFolder(Guid? folderId, [FromBody] WebsiteMediaFolderItemModel websiteMediaFolderItemModel)
    {
        return Ok(await _websiteMediaService.CreateMediaFolder(folderId, websiteMediaFolderItemModel));
    }

    [HttpPost]
    public async Task<IActionResult> UploadMediaFile(Guid? folderId, [FromForm] IFormFile file)
    {
        return Ok(await _websiteMediaService.UploadMediaFile(folderId, file));
    }

}
