﻿using HT.Api.Services.Supplier;
using HT.Shared.Models.Website.Layout;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers.Website;

[ApiController]
[Route("[controller]/[action]")]
public class WebsiteLayoutController : ControllerBase
{
    private readonly WebsiteLayoutService _websiteLayoutService;

    public WebsiteLayoutController(WebsiteLayoutService websiteLayoutService)
    {
        _websiteLayoutService = websiteLayoutService;
    }

    [HttpPost]
    public async Task<IActionResult> GetWebsiteLayout()
    {
        return Ok(await _websiteLayoutService.GetWebsiteLayoutAsync());
    }

    [HttpPost]
    public async Task<IActionResult> UpdateWebsiteLayout(WebsiteLayoutModel websiteLayoutModel)
    {
        return Ok(await _websiteLayoutService.UpdateWebsiteLayoutAsync(websiteLayoutModel));
    }

}
