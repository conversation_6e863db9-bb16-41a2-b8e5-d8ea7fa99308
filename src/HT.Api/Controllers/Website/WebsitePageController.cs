﻿using HT.Api.Services.Supplier;
using HT.Blazor.Models.Grid;
using HT.Shared.Models.Website.Page.Create;
using HT.Shared.Models.Website.Page.List;
using HT.Shared.Models.Website.Page.View;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers.Website;

[ApiController]
[Route("[controller]/[action]")]
public class WebsitePageController : ControllerBase
{
    private readonly WebsitePageService _websitePageService;
    private readonly GenerateWebsitePageService _generateWebsitePageService;

    public WebsitePageController(WebsitePageService websitePageService, GenerateWebsitePageService generateWebsitePageService)
    {
        _websitePageService = websitePageService;
        _generateWebsitePageService = generateWebsitePageService;
    }

    [HttpPost]
    public async Task<IActionResult> GetWebsitePageFolders(Guid? folderId)
    {
        return Ok(await _websitePageService.GetWebsitePageFolders(folderId));
    }

    [HttpPost]
    public async Task<IActionResult> CreateWebsitePageFolder(Guid? folderId, [FromBody] WebsitePageFolderItemModel websitePageFolderItemModel)
    {
        return Ok(await _websitePageService.CreateWebsitePageFolder(folderId, websitePageFolderItemModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListWebsitePages(Guid? folderId, [FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _websitePageService.ListWebsitePagesAsync(folderId, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> CreateWebsitePage([FromBody] WebsitePageCreateModel websitePageCreateModel)
    {
        return Ok(await _websitePageService.CreateWebsitePageAsync(websitePageCreateModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetWebsitePage(Guid id)
    {
        return Ok(await _websitePageService.GetWebsitePageAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateWebsitePage(WebsitePageModel websitePageModel)
    {
        return Ok(await _websitePageService.UpdateWebsitePageAsync(websitePageModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteWebsitePage(Guid websitePageId, bool deleteImages)
    {
        return Ok(await _websitePageService.DeleteWebsitePage(websitePageId, deleteImages));
    }

    [HttpPost]
    public async Task<IActionResult> GenerateWebsitePage([FromBody] WebsitePageCreateModel websitePageCreateModel)
    {
        return Ok(await _generateWebsitePageService.GenerateWebsitePageAsync(websitePageCreateModel));
    }



}
