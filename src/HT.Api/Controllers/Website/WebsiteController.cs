﻿using Azure.Storage.Blobs;
using HT.Api.Common.Extensions;
using HT.Api.Services.Supplier;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Website.Request;
using HT.Shared.Models.Website.Request.Media;
using HT.Shared.Models.Website.Request.Page;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.Website;

[ApiController]
[Route("[controller]/[action]")]
public class WebsiteController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly WebsiteActivityService _websiteActivityService;

    public WebsiteController(DatabaseContext db, WebsiteActivityService websiteActivityService)
    {
        _db = db;
        _websiteActivityService = websiteActivityService;
    }

    [HttpPost]
    public async Task<IActionResult> GetWebsitePage([FromBody] WebsiteRequestModel getPageModel)
    {
        var websiteDomain = await _db.WebsiteDomains.FirstOrDefaultAsync(a => a.Domain == getPageModel.Domain);

        if (websiteDomain == null)
        {
            return this.Ok(new GetWebsitePageResponseModel
            {
                Found = false
            });
        }

        var page = await _db.WebsitePages
            .Where(p => p.WebsiteId == websiteDomain.WebsiteId)
            .Where(w => w.Slug == getPageModel.Slug).Select(s => new GetWebsitePageResponseModel
            {
                Id = s.Id,
                Found = true,
                RedirectUrl = s.RedirectUrl,

                Domain = "https://" + getPageModel.Domain,
                CanonicalUrl = "https://" + getPageModel.Domain + s.Slug,

                GoogleAnalyticsHtml = s.Website.GoogleAnalyticsHtml,
                SiteName = s.Website.Name,
                LogoUrl = s.Website.LogoUrl,
                HeadHtml = s.Website.HeadHtml,
                TopHeaderHtml = s.Website.TopHeaderHtml,
                HeaderHtml = s.Website.HeaderHtml,
                FooterHtml = s.Website.FooterHtml,

                PageTitle = s.Title,
                MetaDescription = s.MetaDescription,
                PageContentHtml = s.ContentHtml,

                Slogan = s.Website.Slogan,
                PrimaryColour = s.Website.PrimaryColour,
                SecondaryColour = s.Website.SecondaryColour,
                EmailAddress = s.Website.EmailAddress,
                PhoneNumber = s.Website.PhoneNumber,

                PageTemplateType = s.PageTemplateType,
                PageThumbnailImageSlug = s.ThumbnailWebsiteMedia.Slug,
                PageAuthor = s.WebsiteAuthor.Name,
                PageAuthorImage = s.WebsiteAuthor.ImageWebsiteMedia.Slug,
                PageCreatedDate = s.CreatedDate,
                PageLastModifiedDate = s.LastModifiedDate,

                Blocks = s.WebsitePageBlocks.OrderBy(o => o.Order).Select(b => new GetWebsitePageResponseBlockModel
                {
                    Id = b.Id,
                    BlockType = b.Type,
                    ContentHtml = b.ContentHtml,
                    PageTitle = s.Title
                }).ToList(),

                InstagramUrl = s.Website.InstagramUrl,
                TikTokUrl = s.Website.TikTokUrl,
                FacebookUrl = s.Website.FacebookUrl,
                YouTubeUrl = s.Website.YouTubeUrl,
                TwitterUrl = s.Website.TwitterUrl,
                PinterestUrl = s.Website.PinterestUrl,
            }).FirstOrDefaultAsync();

        page ??= await _db.WebsitePages
                      .Where(p => p.WebsiteId == websiteDomain.WebsiteId)
                      .Where(w => w.Slug == "/404").Select(s => new GetWebsitePageResponseModel
                      {
                          Id = s.Id,
                          Found = false,
                          RedirectUrl = s.RedirectUrl,

                          GoogleAnalyticsHtml = s.Website.GoogleAnalyticsHtml,
                          SiteName = s.Website.Name,
                          LogoUrl = s.Website.LogoUrl,
                          HeadHtml = s.Website.HeadHtml,
                          TopHeaderHtml = s.Website.TopHeaderHtml,
                          HeaderHtml = s.Website.HeaderHtml,
                          FooterHtml = s.Website.FooterHtml,

                          PageTitle = s.Title,
                          MetaDescription = s.MetaDescription,
                          PageContentHtml = s.ContentHtml,

                          Slogan = s.Website.Slogan,
                          PrimaryColour = s.Website.PrimaryColour,
                          SecondaryColour = s.Website.SecondaryColour,
                          EmailAddress = s.Website.EmailAddress,
                          PhoneNumber = s.Website.PhoneNumber,

                          PageTemplateType = s.PageTemplateType,
                          PageThumbnailImageSlug = s.ThumbnailWebsiteMedia.Slug,
                          PageAuthor = s.WebsiteAuthor.Name,
                          PageAuthorImage = s.WebsiteAuthor.ImageWebsiteMedia.Slug,
                          PageCreatedDate = s.CreatedDate,
                          PageLastModifiedDate = s.LastModifiedDate,


                          Blocks = s.WebsitePageBlocks.OrderBy(o => o.Order).Select(b => new GetWebsitePageResponseBlockModel
                          {
                              Id = b.Id,
                              BlockType = b.Type,
                              ContentHtml = b.ContentHtml,
                          }).ToList(),

                          InstagramUrl = s.Website.InstagramUrl,
                          TikTokUrl = s.Website.TikTokUrl,
                          FacebookUrl = s.Website.FacebookUrl,
                          YouTubeUrl = s.Website.YouTubeUrl,
                          TwitterUrl = s.Website.TwitterUrl,
                          PinterestUrl = s.Website.PinterestUrl,

                      }).FirstOrDefaultAsync();

        foreach (var block in page.Blocks)
        {
            if (block.BlockType == (int)WebsitePageBlockType.ListLatest)
            {
                block.List = await _db.WebsitePages
                                .Where(s => s.WebsiteId == websiteDomain.WebsiteId)
                                .Where(s => s.PageTemplateType == (int)WebsitePageTemplateType.Post)
                                .Where(s => s.Status == (int)WebsitePageStatus.Published)
                                .OrderByDescending(o => o.CreatedDate)
                                .Select(s => new GetWebsitePageResponseBlockListItemModel
                                {
                                    PageName = s.Name,
                                    PageDescription = s.MetaDescription,
                                    PageImage = s.ThumbnailWebsiteMedia != null ? s.ThumbnailWebsiteMedia.Slug : string.Empty,
                                    PageUrl = s.Slug
                                })
                                .Take(10)
                                .ToListAsync();
            }

            if (block.BlockType == (int)WebsitePageBlockType.List)
            {
                var links = await _db.WebsitePageBlockLinks.Where(s => s.WebsitePageBlockId == block.Id).ToListAsync();

                var folderIds = links.Where(w => w.WebsitePageFolderId != null).Select(s => s.WebsitePageFolderId).ToList();

                block.List = await _db.WebsitePages.Where(s => folderIds.Contains(s.WebsitePageFolderId) && s.Status == (int)WebsitePageStatus.Published).Select(s => new GetWebsitePageResponseBlockListItemModel
                {
                    PageName = s.Name,
                    PageDescription = s.MetaDescription,
                    PageImage = s.ThumbnailWebsiteMedia != null ? s.ThumbnailWebsiteMedia.Slug : string.Empty,
                    PageUrl = s.Slug
                }).ToListAsync();
            }

            if (block.BlockType == (int)WebsitePageBlockType.Gallery)
            {
                var links = await _db.WebsitePageBlockLinks.Where(s => s.WebsitePageBlockId == block.Id).ToListAsync();

                var folderIds = links.Where(w => w.WebsiteMediaFolderId != null).Select(s => s.WebsiteMediaFolderId).ToList();

                var folderImages = await _db.WebsiteMedia.Where(s => folderIds.Contains(s.WebsiteMediaFolderId))
                        .Select(s => new GetWebsitePageResponseBlockListItemModel
                        {
                            PageName = s.Slug,
                            PageUrl = s.Slug,
                            Description = s.Description
                        })
                        .ToListAsync();

                block.List = folderImages;

                var getSingleImages = await _db.WebsitePageBlockLinks.Where(s => s.WebsitePageBlockId == block.Id && s.WebsiteMediaId != null)
                                            .Select(s => new GetWebsitePageResponseBlockListItemModel
                                            {
                                                PageName = s.WebsiteMedia.Slug,
                                                PageUrl = s.WebsiteMedia.Slug,
                                                Description = s.WebsiteMedia.Description
                                            })
                                            .ToListAsync();

                block.List.AddRange(getSingleImages);
            }

            page.IncludeYouTubeFacade = page.Blocks.Where(b => b.ContentHtml != null).Any(b => b.ContentHtml.Contains("youtube"));
        }

        await _websiteActivityService.Log(
            websiteDomain.WebsiteId,
            (int)WebsiteActivityType.Page,
            getPageModel.Slug,
            getPageModel.RequestData,
            page.Id,
            null,
            getPageModel.RequestDataModel
        );

        return Ok(page);
    }

    [HttpPost]
    public async Task<IActionResult> GetWebsiteMedia([FromBody] WebsiteRequestModel getImageModel)
    {
        var website = await _db.WebsiteDomains.FirstOrDefaultAsync(a => a.Domain == getImageModel.Domain);

        if (website == null)
        {
            return Ok(new MediaResponseModel { Found = false });
        }

        var media = await _db.WebsiteMedia.Where(wm => wm.WebsiteId == website.WebsiteId)
                                    .Where(wm => wm.Slug == getImageModel.Slug)
                                    .FirstOrDefaultAsync();

        if (media == null)
        {
            return Ok(new MediaResponseModel { Found = false });
        }

        var blobClient = GetBlobClient(media.AzureStoragePath);

        if (!await BlobExists(blobClient))
        {
            return NotFound();
        }

        var imageModel = new MediaResponseModel
        {
            Found = true,
            ImageData = await GetImageData(blobClient),
            ContentType = media.ContentType,
        };

        await _websiteActivityService.Log(
            website.WebsiteId,
            (int)WebsiteActivityType.Media,
            getImageModel.Slug,
            getImageModel.RequestData,
            null,
            media.Id,
            getImageModel.RequestDataModel
        );

        return Ok(imageModel);
    }

    private static BlobClient GetBlobClient(string azureStoragePath)
    {
        string connection = "DefaultEndpointsProtocol=https;AccountName=stheavydutycdn;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
        var blobServiceClient = new BlobServiceClient(connection);
        var containerClient = blobServiceClient.GetBlobContainerClient("website");
        return containerClient.GetBlobClient(azureStoragePath);
    }

    private static async Task<bool> BlobExists(BlobClient blobClient)
    {
        return await blobClient.ExistsAsync();
    }

    private static async Task<byte[]> GetImageData(BlobClient blobClient)
    {
        var response = await blobClient.OpenReadAsync();
        using var memoryStream = new MemoryStream();
        await response.CopyToAsync(memoryStream);
        return memoryStream.ToArray();
    }


    [HttpPost]
    public async Task<IActionResult> GetWebsiteSitemap([FromBody] WebsiteRequestModel getImageModel)
    {
        var website = await _db.WebsiteDomains.FirstOrDefaultAsync(a => a.Domain == getImageModel.Domain);

        var pageUrls = await _db.WebsitePages
           .Where(p => p.WebsiteId == website.WebsiteId)
           .Where(p => p.Status == (int)WebsitePageStatus.Published)
           .Select(s => new PageData
           {
               Url = "https://" + getImageModel.Domain + s.Slug,
               LastModified = s.LastModifiedDate
           })
           .ToListAsync();

        var imageModel = new MediaResponseModel
        {
            Found = true,
            ImageData = SitemapGenerator.GenerateSitemapBytes(pageUrls),
            ContentType = "text/xml",
        };

        await _websiteActivityService.Log(
            website.WebsiteId,
            (int)WebsiteActivityType.Sitemap,
            getImageModel.Slug,
            getImageModel.RequestData,
            null,
            null,
            getImageModel.RequestDataModel
        );

        return Ok(imageModel);
    }

    [HttpPost]
    public async Task<IActionResult> ListWebsiteActivity([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _websiteActivityService.ListWebsiteActivityAsync(gridParametersModel));
    }
}
