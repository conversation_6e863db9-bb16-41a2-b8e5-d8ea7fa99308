﻿using HT.Api.Services.Order;
using HT.Shared.Models.Order;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class OrderLineController : ControllerBase
{
    private readonly OrderLineService _orderLineService;

    public OrderLineController(OrderLineService orderLineService)
    {
        _orderLineService = orderLineService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateOrderLine(Guid orderId)
    {
        return Ok(await _orderLineService.CreateOrderLine(orderId));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateOrderLine([FromBody] OrderLineModel orderLineModel)
    {
        return Ok(await _orderLineService.UpdateOrderLine(orderLineModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteOrderLine([FromBody] OrderLineModel orderLineModel)
    {
        return Ok(await _orderLineService.DeleteOrderLine(orderLineModel));
    }
}
