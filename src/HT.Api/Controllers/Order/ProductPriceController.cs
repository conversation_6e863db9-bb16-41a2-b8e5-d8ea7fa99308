﻿using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.Product;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.Order;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class ProductPriceController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public ProductPriceController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateProductPrice([FromBody] ProductPriceCreateModel productPriceCreateModel)
    {
        _db.ProductPrices.Add(new ProductPrice
        {
            Id = Guid.NewGuid(),
            TenantId = _currentUserService.TenantId,
            ProductId = productPriceCreateModel.ProductId,
            Type = productPriceCreateModel.Type,
            EffectiveDate = productPriceCreateModel.EffectiveDate.Value,
            Price = productPriceCreateModel.Price,
            VATPercent = productPriceCreateModel.VATPercent,
            PricingMethod = productPriceCreateModel.PricingMethod,

            CustomerId = productPriceCreateModel.Type == 2 ? productPriceCreateModel.CustomerId : null,
        });
        await _db.SaveChangesAsync();

        var formResponseModel = new FormResponseModel<ProductPriceCreateModel>(productPriceCreateModel);
        return Ok(formResponseModel);
    }

    [HttpPost]
    public async Task<IActionResult> ListProductPrices(Guid productId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.ProductPrices
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.ProductId == productId)
                   .Where(w => w.Type == 1)
                   .Select(s => new ProductPriceListItemModel
                   {
                       Id = s.Id,
                       PricingMethod = s.PricingMethod,
                       EffectiveDate = s.EffectiveDate,
                       Price = s.Price,
                       VATPercent = s.VATPercent
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListProductPricesCustomer(Guid productId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.ProductPrices
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.ProductId == productId)
                   .Where(w => w.Type == 2)
                   .Select(s => new ProductPriceListItemModel
                   {
                       Id = s.Id,
                       PricingMethod = s.PricingMethod,
                       CustomerName = s.Customer.Name,
                       EffectiveDate = s.EffectiveDate,
                       Price = s.Price,
                       VATPercent = s.VATPercent
                   })
                   .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListCustomersDropdown()
    {
        var q = await _db.Customers.Where(w => w.TenantId == _currentUserService.TenantId)
                                   //.Where(w => w.Status == (int)GenericStatus.Active)
                                   .Select(s => new DropdownListItem
                                   {
                                       Id = s.Id.ToString(),
                                       Name = s.Name
                                   })
                                   .OrderBy(o => o.Name)
                                   .ToListAsync();

        return Ok(q);
    }


}
