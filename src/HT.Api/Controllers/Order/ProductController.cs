﻿using HT.Api.Services;
using HT.Blazor.Models.Grid;
using HT.Shared.Models.Product;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class ProductController : ControllerBase
{
    private readonly ProductService _productService;

    public ProductController(ProductService productService)
    {
        _productService = productService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateProduct([FromBody] ProductCreateModel productCreateModel)
    {
        return Ok(await _productService.CreateProductAsync(productCreateModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetProduct(Guid id)
    {
        return Ok(await _productService.GetProductAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateProduct(ProductModel productModel)
    {
        return Ok(await _productService.UpdateProductAsync(productModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteProduct(Guid id)
    {
        return Ok();
    }


    [HttpPost]
    public async Task<IActionResult> ListProducts([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _productService.ListProductsAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListProductsForCustomerView(Guid customerId, [FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _productService.ListProductsForCustomerViewAsync(customerId, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListProductsForETicket(int productType)
    {
        return Ok(await _productService.ListProductsForETicketAsync(productType));
    }

    [HttpPost]
    public async Task<IActionResult> ListActiveProductsDropdown(int productType)
    {
        return Ok(await _productService.ListActiveProductsDropdownAsync(productType));
    }

    [HttpPost]
    public async Task<IActionResult> ListAllActiveProductsDropdown()
    {
        return Ok(await _productService.ListAllActiveProductsDropdownAsync());
    }

    [HttpPost]
    public async Task<IActionResult> GetProductGroupsSelectList()
    {
        return Ok(await _productService.GetProductGroupsSelectListAsync());
    }

}
