﻿using HT.Api.Services;
using HT.Api.Services.Order;
using HT.Blazor.Models.Grid;
using HT.Shared.Models.Order;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers.Order;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class OrderController(OrderService orderService, PortalEventService portalEventService) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> CreateOrder([FromBody] OrderCreateModel orderCreateModel)
    {
        return Ok(await orderService.CreateOrderAsync(orderCreateModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListOrders([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await orderService.ListOrdersAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetOrder(Guid orderId)
    {
        return Ok(await orderService.GetOrderAsync(orderId));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteOrder(Guid orderId)
    {
        return Ok(await orderService.DeleteOrderAsync(orderId));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateOrderDetails(OrderModel orderModel)
    {
        return Ok(await orderService.UpdateOrderDetailsAsync(orderModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListCustomersDropdown()
    {
        return Ok(await orderService.ListCustomersForOrderDropdownAsync());
    }

    [HttpPost]
    public async Task<IActionResult> ListProductsDropdown()
    {
        return Ok(await orderService.ListProductsDropdownAsync());
    }

    [HttpPost]
    public async Task<IActionResult> MarkOrderComplete(Guid orderId)
    {
        return Ok(await orderService.MarkAsCompletedAsync(orderId));
    }

    [HttpPost]
    public async Task<IActionResult> GetAvailableRoutes(Guid orderId, Guid? routeAreaId, DateTime? date)
    {
        return Ok(await orderService.GetAvailableRoutesAsync(orderId, routeAreaId, date));
    }

    [HttpPost]
    public async Task<IActionResult> GetCustomerFutureOrders(Guid customerId)
    {
        return Ok(await orderService.GetCustomerFutureOrdersAsync(customerId));
    }

    [HttpPost]
    public async Task<IActionResult> GetCustomerOrderSnapshot(Guid customerId)
    {
        return Ok(await orderService.GetCustomerOrderSnapshotAsync(customerId));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateOrderNotes(OrderModel orderModel)
    {
        return Ok(await orderService.UpdateOrderNotesAsync(orderModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListCustomerLedger(Guid customerId, [FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await orderService.ListCustomerLedgerAsync(customerId, gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListOrderEvents(Guid orderId, [FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await portalEventService.ListPortalEventsAsync(orderId, gridParametersModel));
    }
}
