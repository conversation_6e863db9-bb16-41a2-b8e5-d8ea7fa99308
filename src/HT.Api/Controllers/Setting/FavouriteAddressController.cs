using HT.Api.Services.Setting;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.Address;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class FavouriteAddressController(FavouriteAddressService favouriteAddressService) : ControllerBase
{
    [HttpPost]
    public async Task<IActionResult> CreateFavouriteAddress([FromBody] FavouriteAddressModel addressModel)
    {
        return Ok(await favouriteAddressService.CreateFavouriteAddressAsync(addressModel));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateFavouriteAddress(FavouriteAddressModel addressModel)
    {
        return Ok(await favouriteAddressService.UpdateFavouriteAddressAsync(addressModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetFavouriteAddress(Guid id)
    {
        return Ok(await favouriteAddressService.GetFavouriteAddressAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteFavouriteAddress(Guid id)
    {
        return Ok(await favouriteAddressService.DeleteFavouriteAddressAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> ListFavouriteAddresses([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await favouriteAddressService.ListFavouriteAddressesAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListFavouriteAddressesDropdowns()
    {
        return Ok(await favouriteAddressService.ListFavouriteAddressesDropdownsAsync());
    }
}
