using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.VehicleDriver;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class VehicleDriverController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public VehicleDriverController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateVehicleDriver([FromBody] VehicleDriverCreateModel vehicleDriverCreateModel)
    {
        vehicleDriverCreateModel.Id = Guid.NewGuid();

        _db.VehicleDrivers.Add(new VehicleDriver
        {
            Id = vehicleDriverCreateModel.Id,
            TenantId = _currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            FirstName = vehicleDriverCreateModel.FirstName,
            LastName = vehicleDriverCreateModel.LastName,
            Nickname = vehicleDriverCreateModel.Nickname,

        });
        await _db.SaveChangesAsync();

        return Ok(vehicleDriverCreateModel);
    }

    [HttpPost]
    public async Task<IActionResult> GetVehicleDriver(Guid vehicleDriverId)
    {
        var VehicleDriverModel = await _db.VehicleDrivers
                               .Where(i => i.Id == vehicleDriverId)
                               .Select(s => new VehicleDriverModel
                               {
                                   Id = s.Id,
                                   FirstName = s.FirstName,
                                   LastName = s.LastName,
                                   Nickname = s.Nickname,
                               })
                               .FirstOrDefaultAsync();

        return Ok(VehicleDriverModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateVehicleDriver(VehicleDriverModel vehicleDriverModel)
    {
        var VehicleDriver = await _db.VehicleDrivers.FirstOrDefaultAsync(s => s.Id == vehicleDriverModel.Id);
        VehicleDriver.FirstName = vehicleDriverModel.FirstName;
        VehicleDriver.LastName = vehicleDriverModel.LastName;
        VehicleDriver.Nickname = vehicleDriverModel.Nickname;
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<VehicleDriverModel>(vehicleDriverModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteVehicleDriver(Guid id)
    {
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> ListVehicleDrivers(Guid haulierId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.VehicleDrivers.Where(w => w.TenantId == _currentUserService.TenantId)
                             .Select(s => new VehicleDriverListItemModel
                             {
                                 Id = s.Id,
                                 FirstName = s.FirstName,
                                 LastName = s.LastName,
                                 Nickname = s.Nickname,
                             })
                             .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

}
