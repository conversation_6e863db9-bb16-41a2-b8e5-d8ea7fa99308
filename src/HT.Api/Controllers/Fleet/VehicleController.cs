using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Controllers.Fleet;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class VehicleController : ControllerBase
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public VehicleController(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateVehicle([FromBody] VehicleCreateModel vehicleCreateModel)
    {
        vehicleCreateModel.Id = Guid.NewGuid();

        _db.Vehicles.Add(new Vehicle
        {
            Id = vehicleCreateModel.Id,
            TenantId = _currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            Name = vehicleCreateModel.Name,
            Registration = vehicleCreateModel.Registration,
            Capacity = vehicleCreateModel.Capacity,
            Make = vehicleCreateModel.Make,
            Model = vehicleCreateModel.Model
        });
        await _db.SaveChangesAsync();

        return Ok(vehicleCreateModel);
    }

    [HttpPost]
    public async Task<IActionResult> GetVehicle(Guid VehicleId)
    {
        var VehicleModel = await _db.Vehicles
                               .Where(i => i.Id == VehicleId)
                               .Select(s => new VehicleModel
                               {
                                   Id = s.Id,
                                   Name = s.Name,
                                   Registration = s.Registration,
                                   Capacity = s.Capacity,
                                   Make = s.Make,
                                   Model = s.Model
                               })
                               .FirstOrDefaultAsync();

        return Ok(VehicleModel);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateVehicle(VehicleModel VehicleModel)
    {
        var Vehicle = await _db.Vehicles.FirstOrDefaultAsync(s => s.Id == VehicleModel.Id);
        Vehicle.Name = VehicleModel.Name;
        Vehicle.Registration = VehicleModel.Registration;
        Vehicle.Capacity = VehicleModel.Capacity;
        Vehicle.Make = VehicleModel.Make;
        Vehicle.Model = VehicleModel.Model;
        await _db.SaveChangesAsync();

        return Ok(new FormResponseModel<VehicleModel>(VehicleModel));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteVehicle(Guid id)
    {
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> ListVehicles(Guid haulierId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.Vehicles.Where(w => w.TenantId == _currentUserService.TenantId)
                             .Select(s => new VehicleListItemModel
                             {
                                 Id = s.Id,
                                 Name = s.Name,
                                 Registration = s.Registration,
                                 Capacity = s.Capacity,
                                 Make = s.Make,
                                 Model = s.Model
                             })
                             .AsQueryable();

        return Ok(await GridHelper.CreateGridAsync(q, gridParametersModel));
    }

}
