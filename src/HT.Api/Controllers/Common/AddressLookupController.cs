using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class AddressLookupController : ControllerBase
{
    public AddressLookupController()
    {
    }

    [HttpPost]
    public async Task<IActionResult> Find(string searchTerm)
    {
        var service = new GoogleMapsAutocompleteService();
        var response = await service.GetGeocodeLocationAsync(searchTerm);

        return Ok(response);
    }
}
