﻿using HT.Api.Services.Order;
using HT.Api.Services.Round;
using HT.Api.Services.Route;
using HT.Blazor.Models.Grid;
using HT.Shared.Models;
using HT.Shared.Models.Route;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class RouteController : ControllerBase
{
    private readonly RouteService _routeService;
    private readonly OrderService _orderService;

    public RouteController(RouteService routeService, OrderService orderService)
    {
        _routeService = routeService;
        _orderService = orderService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateRoute([FromBody] RouteCreateModel routeCreateModel)
    {
        return Ok(await _routeService.CreateRouteAsync(routeCreateModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetRoute(Guid routeId, bool includeCustomerBalance, bool includeCustomersOwing)
    {
        return Ok(await _routeService.GetRouteAsync(routeId, includeCustomerBalance, includeCustomersOwing));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteRoute(Guid id)
    {
        return Ok(await _routeService.DeleteRouteAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> ListRoutes([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _routeService.ListRoutesAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateCustomerRoundNotes([FromBody] RouteLineModel model)
    {
        return Ok(await _routeService.UpdateCustomerRoundNotesAsync(model));
    }

    [HttpPost]
    public async Task<IActionResult> ListVehicleDropdown()
    {
        return Ok(await _routeService.ListVehicleDropdownAsync());
    }

    [HttpPost]
    public async Task<IActionResult> ListRoundRouteAreaDropdown()
    {
        return Ok(await _routeService.ListRoundRouteAreaDropdownAsync());
    }

    [HttpPost]
    public async Task<IActionResult> ListRouteAreaDropdown()
    {
        return Ok(await _routeService.ListRouteAreaDropdownAsync());
    }

    [HttpPost]
    public async Task<IActionResult> ListOrdersDropdown()
    {
        return Ok(await _routeService.GetOpenOrdersDropdownAsync());
    }

    [HttpPost]
    public async Task<IActionResult> AddOrderToRoute(Guid routeId, Guid orderId)
    {
        return Ok(await _routeService.AddOrderToRouteAsync(routeId, orderId));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteOrderOnRoute(Guid routeLineId)
    {
        return Ok(await _routeService.DeleteOrderOnRouteAsync(routeLineId));
    }

    [HttpPost]
    public async Task<IActionResult> DeleteVisitOnRoute(Guid routeLineId)
    {
        return Ok(await _routeService.DeleteVisitOnRouteAsync(routeLineId));
    }

    [HttpPost]
    public async Task<IActionResult> GetRouteCalendar(DateTime weekStartDate)
    {
        return Ok(await _routeService.GetRouteCalendarAsync(weekStartDate));
    }

    [HttpPost]
    public async Task<IActionResult> ListVehicleDriversDropdown()
    {
        return Ok(await _routeService.ListVehicleDriversDropdown());
    }

    [HttpPost]
    public async Task<IActionResult> UpdateDriverReturnedLine([FromBody] RouteLineModel routeLineModel)
    {
        return Ok(await _routeService.UpdateDriverReturnedLineAsync(routeLineModel));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateRouteDetails([FromBody] RouteModel routeModel)
    {
        return Ok(await _routeService.UpdateRouteDetailsAsync(routeModel));
    }


    [HttpPost]
    public async Task<IActionResult> DriverReturned(Guid id)
    {
        return Ok(await _routeService.DriverReturnedAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> ReOpenRouteFromDriverReturned(Guid id)
    {
        return Ok(await _routeService.ReOpenRouteFromDriverReturnedAsync(id));
    }


    [HttpPost]
    public async Task<IActionResult> CompleteRoute(Guid id)
    {
        return Ok(await _routeService.CompleteRouteAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> GetRouteEditOrder(Guid routeLineId)
    {
        return Ok(await _orderService.GetRouteEditOrderAsync(routeLineId));
    }


    [HttpPost]
    public async Task<IActionResult> UpdateExtraProducts([FromBody] RouteLineProductModel routeLineProductModel)
    {
        return Ok(await _routeService.UpdateExtraProducts(routeLineProductModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetRouteLoadsDatePrint(DateTime dateTime)
    {
        return Ok(await _routeService.GetRouteLoadsDatePrint(dateTime));
    }

    [HttpPost]
    public async Task<IActionResult> GetRouteOwingsDatePrint(DateTime dateTime)
    {
        return Ok(await _routeService.GetRouteOwingsDatePrint(dateTime));
    }

    [HttpPost]
    public async Task<IActionResult> GetRouteDatePrint(DateTime dateTime)
    {
        return Ok(await _routeService.GetRouteDatePrint(dateTime));
    }

    [HttpPost]
    public async Task<IActionResult> GetRouteLoadsPrint(Guid routeId)
    {
        return Ok(await _routeService.GetRouteLoadsPrintAsync(routeId));
    }

    [HttpPost]
    public async Task<IActionResult> GetRouteOwingsPrint(Guid routeId)
    {
        return Ok(await _routeService.GetRouteOwingsPrintAsync(routeId));
    }

    [HttpPost]
    public async Task<IActionResult> GetRoutePrint(Guid routeId)
    {
        return Ok(await _routeService.GetRoutePrintAsync(routeId));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateRouteLinesOrder([FromBody] RouteModel routeModel)
    {
        return Ok(await _routeService.UpdateRouteLinesOrder(routeModel));
    }

    [HttpPost]
    public async Task<IActionResult> OptimiseRoute(Guid routeId)
    {
        return Ok(await _routeService.OptimiseRoute(routeId));
    }

    [HttpPost]
    public async Task<IActionResult> GetRoutesForDate(DateTime date, bool excludeCompleted = false)
    {
        return Ok(await _routeService.GetRoutesForDate(date, excludeCompleted));
    }

    [HttpPost]
    public async Task<IActionResult> RouteMove(RouteMoveModel routeMoveModel)
    {
        return Ok(await _routeService.RouteMove(routeMoveModel));
    }

    [HttpPost]
    public async Task<IActionResult> RouteUpdateAddress(RouteUpdateAddress routeUpdateAddress)
    {
        return Ok(await _routeService.RouteUpdateAddress(routeUpdateAddress));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateRouteCashAmount(RouteCashAmountModel cashAmount)
    {
        return Ok(await _routeService.UpdateRouteCashAmountAsync(cashAmount));
    }

    [HttpPost]
    public async Task<IActionResult> FindRoute(RouteSearchModel searchModel)
    {
        return Ok(await _routeService.FindRouteAsync(searchModel));
    }

    [HttpPost]
    public async Task<IActionResult> AddWillRingsToRoute([FromBody] AddWillRingsRequest request)
    {
        return Ok(await _routeService.AddWillRingsToRouteAsync(request.RouteId, request.TagIds));
    }
}

public class AddWillRingsRequest
{
    public Guid RouteId { get; set; }
    public List<Guid> TagIds { get; set; }
}
