using HT.Blazor.Models.Grid;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HT.Shared.Models;
using HT.Api.Services.Purchase;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class PurchaseController : ControllerBase
{
    private readonly PurchaseService _purchaseService;

    public PurchaseController(PurchaseService purchaseService)
    {
        _purchaseService = purchaseService;
    }

    [HttpPost]
    public async Task<IActionResult> ListPurchases([FromBody] GridParametersModel gridParametersModel, Guid? supplierId)
    {
        return Ok(await _purchaseService.ListPurchases(gridParametersModel, supplierId));
    }

    [HttpPost]
    public async Task<IActionResult> CreatePurchase([FromBody] PurchaseModel purchaseModel)
    {
        return Ok(await _purchaseService.CreatePurchase(purchaseModel));
    }

    [HttpPost]
    public async Task<IActionResult> SavePurchase([FromBody] PurchaseModel purchaseModel)
    {
        return Ok(await _purchaseService.SavePurchase(purchaseModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetPurchase(Guid purchaseId)
    {
        return Ok(await _purchaseService.GetPurchase(purchaseId));
    }


}
