using HT.Api.Services.Supplier;
using HT.Blazor.Models.Grid;
using HT.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HT.Api.Controllers;

[ApiController]
[Authorize]
[Route("[controller]/[action]")]
public class SupplierController : ControllerBase
{
    private readonly SupplierService _supplierService;

    public SupplierController(SupplierService supplierService)
    {
        _supplierService = supplierService;
    }

    [HttpPost]
    public async Task<IActionResult> ListSuppliers([FromBody] GridParametersModel gridParametersModel)
    {
        return Ok(await _supplierService.ListSuppliersAsync(gridParametersModel));
    }

    [HttpPost]
    public async Task<IActionResult> CreateSupplier([FromBody] SupplierCreateModel supplierCreateModel)
    {
        return Ok(await _supplierService.CreateSupplierAsync(supplierCreateModel));
    }

    [HttpPost]
    public async Task<IActionResult> GetSupplier(Guid id)
    {
        return Ok(await _supplierService.GetSupplierAsync(id));
    }

    [HttpPost]
    public async Task<IActionResult> UpdateSupplier(SupplierModel supplierModel)
    {
        return Ok(await _supplierService.UpdateSupplierAsync(supplierModel));
    }

    [HttpPost]
    public async Task<IActionResult> ListSuppliersAssign()
    {
        return Ok(await _supplierService.ListSuppliersAssignAsync());
    }
}
