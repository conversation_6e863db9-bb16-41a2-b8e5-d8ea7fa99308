﻿using HT.Blazor.Models.Form;
using HT.Database;
using Microsoft.EntityFrameworkCore;
using HT.Shared.Models.Website.Layout;

namespace HT.Api.Services.Supplier;

public class WebsiteLayoutService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public WebsiteLayoutService(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    public async Task<WebsiteLayoutModel> GetWebsiteLayoutAsync()
    {
        var websiteLayoutModel = await _db.Websites.Where(w => w.TenantId == _currentUserService.TenantId).Select(w => new WebsiteLayoutModel
        {
            Id = w.Id,
            FooterHtml = w.FooterHtml,
            GoogleAnalyticsHtml = w.GoogleAnalyticsHtml,
            TopHeaderHtml = w.TopHeaderHtml,
            HeaderHtml = w.Header<PERSON>tml,
            HeadHtml = w.HeadHtml,
            SiteName = w.Name,
            LogoUrl = w.LogoUrl,
            Slogan = w.<PERSON>logan,
            PrimaryColour = w.PrimaryColour,
            SecondaryColour = w.SecondaryColour,
            PhoneNumber = w.PhoneNumber,
            EmailAddress = w.EmailAddress,
            InstagramUrl = w.InstagramUrl,
            TikTokUrl = w.TikTokUrl,
            FacebookUrl = w.FacebookUrl,
            YouTubeUrl = w.YouTubeUrl,
            TwitterUrl = w.TwitterUrl,
            PinterestUrl = w.PinterestUrl,
        }).FirstOrDefaultAsync();

        if (websiteLayoutModel != null)
        {
            return websiteLayoutModel;
        }

        var websiteModel = new WebsiteLayoutModel { Id = Guid.NewGuid() };

        _db.Websites.Add(new Website
        {
            Id = websiteModel.Id,
            TenantId = _currentUserService.TenantId,
        });

        await _db.SaveChangesAsync();

        return websiteModel;
    }

    public async Task<FormResponseModel<WebsiteLayoutModel>> UpdateWebsiteLayoutAsync(WebsiteLayoutModel websiteLayoutModel)
    {
        var websiteLayout = await _db.Websites.FirstOrDefaultAsync(w => w.Id == websiteLayoutModel.Id);

        websiteLayout.FooterHtml = websiteLayoutModel.FooterHtml;
        websiteLayout.GoogleAnalyticsHtml = websiteLayoutModel.GoogleAnalyticsHtml;
        websiteLayout.TopHeaderHtml = websiteLayoutModel.TopHeaderHtml;
        websiteLayout.HeaderHtml = websiteLayoutModel.HeaderHtml;
        websiteLayout.HeadHtml = websiteLayoutModel.HeadHtml;
        websiteLayout.Name = websiteLayoutModel.SiteName;
        websiteLayout.LogoUrl = websiteLayoutModel.LogoUrl;

        websiteLayout.Slogan = websiteLayoutModel.Slogan;
        websiteLayout.PrimaryColour = websiteLayoutModel.PrimaryColour;
        websiteLayout.SecondaryColour = websiteLayoutModel.SecondaryColour;
        websiteLayout.PhoneNumber = websiteLayoutModel.PhoneNumber;
        websiteLayout.EmailAddress = websiteLayoutModel.EmailAddress;

        websiteLayout.InstagramUrl = websiteLayoutModel.InstagramUrl;
        websiteLayout.TikTokUrl = websiteLayoutModel.TikTokUrl;
        websiteLayout.FacebookUrl = websiteLayoutModel.FacebookUrl;
        websiteLayout.YouTubeUrl = websiteLayoutModel.YouTubeUrl;
        websiteLayout.TwitterUrl = websiteLayoutModel.TwitterUrl;
        websiteLayout.PinterestUrl = websiteLayoutModel.PinterestUrl;

        await _db.SaveChangesAsync();

        return new FormResponseModel<WebsiteLayoutModel>(websiteLayoutModel);
    }
}
