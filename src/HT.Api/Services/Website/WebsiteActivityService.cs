﻿using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Database;
using Microsoft.AspNetCore.Mvc;
using HT.Shared.Models.Website.Activity;
using WebsiteActivity = HT.Database.WebsiteActivity;
using HT.Shared.Models.Website.Request;

namespace HT.Api.Services.Supplier;

public class WebsiteActivityService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public WebsiteActivityService(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    public async Task Log(Guid websiteId, int type, string slug, string RequestData, Guid? websitePageId, Guid? websiteMediaId, RequestDataModel requestDataModel)
    {
        var websiteActivity = new WebsiteActivity
        {
            Id = Guid.NewGuid(),
            WebsiteId = websiteId,
            DateTime = DateTime.Now,
            Type = type,
            Url = slug,
            RequestData = RequestData,
            WebsitePageId = websitePageId,
            WebsiteMediaId = websiteMediaId,
            IP = requestDataModel.IP,
            Referer = requestDataModel.Referer,
            UserAgent = requestDataModel.UserAgent
        };

        _db.WebsiteActivities.Add(websiteActivity);

        await _db.SaveChangesAsync();

    }

    public async Task<GridResultModel<WebsiteActivityItemModel>> ListWebsiteActivityAsync([FromBody] GridParametersModel gridParametersModel)
    {
        var website = _db.Websites.FirstOrDefault(w => w.TenantId == _currentUserService.TenantId);

        var q = _db.WebsiteActivities
                   .Where(w => w.WebsiteId == website.Id)
                   .Select(s => new WebsiteActivityItemModel
                   {
                       Id = s.Id,
                       WebsiteId = s.WebsiteId,
                       DateTime = s.DateTime,
                       Type = s.Type,
                       Url = s.Url,
                       RequestData = s.RequestData,
                       WebsitePageId = s.WebsitePageId,
                       WebsiteMediaId = s.WebsiteMediaId,
                       IP = s.IP,
                       Referer = s.Referer,
                       UserAgent = s.UserAgent
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

}
