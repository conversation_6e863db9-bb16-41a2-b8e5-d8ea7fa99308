﻿using HT.Database;
using HT.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using HT.Shared.Models.Website.Page.Create;
using HT.Api.Services.Common.OpenAI;
using System.Text.RegularExpressions;

namespace HT.Api.Services.Supplier;

public class GenerateWebsitePageService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public GenerateWebsitePageService(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    public async Task<WebsitePageCreateModel> GenerateWebsitePageAsync(WebsitePageCreateModel websitePageCreateModel)
    {
        var article = await this.GetResponseFromChatbotAsync(websitePageCreateModel.Name);

        var sections = SplitMarkdownSections(article.Article);

        var websiteId = await _db.Websites.Where(w => w.TenantId == _currentUserService.TenantId).FirstOrDefaultAsync();

        var websitePage = new WebsitePage
        {
            Id = Guid.NewGuid(),
            WebsiteId = websiteId.Id,
            WebsitePageFolderId = websitePageCreateModel.FolderId,
            Status = (int)WebsitePageStatus.Draft,
            Type = 0,
            Slug = article.Slug,
            Name = article.Name,
            Title = article.Name,
            MetaDescription = article.MetaDescription,
            LastModifiedDate = DateTime.Now,
            CreatedDate = DateTime.Now,
        };
        _db.WebsitePages.Add(websitePage);

        int count = 1;
        foreach (var section in sections)
        {
            var websiteBlock = new WebsitePageBlock
            {
                Id = Guid.NewGuid(),
                WebsitePageId = websitePage.Id,
                ContentMarkdown = section.Content,
                Type = 2,
                Order = count
            };
            _db.WebsitePageBlocks.Add(websiteBlock);
            count++;
        }

        await _db.SaveChangesAsync();

        websitePageCreateModel.Id = websitePage.Id;

        return websitePageCreateModel;
    }

    public async Task<GenerateArticleModel> GetResponseFromChatbotAsync(string articleName)
    {
        var generateArticleModel = new GenerateArticleModel
        {
            Name = articleName,
        };

        var chat = new ChatApi();

        chat.AppendSystemMessage("Write me a article on the below, include up to three image but none at the top of the article, and include a good description of the image in a alt tag, put the image url in the format /image/IMAGESLUG.webp use dashes in the name of the slug if needed, output this in markdown");
        generateArticleModel.Article = await chat.GetResponseFromChatbotAsync(articleName);
        generateArticleModel.Slug = await chat.GetResponseFromChatbotAsync("now give me a good seo slug url for the article, output this only, in the format /SEOSLUGHERE");
        generateArticleModel.MetaDescription = await chat.GetResponseFromChatbotAsync("now give me a good meta description for this article and don't add quotes");

        return generateArticleModel;
    }

    public static List<MarkdownSection> SplitMarkdownSections(string markdown)
    {
        string pattern = @"(\!\[.*?\]\(.*?\))|([^\!\[]+?(?=\!\[|$))";
        var matches = Regex.Matches(markdown, pattern);

        List<MarkdownSection> sections = [];
        foreach (Match match in matches)
        {
            if (match.Groups[1].Success) // Image section
            {
                sections.Add(new MarkdownSection { Type = "Image", Content = match.Groups[1].Value });
            }
            else if (match.Groups[2].Success) // Text section
            {
                sections.Add(new MarkdownSection { Type = "Text", Content = match.Groups[2].Value.Trim() });
            }
        }

        return sections;
    }

}

public class GenerateArticleModel
{
    public string Name { get; set; }
    public string Article { get; set; }
    public string Slug { get; set; }
    public string MetaDescription { get; set; }
}

public class MarkdownSection
{
    public string Type { get; set; }
    public string Content { get; set; }
}
