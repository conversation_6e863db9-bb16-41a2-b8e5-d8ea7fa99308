﻿using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using Microsoft.AspNetCore.Mvc;
using HT.Blazor.Models.Field;
using Microsoft.EntityFrameworkCore;
using System.Text;
using HT.Shared.Models.Website.Page.List;
using HT.Shared.Models.Website.Page.View;
using HT.Shared.Models.Website.Page.Create;
using HT.Api.Common.Extensions;

namespace HT.Api.Services.Supplier;

public class WebsitePageService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public WebsitePageService(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    public async Task<FormResponseModel<bool>> CreateWebsitePageFolder(Guid? folderId, WebsitePageFolderItemModel websitePageFolderItemModel)
    {
        var website = await _db.Websites.FirstOrDefaultAsync(w => w.TenantId == _currentUserService.TenantId);

        _db.WebsitePageFolders.Add(new WebsitePageFolder
        {
            Id = Guid.NewGuid(),
            WebsiteId = website.Id,
            ParentFolderId = folderId,
            Name = websitePageFolderItemModel.Name,
        });

        await _db.SaveChangesAsync();

        return new FormResponseModel<bool>();
    }

    public async Task<WebsitePageListPageModel> GetWebsitePageFolders(Guid? folderId)
    {
        var folders = await _db.WebsitePageFolders
                         .Where(w => w.Website.TenantId == _currentUserService.TenantId)
                         .Where(w => w.ParentFolderId == folderId)
                         .Select(s => new WebsitePageFolderItemModel
                         {
                             Id = s.Id,
                             Name = s.Name
                         })
                         .OrderBy(o => o.Name)
                         .ToListAsync();

        return new WebsitePageListPageModel
        {
            Folders = folders
        };
    }

    public async Task<GridResultModel<WebsitePageListItemModel>> ListWebsitePagesAsync(Guid? folderId, [FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.WebsitePages
                   .Where(w => w.Website.TenantId == _currentUserService.TenantId)
                   .Where(w => w.WebsitePageFolderId == folderId)
                   .Select(s => new WebsitePageListItemModel
                   {
                       Id = s.Id,
                       Name = s.Name,
                       Slug = s.Slug,
                       Status = s.Status,
                       Title = s.Title,
                       MetaDescription = s.MetaDescription,
                   })
                   .OrderBy(o => o.Name)
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<WebsitePageCreateModel> CreateWebsitePageAsync(WebsitePageCreateModel websitePageCreateModel)
    {
        var websiteId = await _db.Websites.Where(w => w.TenantId == _currentUserService.TenantId).FirstOrDefaultAsync();

        var createWebsitePage = new WebsitePage
        {
            Id = Guid.NewGuid(),
            WebsiteId = websiteId.Id,
            Status = (int)WebsitePageStatus.Draft,
            Name = websitePageCreateModel.Name,
            Slug = websitePageCreateModel.Slug,
            WebsitePageFolderId = websitePageCreateModel.FolderId,
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
        };
        _db.WebsitePages.Add(createWebsitePage);
        await _db.SaveChangesAsync();

        websitePageCreateModel.Id = createWebsitePage.Id;

        return websitePageCreateModel;
    }

    public async Task<WebsitePageModel> GetWebsitePageAsync(Guid id)
    {
        var websitePageModel = await _db.WebsitePages.Where(wp => wp.Id == id).Select(s => new WebsitePageModel
        {
            WebsiteId = s.WebsiteId,

            ThumbnailWebsiteMediaId = s.ThumbnailWebsiteMediaId,

            Id = s.Id,
            Status = s.Status,
            Name = s.Name,
            Title = s.Title,
            MetaDescription = s.MetaDescription,
            Slug = s.Slug,
            PageTemplateType = s.PageTemplateType,

            Blocks = s.WebsitePageBlocks.Select(s => new WebsitePageBlockModel
            {
                Id = s.Id,
                Type = s.Type,
                Order = s.Order,
                ContentHtml = s.ContentHtml,
                ContentMarkdown = s.ContentMarkdown,
                MediaId = s.WebsiteMediaId,
                MediaLinks = s.WebsitePageBlockLinks.Select(wpbl => new WebsitePageBlockMediaLinkModel
                {
                    Type = wpbl.Type,
                    MediaId = wpbl.WebsiteMediaId,
                    WebsiteMediaFolderId = wpbl.WebsiteMediaFolderId,
                    WebsitePageId = wpbl.WebsitePageId,
                    WebsitePageFolderId = wpbl.WebsitePageFolderId,
                }).ToList()
            })
            .OrderBy(o => o.Order)
            .ToList(),

        }).FirstOrDefaultAsync();


        websitePageModel.Media = await _db.WebsiteMedia.Where(w => w.WebsiteId == websitePageModel.WebsiteId).Select(s => new DropdownListItem
        {
            Id = s.Id.ToString(),
            Name = s.Slug + " - " + s.Name,

        }).ToListAsync();

        websitePageModel.WebsitePageFolders = await _db.WebsitePageFolders.Where(w => w.WebsiteId == websitePageModel.WebsiteId).Select(s => new DropdownListItem
        {
            Id = s.Id.ToString(),
            Name = s.Name,

        }).ToListAsync();

        websitePageModel.WebsiteMediaFolders = await _db.WebsiteMediaFolders.Where(w => w.WebsiteId == websitePageModel.WebsiteId).Select(s => new DropdownListItem
        {
            Id = s.Id.ToString(),
            Name = s.Name,

        }).ToListAsync();

        return websitePageModel;
    }

    public async Task<FormResponseModel<WebsitePageModel>> UpdateWebsitePageAsync(WebsitePageModel websitePageModel)
    {
        var websitePage = await _db.WebsitePages
                                   .Include(i => i.WebsitePageBlocks)
                                   .ThenInclude(i => i.WebsitePageBlockLinks)
                                   .Where(wp => wp.Id == websitePageModel.Id)
                                   .FirstOrDefaultAsync();

        websitePage.Status = websitePageModel.Status;
        websitePage.Slug = websitePageModel.Slug;
        websitePage.Name = websitePageModel.Name;
        websitePage.Title = websitePageModel.Title;
        websitePage.MetaDescription = websitePageModel.MetaDescription;
        websitePage.ThumbnailWebsiteMediaId = websitePageModel.ThumbnailWebsiteMediaId;
        websitePage.LastModifiedDate = DateTime.UtcNow;
        websitePage.PageTemplateType = websitePageModel.PageTemplateType;

        _db.WebsitePageBlockLinks.RemoveRange(websitePage.WebsitePageBlocks.SelectMany(s => s.WebsitePageBlockLinks));
        _db.WebsitePageBlocks.RemoveRange(websitePage.WebsitePageBlocks);

        var pageHtml = new StringBuilder();

        foreach (var websitePageBlock in websitePageModel.Blocks)
        {
            var block = new WebsitePageBlock
            {
                Id = Guid.NewGuid(),
                WebsitePageId = websitePageModel.Id,
                Order = websitePageBlock.Order,
                Type = websitePageBlock.Type,
                ContentHtml = websitePageBlock.ContentHtml,
                ContentMarkdown = websitePageBlock.ContentMarkdown,
                WebsiteMediaId = websitePageBlock.MediaId,
            };

            if (websitePageBlock.Type == (int)WebsitePageBlockType.Markdown)
            {
                block.ContentHtml = websitePageBlock.ContentMarkdown.ConvertMarkdownToHtml();
            }

            if (websitePageBlock.Type == (int)WebsitePageBlockType.Gallery)
            {
                foreach (var item in websitePageBlock.MediaLinks.Where(s => s.MediaId != null || s.WebsiteMediaFolderId != null))
                {
                    var blockLink = new WebsitePageBlockLink
                    {
                        Id = Guid.NewGuid(),
                        Type = item.Type,
                        WebsitePageBlockId = block.Id,
                        WebsiteMediaId = item.MediaId,
                        WebsiteMediaFolderId = item.WebsiteMediaFolderId,
                    };
                    _db.WebsitePageBlockLinks.Add(blockLink);
                }
            }

            if (websitePageBlock.Type == (int)WebsitePageBlockType.List)
            {
                foreach (var item in websitePageBlock.MediaLinks.Where(s => s.WebsitePageId != null || s.WebsitePageFolderId != null))
                {
                    var blockLink = new WebsitePageBlockLink
                    {
                        Id = Guid.NewGuid(),
                        Type = item.Type,
                        WebsitePageBlockId = block.Id,
                        WebsitePageId = item.WebsitePageId,
                        WebsitePageFolderId = item.WebsitePageFolderId,
                    };
                    _db.WebsitePageBlockLinks.Add(blockLink);
                }
            }

            pageHtml.Append("<section>");
            pageHtml.Append(block.ContentHtml);
            pageHtml.Append("</section>");

            _db.WebsitePageBlocks.Add(block);
        }

        websitePage.ContentHtml = pageHtml.ToString();

        await _db.SaveChangesAsync();

        return new FormResponseModel<WebsitePageModel>(websitePageModel);
    }

    public async Task<FormResponseModel<bool>> DeleteWebsitePage(Guid websitePageId, bool deleteImages)
    {
        var websitePage = await _db.WebsitePages.Include(x => x.WebsitePageBlocks).FirstOrDefaultAsync(wp => wp.Id == websitePageId);

        _db.WebsitePageBlocks.RemoveRange(websitePage.WebsitePageBlocks);
        _db.WebsitePages.Remove(websitePage);
        await _db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }
}
