﻿using HT.Api.Services.Common.AzureStorage;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using Microsoft.EntityFrameworkCore;
using HT.Shared.Common;
using HT.Shared.Models.Website.Media.List;
using HT.Shared.Models.Website.Media.View;

namespace HT.Api.Services.Supplier;

public class WebsiteMediaService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly AzureStorageService _azureStorageService;

    public WebsiteMediaService(DatabaseContext db, CurrentUserService currentUserService, AzureStorageService azureStorageService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _azureStorageService = azureStorageService;
    }


    public async Task<ViewWebsiteMediaModel> GetWebsiteMediaAsync(Guid id)
    {
        var websitePageModel = await _db.WebsiteMedia.Where(wp => wp.Id == id).Select(s => new ViewWebsiteMediaModel
        {
            Id = s.Id,
            Slug = s.Slug,
            Name = s.Name,
            Description = s.Description,
            AzureStorageURL = s.AzureStorageURL

        }).FirstOrDefaultAsync();


        return websitePageModel;
    }

    public async Task<FormResponseModel<ViewWebsiteMediaModel>> UpdateWebsiteMediaAsync(ViewWebsiteMediaModel viewWebsiteMediaModel)
    {
        var websitePageModel = await _db.WebsiteMedia.FirstOrDefaultAsync(w => w.Id == viewWebsiteMediaModel.Id);

        websitePageModel.Slug = viewWebsiteMediaModel.Slug;
        websitePageModel.Name = viewWebsiteMediaModel.Name;
        websitePageModel.Description = viewWebsiteMediaModel.Description;

        await _db.SaveChangesAsync();

        return new FormResponseModel<ViewWebsiteMediaModel>(viewWebsiteMediaModel);
    }

    public async Task<FormResponseModel<bool>> DeleteWebsiteMediaAsync(ViewWebsiteMediaModel viewWebsiteMediaModel)
    {
        var websitePageModel = await _db.WebsiteMedia.Include(i => i.WebsitePageBlockLinks).FirstOrDefaultAsync(w => w.Id == viewWebsiteMediaModel.Id);

        await _azureStorageService.DeleteBlobAsync("website", websitePageModel.AzureStoragePath);

        _db.WebsitePageBlockLinks.RemoveRange(websitePageModel.WebsitePageBlockLinks);
        _db.WebsiteMedia.Remove(websitePageModel);

        await _db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }


    public async Task<FormResponseModel<bool>> CreateMediaFolder(Guid? folderId, WebsiteMediaFolderItemModel websiteMediaFolderItemModel)
    {
        var website = await _db.Websites.FirstOrDefaultAsync(w => w.TenantId == _currentUserService.TenantId);

        _db.WebsiteMediaFolders.Add(new WebsiteMediaFolder
        {
            Id = Guid.NewGuid(),
            WebsiteId = website.Id,
            ParentFolderId = folderId,
            Name = websiteMediaFolderItemModel.Name,
        });

        await _db.SaveChangesAsync();

        return new FormResponseModel<bool>();
    }

    public async Task<WebsiteMediaListPageModel> GetMediaFolders(Guid? folderId)
    {
        var folders = await _db.WebsiteMediaFolders
                         .Where(w => w.Website.TenantId == _currentUserService.TenantId)
                         .Where(w => w.ParentFolderId == folderId)
                         .Select(s => new WebsiteMediaFolderItemModel
                         {
                             Id = s.Id,
                             Name = s.Name
                         })
                         .ToListAsync();

        return new WebsiteMediaListPageModel
        {
            Folders = folders
        };
    }

    public async Task<GridResultModel<WebsiteMediaListItemModel>> ListWebsitePagesAsync(Guid? folderId, GridParametersModel gridParametersModel)
    {
        var q = _db.WebsiteMedia
                   .Where(w => w.Website.TenantId == _currentUserService.TenantId)
                   .Where(w => w.WebsiteMediaFolderId == folderId)
                   .Select(s => new WebsiteMediaListItemModel
                   {
                       Id = s.Id,
                       Name = s.Name,
                       Slug = s.Slug,
                       Description = s.Description,
                       AzureStorageURL = s.AzureStorageURL
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<FormResponseModel<bool>> UploadMediaFile(Guid? folderId, IFormFile file)
    {
        var website = await _db.Websites.FirstOrDefaultAsync(w => w.TenantId == _currentUserService.TenantId);

        string fileExtension = Path.GetExtension(file.FileName).ToLower();
        var fileType = FileExtensionHelper.GetWebsiteMediaType(fileExtension);

        var media = new WebsiteMedia
        {
            Id = Guid.NewGuid(),
            WebsiteId = website.Id,
            Type = (int)fileType,
            Name = file.FileName.ToLower(),
            ContentType = file.ContentType.ToLower(),
            WebsiteMediaFolderId = folderId,
            FileExtention = fileExtension.ToLower(),
            Slug = $"/{fileType}/{file.FileName}".ToLower()
        };
        _db.WebsiteMedia.Add(media);

        media.AzureStoragePath = (website.Id + "/media/" + media.Id + "/" + file.FileName).ToLower();
        media.AzureStorageURL = await _azureStorageService.UploadFileToBlobAsync(
                                                               "website",
                                                               media.AzureStoragePath,
                                                               file.OpenReadStream(),
                                                               "application/octet-stream");

        await _db.SaveChangesAsync();

        return new FormResponseModel<bool>();
    }
}
