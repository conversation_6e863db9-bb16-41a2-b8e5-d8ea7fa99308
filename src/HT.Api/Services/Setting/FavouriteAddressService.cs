using HT.Api.Services;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Database.Tables;
using HT.Shared.Enums;
using HT.Shared.Models.Address;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Setting;

public class FavouriteAddressService(
    DatabaseContext db,
    CurrentUserService currentUserService)
{
    public async Task<FormResponseModel<Guid>> CreateFavouriteAddressAsync(FavouriteAddressModel addressModel)
    {
        var response = new FormResponseModel<Guid>();

        addressModel.Id = Guid.NewGuid();
        var addressId = Guid.NewGuid();

        db.Add(new FavouriteAddress
        {
            Id = addressModel.Id.Value,
            TenantId = currentUserService.TenantId,
            Name = addressModel.Name,
            AddressId = addressId,
        });

        db.Add(new Address
        {
            Id = addressId,
            TenantId = currentUserService.TenantId,
            AddressLine1 = addressModel.Address.AddressLine1,
            AddressLine2 = addressModel.Address.AddressLine2,
            AddressLine3 = addressModel.Address.AddressLine3,
            AddressLine4 = addressModel.Address.AddressLine4,
            AddressPostcode = addressModel.Address.AddressPostcode,
            Latitude = addressModel.Address.Latitude,
            Longitude = addressModel.Address.Longitude
        });

        await db.SaveChangesAsync();

        response.Data = addressModel.Id.Value;
        return response;
    }

    public async Task<FormResponseModel<Guid>> UpdateFavouriteAddressAsync(FavouriteAddressModel addressModel)
    {
        var response = new FormResponseModel<Guid>();

        var favouriteAddress = await db.FavouriteAddresses
                                      .Include(i => i.Address)
                                      .FirstOrDefaultAsync(f => f.Id == addressModel.Id);

        favouriteAddress.Name = addressModel.Name;
        favouriteAddress.Address.AddressLine1 = addressModel.Address.AddressLine1;
        favouriteAddress.Address.AddressLine2 = addressModel.Address.AddressLine2;
        favouriteAddress.Address.AddressLine3 = addressModel.Address.AddressLine3;
        favouriteAddress.Address.AddressLine4 = addressModel.Address.AddressLine4;
        favouriteAddress.Address.AddressPostcode = addressModel.Address.AddressPostcode;
        favouriteAddress.Address.Latitude = addressModel.Address.Latitude;
        favouriteAddress.Address.Longitude = addressModel.Address.Longitude;

        await db.SaveChangesAsync();

        response.Data = addressModel.Id.Value;
        return response;
    }

    public async Task<FavouriteAddressModel> GetFavouriteAddressAsync(Guid id)
    {
        var favouriteAddressModel = await db.FavouriteAddresses
                                               .Where(i => i.Id == id)
                                               .Select(s => new FavouriteAddressModel
                                               {
                                                   Id = s.Id,
                                                   Name = s.Name,
                                                   Address = new AddressModel(s.Address)
                                               })
                                               .FirstOrDefaultAsync();

        return favouriteAddressModel;
    }

    public async Task<bool> DeleteFavouriteAddressAsync(Guid id)
    {
        var favouriteAddress = await db.FavouriteAddresses
                                .Include(i => i.Address)
                                .FirstOrDefaultAsync(a => a.Id == id);

        db.Addresses.Remove(favouriteAddress.Address);
        db.FavouriteAddresses.Remove(favouriteAddress);

        await db.SaveChangesAsync();

        return true;
    }

    public async Task<GridResultModel<FavouriteAddressModel>> ListFavouriteAddressesAsync(GridParametersModel gridParametersModel)
    {
        var q = db.FavouriteAddresses
                .Where(w => w.TenantId == currentUserService.TenantId)
                .Select(s => new FavouriteAddressModel
                {
                    Id = s.Id,
                    Name = s.Name,
                    Address = new AddressModel
                    {
                        AddressLine1 = s.Address.AddressLine1,
                        AddressLine2 = s.Address.AddressLine2,
                        AddressLine3 = s.Address.AddressLine3,
                        AddressLine4 = s.Address.AddressLine4,
                        AddressPostcode = s.Address.AddressPostcode,
                        Latitude = s.Address.Latitude ?? 0,
                        Longitude = s.Address.Longitude ?? 0
                    }
                })
                .AsQueryable();

            var grid = await GridHelper.CreateGridAsync(q, gridParametersModel);

            return grid;
    }

    public async Task<List<DropdownListItem>> ListFavouriteAddressesDropdownsAsync()
    {
        return await db.FavouriteAddresses
                .Where(w => w.TenantId == currentUserService.TenantId)
                .Select(s => new DropdownListItem
                {
                    Id = s.Id.ToString(),
                    Name = new AddressModel(s.Address).FullAddress,
                })
                .ToListAsync();
    }
}
