﻿using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Haulier;
using HT.Shared.Models.HaulierRoute;
using HT.Shared.Models.HaulierVehicle;
using HT.Shared.Models.User;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.HaulierDriver;

public class HaulierDriverService(
    DatabaseContext db,
    CurrentUserService currentUserService)
{
    public async Task<GridResultModel<HaulierListDriverModel>> ListDriversAsync([FromBody] GridParametersModel gridParametersModel)
    {
        var q = db.Users
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.UserType == (int)UserType.Driver)
                   .Select(s => new HaulierListDriverModel
                   {
                       DriverUserId = s.Id,
                       FirstName = s.FirstName,
                       Surname = s.Surname,
                       Registration = s.HaulierVehicle != null ? s.HaulierVehicle.Registration : string.Empty,
                       HaulierName = s.HaulierVehicle != null ? s.HaulierVehicle.Haulier.Name : string.Empty,
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<List<DropdownListItem>> ListHaulierVehicleDriverDropdownAsync()
    {
        var hauliers = await db.Users
                .Where(w => w.TenantId == currentUserService.TenantId)
                .Where(w => w.UserType == (int)UserType.Driver)
                .Where(w => w.HaulierVehicle != null)
                .Select(s => new DropdownListItem
                {
                    Id = s.Id.ToString(),
                    Name = $"{s.FirstName} {s.Surname} / {s.HaulierVehicle.Haulier.Name} / {s.HaulierVehicle.Registration}",
                })
                .ToListAsync();

        return hauliers;
    }

    public async Task<List<DropdownListItem>> ListHaulierVehicleDropdownAsync()
    {
        var hauliers = await db.HaulierVehicles
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.DriverUserId != null)
                   .Select(s => new DropdownListItem
                   {
                       Id = s.Id.ToString(),
                       Name = $"{s.DriverUser.FirstName} {s.DriverUser.Surname} / {s.Haulier.Name} / {s.Registration}",
                   })
                   .ToListAsync();

        return hauliers;
    }

    /// <summary>
    /// Gets the driver, haulier and vehicle details
    /// </summary>
    /// <param name="driverUserId"></param>
    /// <returns></returns>
    public async Task<HaulierDriverModel> GetDriverDetailsAsync(Guid driverUserId)
    {
        return await db.Users
                        .Where(w => w.Id == driverUserId)
                        .Select(s => new HaulierDriverModel
                        {
                            HaulierVehicleId = s.HaulierVehicle.Id,
                            HaulierVehicleRegistration = s.HaulierVehicle.Registration,
                            HaulierId = s.HaulierVehicle.HaulierId,
                            HaulierName = s.HaulierVehicle.Haulier.Name,
                            FirstName = s.FirstName,
                            LastName = s.Surname,
                            DriverUserId = s.Id,
                        })
                        .FirstAsync();
    }
}
