﻿using HT.Api.Services.Common.Address;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using Microsoft.AspNetCore.Mvc;
using HT.Blazor.Models.Field;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Supplier;

public class SupplierService(DatabaseContext db, CurrentUserService currentUserService, AddressService addressService)
{
    public async Task<GridResultModel<SupplierListItemModel>> ListSuppliersAsync([FromBody] GridParametersModel gridParametersModel)
    {
        var q = db.Suppliers
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Select(s => new SupplierListItemModel
                   {
                       Id = s.Id,
                       Status = s.Status,
                       Code = s.Code,
                       Name = s.Name,
                       TotalAmountOwed = s.Purchases.Where(p => !p.Paid).Sum(s => s.Total)
                   })
                   .AsQueryable();

        var supplierList = await GridHelper.CreateGridAsync(q, gridParametersModel);

        return supplierList;
    }

    public async Task<SupplierCreateModel> CreateSupplierAsync([FromBody] SupplierCreateModel supplierCreateModel)
    {
        supplierCreateModel.Id = Guid.NewGuid();

        db.Suppliers.Add(new Database.Supplier
        {
            Id = supplierCreateModel.Id,
            TenantId = currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            Code = supplierCreateModel.Code,
            Name = supplierCreateModel.Name,
        });

        await db.SaveChangesAsync();

        return supplierCreateModel;
    }

    public async Task<SupplierModel> GetSupplierAsync(Guid id)
    {
        var supplierModel = await db.Suppliers
                               .Where(i => i.Id == id)
                               .Select(s => new SupplierModel
                               {
                                   Id = s.Id,
                                   Status = s.Status,
                                   Code = s.Code,
                                   Name = s.Name,
                                   PhoneNumber = s.PhoneNumber,
                                   MobilePhoneNumber = s.MobilePhoneNumber,
                                   Email = s.Email,
                                   SupplierAddressId = s.SupplierAddressId,
                               })
                               .FirstOrDefaultAsync();

        supplierModel.SupplierAddress = await addressService.GetAddressAsync(supplierModel.SupplierAddressId);

        return supplierModel;
    }

    public async Task<FormResponseModel<SupplierModel>> UpdateSupplierAsync(SupplierModel supplierModel)
    {
        var supplier = await db.Suppliers.FirstOrDefaultAsync(s => s.Id == supplierModel.Id);

        supplier.Status = supplierModel.Status;
        supplier.Code = supplierModel.Code;
        supplier.Name = supplierModel.Name;
        supplier.PhoneNumber = supplierModel.PhoneNumber;
        supplier.MobilePhoneNumber = supplierModel.MobilePhoneNumber;
        supplier.Email = supplierModel.Email;

        supplier.SupplierAddressId = await addressService.CreateUpdateAddressAsync(supplierModel.SupplierAddress);

        await db.SaveChangesAsync();

        return new FormResponseModel<SupplierModel>(supplierModel);
    }

    public async Task<List<DropdownListItem>> ListSuppliersAssignAsync()
    {
        var supplierList = await db.Suppliers
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.Status == (int)GenericStatus.Active)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = s.Code + " - " + s.Name,
            })
            .ToListAsync();

        return supplierList;
    }
}
