﻿using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.Product;
using Microsoft.EntityFrameworkCore;
using HT.Blazor.Models.Form;
using HT.Shared.Enums;
using HT.Blazor.Models.Field;
using HT.Shared.Constants;
using HT.Api.Common.Extensions;

namespace HT.Api.Services;

public class ProductService(DatabaseContext db, CurrentUserService currentUserService, PortalEventService portalEventService)
{
    public async Task<FormResponseModel<ProductCreateModel>> CreateProductAsync(ProductCreateModel productCreateModel)
    {
        productCreateModel.Id = Guid.NewGuid();

        db.Products.Add(new Product
        {
            Id = productCreateModel.Id,
            TenantId = currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            Name = productCreateModel.Name,
            Type = productCreateModel.Type,
            EWCCode = productCreateModel.EWCCode,
            ProductCode = productCreateModel.ProductCode,
            DefaultPrice = productCreateModel.DefaultPrice,
            PricePerKilogram = productCreateModel.PricePerKilogram,
            PricePerLoad = productCreateModel.PricePerLoad,
        });

        await portalEventService.LogAsync(PortalEventType.ProductCreated, productCreateModel.Id);

        await db.SaveChangesAsync();

        var formResponseModel = new FormResponseModel<ProductCreateModel>(productCreateModel);
        return formResponseModel;
    }

    public async Task<ProductModel> GetProductAsync(Guid id)
    {
        var productModel = await db.Products
                              .Where(i => i.Id == id)
                              .Select(s => new ProductModel
                              {
                                  Id = s.Id,
                                  Type = s.Type,
                                  Name = s.Name,
                                  EWCCode = s.EWCCode,
                                  ProductCode = s.ProductCode,
                                  DefaultPrice = s.DefaultPrice,
                                  PricePerLoad = s.PricePerLoad,
                                  PricePerKilogram = s.PricePerKilogram,
                                  ProductWeightKilograms = s.ProductWeightKilograms,
                                  ProductGroupId = s.ProductGroupId,
                                  OperatingProfit = s.OperatingProfit,
                                  Cost = s.Cost,
                              })
                              .FirstOrDefaultAsync();

        return productModel;
    }

    public async Task<FormResponseModel<ProductModel>> UpdateProductAsync(ProductModel productModel)
    {
        var product = await db.Products.FirstOrDefaultAsync(s => s.Id == productModel.Id);
        product.Name = productModel.Name;
        product.DefaultPrice = productModel.DefaultPrice;
        product.Type = productModel.Type;
        product.EWCCode = productModel.EWCCode;
        product.ProductCode = productModel.ProductCode;
        product.PricePerKilogram = productModel.PricePerKilogram;
        product.PricePerLoad = productModel.PricePerLoad;
        product.ProductWeightKilograms = productModel.ProductWeightKilograms;
        product.ProductGroupId = productModel.ProductGroupId;
        product.OperatingProfit = productModel.OperatingProfit;
        product.Cost = productModel.Cost;

        string changes = db.GetChangedProperties(product);
        await portalEventService.LogAsync(PortalEventType.ProductUpdated, product.Id, changes);

        await db.SaveChangesAsync();

        return new FormResponseModel<ProductModel>(productModel);
    }

    public async Task<GridResultModel<ProductListItemModel>> ListProductsAsync(GridParametersModel gridParametersModel)
    {
        var q = db.Products
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Select(s => new ProductListItemModel
                   {
                       Id = s.Id,
                       Name = s.Name,
                       Type = s.Type,
                       ProductCode = s.ProductCode,
                       EWCCode = s.EWCCode,
                       PricePerLoad = s.PricePerLoad,
                       PricePerKilogram = s.PricePerKilogram,
                       ProductWeightKilograms = s.ProductWeightKilograms,
                       DefaultPrice = s.ProductPrices.Any(pp => pp.Type == 1) ? s.ProductPrices.Where(pp => pp.Type == 1)
                                                        .OrderByDescending(pp => pp.EffectiveDate)
                                                        .First()
                                                        .Price
                                                    : 0,
                       DefaultVAT = s.ProductPrices.Any(pp => pp.Type == 1) ? s.ProductPrices.Where(pp => pp.Type == 1)
                                                        .OrderByDescending(pp => pp.EffectiveDate)
                                                        .First()
                                                        .VATPercent
                                                    : 0,
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<GridResultModel<ProductListItemModel>> ListProductsForCustomerViewAsync(Guid customerId, GridParametersModel gridParametersModel)
    {
        var q = db.Products
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Select(s => new ProductListItemModel
                   {
                       Id = s.Id,
                       Name = s.Name,
                       Type = s.Type,
                       ProductCode = s.ProductCode,
                       EWCCode = s.EWCCode,
                       
                       CurrentPriceForCustomer = s.ProductPrices.Any() 
                            ? s.ProductPrices
                                 .Where(w => w.CustomerId == null || w.CustomerId == customerId)
                                 .Where(w => w.EffectiveDate <= DateTime.Now)
                                 .OrderByDescending(o => o.CustomerId)
                                 .ThenByDescending(s => s.EffectiveDate)
                                 .Select(s => new ProductPriceListItemModel
                                 { 
                                    Price = s.Price,
                                     VATPercent = s.VATPercent,
                                     PricingMethod = s.PricingMethod,
                                 })
                                 .FirstOrDefault() ?? new ProductPriceListItemModel()
                             : new ProductPriceListItemModel(),
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<List<ProductListItemModel>> ListProductsForETicketAsync(int productType)
    {
        return await db.Products
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(a => a.Type == productType)
                   .Select(s => new ProductListItemModel
                   {
                       Id = s.Id,
                       Name = s.Name,
                       DefaultPrice = s.DefaultPrice,
                       Type = s.Type,
                   })
                   .ToListAsync();
    }

    public async Task<List<DropdownListItem>> ListActiveProductsDropdownAsync(int productType)
    {
        return await db.Products
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.Type == productType)
            .Where(w => w.Status == (int)GenericStatus.Active)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = s.Name
            })
            .OrderBy(o => o.Name)
            .ToListAsync();
    }

    public async Task<List<DropdownListItem>> ListAllActiveProductsDropdownAsync()
    {
        return await db.Products
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.Status == (int)GenericStatus.Active)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = s.Name
            })
            .OrderBy(o => o.Name)
            .ToListAsync();
    }

    public async Task<List<DropdownListItem>> GetProductGroupsSelectListAsync()
    {
        return await db.ProductGroups
                        .Where(g => g.TenantId == currentUserService.TenantId)
                        .Select(s => new DropdownListItem
                        {
                            Id = s.Id.ToString(),
                            Name = s.Name,
                        })
                        .OrderBy(o => o.Name)
                        .ToListAsync();
    }
}
