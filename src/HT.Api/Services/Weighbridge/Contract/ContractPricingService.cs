﻿using HT.Api.Services.ETicket;
using HT.Database;
using HT.Shared.Models.ETicket;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services;

public class ContractPricingService
{
    private readonly DatabaseContext _db;
    private readonly ETicketPricingService _eTicketPricingService;

    public ContractPricingService(DatabaseContext databaseContext, ETicketPricingService eTicketPricingService)
    {
        _db = databaseContext;
        _eTicketPricingService = eTicketPricingService;
    }

    public async Task UpdateContractETicketsPricing(Guid contractId)
    {
        var etickets = await _db.ETickets.Where(w => w.ContractId == contractId)
                                         .Where(w => w.Status == (int)ETicketStatus.Saved || w.Status == (int)ETicketStatus.Closed)
                                         .Select(s => s.Id)
                                         .ToListAsync();

        foreach (var eticket in etickets)
        {
            await _eTicketPricingService.UpdatePriceAsync(eticket);
        }
    }


}
