﻿using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using HT.Shared.Models.ETicket;
using HT.Shared.Models.User;

namespace HT.Api.Services;

public class ContractService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public ContractService(DatabaseContext databaseContext,
        CurrentUserService currentUserService)
    {
        _db = databaseContext;
        _currentUserService = currentUserService;
    }

    public async Task<GridResultModel<ContractListItemModel>> ListContracts(GridParametersModel gridParametersModel)
    {
        var q = _db.Contracts
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.Status != (int)GenericStatus.Deleted)
                   .Select(s => new ContractListItemModel
                   {
                       Id = s.Id,
                       Status = s.Status,
                       ContractNumber = s.ContractNumber,
                       Name = s.Name,
                       RemainingAllowance = s.RemainingAllowance,
                       IsHaulierContract = s.IsHaulierContract,
                       TotalValue = s.ETickets.Where(e => e.Status == (int)ETicketStatus.Saved
                                                    || e.Status == (int)ETicketStatus.Closed
                                                    || e.Status == (int)ETicketStatus.Invoiced)
                                              .Sum(s => s.Total),
                       Visits = s.ETickets.Count(e => e.Status == (int)ETicketStatus.Saved
                                                    || e.Status == (int)ETicketStatus.Closed
                                                    || e.Status == (int)ETicketStatus.Invoiced),
                       SiteId = s.SitePermit != null ? s.SitePermit.SiteId : Guid.Empty
                   })
                   .AsQueryable();

        var userSecurity = await _currentUserService.GetUserSecurityAsync();

        if (userSecurity.UserType is not ((int)UserType.SuperUser) and not ((int)UserType.Admin))
        {
            q = q.Where(s => userSecurity.Sites.Contains(s.SiteId));
        }

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }
}
