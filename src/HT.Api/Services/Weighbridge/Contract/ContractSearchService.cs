﻿using System.Linq;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Contract;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Weighbridge.Contract;

public class ContractSearchService(DatabaseContext db, CurrentUserService currentUserService)
{
    public async Task<List<ContractSearchItemModel>> SearchHaulierContractAsync(string searchTerm)
    {
        var q = await db.Contracts
                         .Where(w => w.TenantId == currentUserService.TenantId)
                         .Where(w => w.Status == (int)GenericStatus.Active)
                         .Where(w => w.IsHaulierContract)
                         .Where(w =>
                            w.Name.Contains(searchTerm) ||
                            w.ContractNumber.ToString().Contains(searchTerm) ||
                            w.AddressLine1.Contains(searchTerm) ||
                            w.AddressLine2.Contains(searchTerm) ||
                            w.AddressLine3.Contains(searchTerm) ||
                            w.AddressLine4.Contains(searchTerm) ||
                            w.AddressPostcode.Contains(searchTerm)
                         )
                         .Select(s => new ContractSearchItemModel
                         {
                             Id = s.Id,
                             Name = s.Name,
                             ContractNumber = s.ContractNumber,
                             AddressLine1 = s.AddressLine1,
                             AddressLine2 = s.AddressLine2,
                             AddressLine3 = s.AddressLine3,
                             AddressLine4 = s.AddressLine4,
                             AddressPostcode = s.AddressPostcode,
                             Longitude = s.AddressLongitude,
                             Latitude = s.AddressLatitude,
                             TipAddress = s.TipAddressId != null
                                ? new Shared.Models.Address.AddressModel(s.TipAddress)
                                : null,
                         })
                         .OrderBy(o => o.Name)
                         .Take(10)
                         .ToListAsync();

        return q;
    }

}
