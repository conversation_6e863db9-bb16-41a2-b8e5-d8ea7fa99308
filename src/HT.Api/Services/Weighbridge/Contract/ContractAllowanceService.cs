﻿using HT.Database;
using HT.Shared.Models;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services;

public class ContractAllowanceService
{
    private readonly DatabaseContext _db;

    public ContractAllowanceService(DatabaseContext databaseContext)
    {
        _db = databaseContext;
    }

    public async Task<ContractAllowanceOverallModel> GetContractAllowanceOverallAsync(Guid contractId)
    {
        decimal totalTippedKilograms = await _db.ETickets.Where(s => s.ContractId == contractId).SumAsync(s => s.NetWeight);
        decimal totalAllowance = await _db.ContractAllowances.Where(s => s.ContractId == contractId).SumAsync(s => s.AllowanceAmountTonnes);
        decimal contractOverallAllowance = (await _db.Contracts.FirstOrDefaultAsync(s => s.Id == contractId))?.OverallTotalAllowance ?? 0.0m;

        var contractAllowanceOverallModel = new ContractAllowanceOverallModel
        {
            TotalTipped = Math.Round(totalTippedKilograms / 1000, 2),
            TotalAllowance = Math.Round(totalAllowance, 2),
            OverallTotalAllowance = Math.Round(contractOverallAllowance, 2),
        };

        contractAllowanceOverallModel.TotalRemaining = Math.Round(contractAllowanceOverallModel.TotalAllowance - contractAllowanceOverallModel.TotalTipped, 2);
        contractAllowanceOverallModel.OverallRemainingTotalAllowance = Math.Round(contractAllowanceOverallModel.OverallTotalAllowance - contractAllowanceOverallModel.TotalTipped, 2);

        return contractAllowanceOverallModel;
    }

    public async Task UpdateContractAllowanceAsync(Guid contractId)
    {
        var contract = await _db.Contracts.FirstOrDefaultAsync(s => s.Id == contractId);
        var contractAllowance = await this.GetContractAllowanceOverallAsync(contractId);

        contract.RemainingAllowance = contractAllowance.TotalRemaining;

        await _db.SaveChangesAsync();
    }


}
