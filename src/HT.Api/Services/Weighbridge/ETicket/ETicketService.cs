﻿using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using HT.Shared.Models.ETicket;
using HT.Shared.Models.HaulierRoute;
using HT.Shared.Models.Product;
using HT.Shared.Models.User;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.ETicket;

public class ETicketService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly ContractAllowanceService _contractAllowanceService;
    private readonly ETicketPricingService _eTicketPricingService;

    public ETicketService(DatabaseContext databaseContext,
        CurrentUserService currentUserService,
        ContractAllowanceService contractAllowanceService,
        ETicketPricingService eTicketPricingService)
    {
        _db = databaseContext;
        _currentUserService = currentUserService;
        _contractAllowanceService = contractAllowanceService;
        _eTicketPricingService = eTicketPricingService;
    }

    public async Task<CreateETicketModel> CreateETicketAsync(CreateETicketModel createETicketModel)
    {
        createETicketModel.Id = Guid.NewGuid();

        string siteCode = (await _db.Sites.FirstOrDefaultAsync(s => s.Id == createETicketModel.SiteId)).Code;

        var lastTicket = await _db.ETickets.Where(s => s.SiteId == createETicketModel.SiteId)
                                            .OrderByDescending(s => s.TicketNumber)
                                            .FirstOrDefaultAsync();

        int ticketNumber = lastTicket == null ? 1 : lastTicket.TicketNumber + 1;

        var haulierVehicle = await _db.HaulierVehicles.FirstOrDefaultAsync(s => s.Id == createETicketModel.HaulierVehicleId);

        _db.ETickets.Add(new Database.ETicket
        {
            Id = createETicketModel.Id,
            TenantId = _currentUserService.TenantId,

            Type = (int)ETicketType.Waste,

            SiteId = createETicketModel.SiteId!.Value,
            CustomerId = createETicketModel.CustomerId!.Value,
            HaulierVehicleId = createETicketModel.HaulierVehicleId!.Value,
            Status = (int)GenericStatus.Active,

            TicketNumber = ticketNumber,
            SiteETicketNumber = siteCode + "-" + ticketNumber,

            EntryDateTime = DateTime.Now,
            ExitDateTime = DateTime.Now.AddMinutes(10),
            ExitWeight = haulierVehicle.TareWeight,
            DriverName = haulierVehicle.LastDriverName
        });
        await _db.SaveChangesAsync();

        return createETicketModel;
    }

    public async Task<GridResultModel<ETicketListItemModel>> ListETicketsAsync(GridParametersModel gridParametersModel, Guid? contractId, Guid? customerId)
    {
        var q = _db.ETickets
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .AsQueryable();

        if (contractId != null)
        {
            q = q.Where(w => w.ContractId == contractId);
        }

        if (customerId != null)
        {
            q = q.Where(w => w.CustomerId == customerId);
        }

        var query = q.Select(s => new ETicketListItemModel
        {
            Id = s.Id,
            Status = s.Status,
            TicketNumber = s.TicketNumber,
            SiteETicketNumber = s.SiteETicketNumber,
            Registration = s.HaulierVehicle.Registration,
            Customer = s.Customer.Name,
            OrderNumber = s.OrderNumber,
            EntryDateTime = s.EntryDateTime,
            ExitDateTime = s.ExitDateTime,
            SiteId = s.SiteId,
            SiteName = s.Site.Name,
            NetWeight = s.NetWeight.ToString(),
            ContractName = s.Contract.Name,
            IsTotalZero = s.Total == 0,
            Type = s.Type,
            DocumentCount = s.Documents.Count,

            EWCCode = s.Customer != null && s.Contract.SitePermitProductLink != null && s.Contract.SitePermitProductLink.Product != null ? s.Contract.SitePermitProductLink.Product.EWCCode : "",
            EWCCodeName = s.Customer != null && s.Contract.SitePermitProductLink != null && s.Contract.SitePermitProductLink.Product != null ? s.Contract.SitePermitProductLink.Product.Name : "",
            ProductName = s.Product.Name
        })
        .OrderByDescending(o => o.EntryDateTime)
        .AsQueryable();

        var userSecurity = await _currentUserService.GetUserSecurityAsync();

        if (userSecurity.UserType is not ((int)UserType.SuperUser) and not ((int)UserType.Admin))
        {
            query = query.Where(s => userSecurity.Sites.Contains(s.SiteId));
        }

        return await GridHelper.CreateGridAsync(query, gridParametersModel);
    }

    public async Task<ETicketModel> GetETicketAsync(Guid id)
    {
        var ETicketModel = await _db.ETickets
                               .Where(i => i.Id == id)
                               .Select(s => new ETicketModel
                               {
                                   ReadOnly = s.Status == (int)ETicketStatus.Closed || s.Status == (int)ETicketStatus.Void || s.Status == (int)ETicketStatus.Invoiced,

                                   Status = s.Status,

                                   TicketNumber = s.TicketNumber,
                                   SiteETicketNumber = s.SiteETicketNumber,

                                   Id = s.Id,
                                   Registration = s.HaulierVehicle.Registration,
                                   CarrierLicence = s.HaulierVehicle.Haulier.CarrierLicence,
                                   VehicleType = s.HaulierVehicle.HaulierVehicleType.Name,
                                   Customer = s.Customer.Name,

                                   SiteId = s.SiteId,
                                   SiteName = s.Site.Name,

                                   HasSiteSurcharge = s.HasSiteSurcharge,
                                   SiteSurchargeId = s.SiteSurchargeId,

                                   CustomerId = s.Customer.Id,
                                   ContractId = s.ContractId,
                                   OrderNo = s.OrderNumber,

                                   EntryWeight = s.EntryWeight,
                                   EntryDateTime = s.EntryDateTime,

                                   ExitWeight = s.ExitWeight,
                                   ExitDateTime = s.ExitDateTime,

                                   NetWeight = s.NetWeight,


                                   SurchargeReason = s.SurchargeReason,

                                   DriverName = s.DriverName,
                                   TransferNote = s.TransferNote,
                                   Remarks = s.Remarks,
                                   HazardousRemarks = s.HazardousRemarks,

                                   Type = s.Type,
                                   Tonnes = s.Tonnes,
                                   ProductId = s.ProductId,

                                   Documents = s.Documents.Select(s => new ETicketDocumentModel
                                   {
                                       Id = s.Id,
                                       Url = s.AzureStorageURL,
                                       TimeStamp = s.UploadedDate
                                   }).ToList(),
                               })
                               .FirstOrDefaultAsync();

        return ETicketModel;
    }

    public async Task<ETicketPrintModel> GetPrintETicketAsync(Guid id)
    {
        var ETicketModel = await _db.ETickets
                               .Where(i => i.Id == id)
                               .Select(s => new ETicketPrintModel
                               {
                                   TicketNumber = s.TicketNumber,
                                   SiteETicketNumber = s.SiteETicketNumber,
                                   Type = s.Type,
                                   ProductTypeName = s.Product.Name,

                                   Registration = s.HaulierVehicle.Registration,
                                   CarrierLicence = s.HaulierVehicle.Haulier.CarrierLicence,
                                   VehicleType = s.HaulierVehicle.HaulierVehicleType.Name,

                                   Customer = s.Customer.Name,
                                   SiteName = s.Site.Name,
                                   OrderNo = s.OrderNumber,

                                   HaulierName = s.HaulierVehicle.Haulier.Name,
                                   PermitLicenceNumber = s.Contract.SitePermit.LicenceNumber,
                                   PermitName = s.Contract.SitePermit.Name,
                                   ContractNumber = s.Contract.ContractNumber,
                                   OrderNumber = s.OrderNumber,
                                   ContractName = s.Contract.Name,
                                   ContractBorough = s.Contract.Borough,
                                   ProductName = s.Contract.SitePermitProductLink.Product.Name + " - " + s.Contract.SitePermitProductLink.Product.EWCCode,

                                   EntryWeight = s.EntryWeight,
                                   EntryDateTime = s.EntryDateTime,
                                   ExitWeight = s.ExitWeight,
                                   ExitDateTime = s.ExitDateTime,

                                   NetWeight = s.NetWeight,


                                   DriverName = s.DriverName,
                                   TransferNote = s.TransferNote,
                                   Remarks = s.Remarks,
                                   HazardousRemarks = s.HazardousRemarks,

                                   ContractSiteAddress = s.Contract.AddressLine1 + " " + s.Contract.AddressLine2 + " " + s.Contract.AddressLine3 + " " + s.Contract.AddressLine4 + " " + s.Contract.AddressPostcode
                               })
                               .FirstOrDefaultAsync();

        return ETicketModel;
    }

    public async Task<FormResponseModel<ETicketModel>> UpdateETicketAsync(ETicketModel eTicketModel)
    {
        var response = new FormResponseModel<ETicketModel>(eTicketModel);

        var eticket = await _db.ETickets
                    .Include(i => i.HaulierVehicle)
                    .Include(i => i.Contract)
                    .FirstOrDefaultAsync(s => s.Id == eTicketModel.Id);

        // Status
        eticket.Status = (int)ETicketStatus.Saved;
        eticket.Type = eTicketModel.Type;
        eticket.Tonnes = eTicketModel.Tonnes;
        eticket.ProductId = eTicketModel.ProductId;

        // Driver Notes
        eticket.DriverName = eTicketModel.DriverName;
        eticket.TransferNote = eTicketModel.TransferNote;

        // Weights & Times
        eticket.EntryWeight = eTicketModel.EntryWeight;
        eticket.EntryDateTime = eTicketModel.EntryDateTime;
        eticket.ExitWeight = eTicketModel.ExitWeight;
        eticket.ExitDateTime = eTicketModel.ExitDateTime;
        eticket.NetWeight = eTicketModel.NetWeight;

        // Product - Price
        eticket.ContractId = eTicketModel.ContractId;
        eticket.OrderNumber = eTicketModel.OrderNo;

        // Surcharge
        if (eTicketModel.Type == (int)ETicketType.Waste)
        {
            eticket.HasSiteSurcharge = eTicketModel.HasSiteSurcharge;
            eticket.SiteSurchargeId = eTicketModel.HasSiteSurcharge ? eTicketModel.SiteSurchargeId : null;
        }
        else
        {
            eticket.HasSiteSurcharge = false;
            eticket.SiteSurchargeId = null;
        }

        // Remarks
        eticket.Remarks = eTicketModel.Remarks;
        eticket.HazardousRemarks = eTicketModel.HazardousRemarks;

        // Last Driver
        eticket.HaulierVehicle.LastDriverName = eTicketModel.DriverName;

        if (eTicketModel.UpdateContractOrderNo)
        {
            eticket.Contract.OrderNumber = eTicketModel.OrderNo;
        }

        await _db.SaveChangesAsync();

        await _eTicketPricingService.UpdatePriceAsync(eticket.Id);

        if (eTicketModel.ContractId != null && eticket.Type == (int)ETicketType.Waste)
        {
            await _contractAllowanceService.UpdateContractAllowanceAsync(eTicketModel.ContractId.Value);
        }

        return response;
    }

    public async Task<FormResponseModel<ETicketModel>> VoidETicketAsync(ETicketModel eTicketModel)
    {
        var response = new FormResponseModel<ETicketModel>(eTicketModel);

        var ETicket = await _db.ETickets.FirstOrDefaultAsync(s => s.Id == eTicketModel.Id);
        ETicket.Status = (int)ETicketStatus.Void;
        await _db.SaveChangesAsync();

        return response;
    }

    public async Task<FormResponseModel<ETicketModel>> CloseETicketAsync(ETicketModel eTicketModel)
    {
        var response = new FormResponseModel<ETicketModel>(eTicketModel);

        var ETicket = await _db.ETickets.FirstOrDefaultAsync(s => s.Id == eTicketModel.Id);
        ETicket.Status = (int)ETicketStatus.Closed;
        await _db.SaveChangesAsync();

        return response;
    }

    public async Task<FormResponseModel<string>> BulkCloseETicketsAsync(ETicketBulkCloseModel eTicketBulkCloseModel)
    {
        var q = _db.ETickets
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Where(w => w.SiteId == eTicketBulkCloseModel.SiteId)
                   .Where(s => s.Status == (int)ETicketStatus.Saved)
                   .AsQueryable();

        if (eTicketBulkCloseModel.GenerateFor == 2)
        {
            q = q.Where(w => w.EntryDateTime.Value.Date >= eTicketBulkCloseModel.StartDate.Value.Date && w.EntryDateTime.Value.Date <= eTicketBulkCloseModel.EndDate.Value.Date);
        }

        var response = new FormResponseModel<string>();

        foreach (var eticket in await q.ToListAsync())
        {
            eticket.Status = (int)ETicketStatus.Closed;
        }
        await _db.SaveChangesAsync();

        return response;
    }

    public async Task<FormResponseModel<ETicketModel>> ReOpenTicketAsync(ETicketModel eTicketModel)
    {
        await _eTicketPricingService.UpdatePriceAsync(eTicketModel.Id);

        var response = new FormResponseModel<ETicketModel>(eTicketModel);

        var ETicket = await _db.ETickets.FirstOrDefaultAsync(s => s.Id == eTicketModel.Id);
        ETicket.Status = (int)ETicketStatus.Saved;
        await _db.SaveChangesAsync();

        return response;
    }

    public async Task<List<ETicketCreateCustomerModel>> GetCustomersFromVehicleAsync(Guid id)
    {
        var eticketCreateCustomerModel = await _db.HaulierVehicles
                               .Where(i => i.Id == id)
                               .SelectMany(s => s.Haulier.HaulierCustomerLinks)
                               .Select(s => new ETicketCreateCustomerModel
                               {
                                   Id = s.Customer.Id,
                                   Name = s.Customer.Name,
                                   OnStop = s.Customer.OnStop,
                               })
                               .ToListAsync();

        return eticketCreateCustomerModel;
    }

    public async Task<List<DropdownListItem>> ListVehiclesDropdownAsync()
    {
        var vehicles = await _db.HaulierVehicles.Where(w => w.TenantId == _currentUserService.TenantId)
                                   .Where(w => w.Haulier.Status == (int)GenericStatus.Active)
                                   .Select(s => new DropdownListItem
                                   {
                                       Id = s.Id.ToString(),
                                       Name = s.Registration + " - " + s.Haulier.Name
                                   })
                                   .OrderBy(o => o.Name)
                                   .ToListAsync();

        return vehicles;
    }

    public async Task<ETicketContractModel> GetETicketContractModelAsync(Guid contractId)
    {
        var ETicketModel = await _db.Contracts
                               .Where(i => i.Id == contractId)
                               .Select(s => new ETicketContractModel
                               {
                                   //Borough = s.B
                                   ContractNumber = s.ContractNumber,
                                   OrderNumber = s.OrderNumber,
                                   PermitLicenceNumber = s.SitePermitProductLink.SitePermit.LicenceNumber,
                                   ProductName = s.SitePermitProductLink.Product.EWCCode + " " + s.SitePermitProductLink.Product.Name,
                                   SiteAddress = s.AddressLine1 + " " + s.AddressLine2 + " " + s.AddressLine3 + " " + s.AddressLine4 + " " + s.AddressPostcode,
                                   Borough = s.Borough,
                                   RemainingAllowance = s.RemainingAllowance
                               })
                               .FirstOrDefaultAsync();

        return ETicketModel;
    }

    public async Task<List<DropdownListItem>> ListSurchargesDropdownAsync(Guid siteId)
    {
        var surcharges = await _db.SiteSurcharges.Where(w => w.TenantId == _currentUserService.TenantId)
                                   .Where(w => w.SiteId == siteId)
                                   .Select(s => new DropdownListItem
                                   {
                                       Id = s.Id.ToString(),
                                       Name = s.Name
                                   })
                                   .ToListAsync();

        return surcharges;
    }

    public async Task<List<DropdownListItem>> ListProductsDropdownAsync()
    {
        var q = await _db.Products
                         .Where(w => w.TenantId == _currentUserService.TenantId && w.Type == (int)ProductType.Product)
                         .Select(s => new DropdownListItem { Id = s.Id.ToString(), Name = s.Name })
                         .OrderBy(o => o.Name)
                         .ToListAsync();

        return q;
    }
}
