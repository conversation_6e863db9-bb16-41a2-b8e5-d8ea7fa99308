﻿using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Order;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.ETicket;

public class ETicketPricingService
{
    private readonly DatabaseContext _db;
    private readonly ProductPriceService _productPriceService;

    public ETicketPricingService(DatabaseContext databaseContext, ProductPriceService productPriceService)
    {
        _db = databaseContext;
        _productPriceService = productPriceService;
    }

    public async Task UpdatePriceAsync(Guid eticketId)
    {
        var eticket = await _db.ETickets.FirstOrDefaultAsync(s => s.Id == eticketId);

        if(eticket.Type == (int)ETicketType.Waste)
        {
            var priced = await CalculateWasteProductPriceAsync(eticket);

            eticket.ProductPrice = priced.Price;
            eticket.SurchargePrice = await CalculateSurchargeAsync(eticket.SiteSurchargeId);

            eticket.PricePerTonne = priced.PricedPerTonne ? priced.PricePerTonne : null;

            eticket.TotalPrice = Math.Round(eticket.ProductPrice + eticket.SurchargePrice, 2);
            eticket.VATTotal = eticket.TotalPrice > 0 ? Math.Round(eticket.TotalPrice * 0.2m, 2) : 0;
            eticket.Total = Math.Round(eticket.TotalPrice + eticket.VATTotal, 2);
        }

        if (eticket.Type == (int)ETicketType.Product)
        {
            var price = await _productPriceService.GetProductPriceAsync((Guid)eticket.ProductId, eticket.CustomerId);
            eticket.ProductPrice = price.Price;
            eticket.SurchargePrice = 0;
            eticket.TotalPrice = Math.Round(price.Price * eticket.Tonnes, 2);
            eticket.VATPercent = price.VATPercent;
            eticket.VATTotal = eticket.TotalPrice > 0 ? Math.Round(eticket.TotalPrice * price.VATPercent / 100, 2) : 0;
            eticket.Total = Math.Round(eticket.TotalPrice + eticket.VATTotal, 2);
        }

        await _db.SaveChangesAsync();
    }

    public async Task<(decimal Price, bool PricedPerTonne, decimal PricePerTonne)> CalculateWasteProductPriceAsync(Database.ETicket eticket)
    {
        if (eticket.ContractId == null)
        {
            return (0, false, 0);
        }

        var contractPricing = await _db.ContractLinks.FirstOrDefaultAsync(s => s.ContractId == eticket.ContractId && s.CustomerId == eticket.CustomerId);

        if (contractPricing.PricePerLoad != 0)
        {
            return (contractPricing.PricePerLoad, false, 0);
        }

        if (contractPricing.PricePerTonne != 0)
        {
            return (Math.Round(contractPricing.PricePerTonne / 1000 * eticket.NetWeight, 2), true, contractPricing.PricePerTonne);
        }

        return (0, false, 0);
    }

    public async Task<decimal> CalculateSurchargeAsync(Guid? siteSurchargeId)
    {
        if (siteSurchargeId != null)
        {
            var surcharge = await _db.SiteSurcharges.FirstOrDefaultAsync(s => s.Id == siteSurchargeId);
            return surcharge.Price;
        }

        return 0;
    }


}
