﻿using HT.Api.Services.Common.Address;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Database.Tables;
using HT.Shared.Constants;
using HT.Shared.Enums;
using HT.Shared.Models;
using HT.Shared.Models.Address;
using HT.Shared.Models.Document;
using HT.Shared.Models.ETicket;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Weighbridge.Job;

public class JobService(
    DatabaseContext db,
    CurrentUserService currentUserService,
    AddressService addressService,
    ProductPriceService productPriceService,
    PortalEventService portalEventService)
{
    public async Task<GridResultModel<JobListItemModel>> ListJobsAsync(GridParametersModel gridParametersModel)
    {
        var query = db.Jobs
            .Where(j => j.TenantId == currentUserService.TenantId)
            .Select(j => new JobListItemModel
            {
                Id = j.Id,
                JobNumber = j.JobNumber,
                JobNumberString = j.JobNumber.ToString(),
                Status = j.Status,
                Type = (JobType)j.Type,
                CustomerName = j.Customer != null ? j.Customer.Name : string.Empty,
                HaulierName = j.Haulier != null ? j.Haulier.Name : string.Empty,
                VehicleRegistration = j.VehicleRegistration,
                DriverName = j.DriverName,
                EntryDateTime = j.EntryDateTime,
                ExitDateTime = j.ExitDateTime,  
                Products = j.Products.Select(p => p.Product.Name).ToList(),
                DocumentCount = j.Documents.Count,
                NetWeight = j.Products.Sum(p => p.NetWeight),
                UnitTotal = j.Products.Sum(p => p.UnitTotal),
                IsInternalTransfer = j.IsInternalTransfer,
            });

        return await GridHelper.CreateGridAsync(query, gridParametersModel);
    }

    public async Task<FormResponseModel<JobModel>> CreateJobAsync()
    {
        var response = new FormResponseModel<JobModel>();

        int lastNumber = await db.Jobs
            .Where(s => s.TenantId == currentUserService.TenantId)
            .Select(s => s.JobNumber)
            .DefaultIfEmpty()
            .MaxAsync();

        var address = new Address
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
        };

        var job = new Database.Job
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            JobNumber = lastNumber + 1,
            EntryDateTime = DateTime.Now,
            ExitDateTime = DateTime.Now.AddMinutes(10),
            Type = (int)JobType.Waste,
            Status = (int)JobStatus.Saved,
            AddressId = address.Id,
        };

        db.Addresses.Add(address);
        db.Jobs.Add(job);

        await portalEventService.LogAsync(PortalEventType.JobCreated, job.Id, "Created job");

        await db.SaveChangesAsync();

        response.Data = new JobModel
        {
            Id = job.Id,
            EntryDateTime = job.EntryDateTime,
            ExitDateTime = job.ExitDateTime,
            AddressModel = new AddressModel
            {
                Id = address.Id,
            },
        };

        return response;
    }

    public async Task<FormResponseModel<JobModel>> GetJobAsync(Guid jobId)
    {
        var response = new FormResponseModel<JobModel>();

        var job = await db.Jobs
            .Include(j => j.Products)
            .Include(j => j.Documents)
            .FirstOrDefaultAsync(j => j.Id == jobId);

        if (job == null)
        {
            response.AddError("Job not found");
            return response;
        }

        response.Data = new JobModel
        {
            Id = job.Id,
            JobNumber = job.JobNumber,
            Status = (JobStatus)job.Status,
            ReadOnly = job.Status == (int)JobStatus.Closed || job.Status == (int)JobStatus.Void || job.Status == (int)JobStatus.Invoiced,
            Type = (JobType)job.Type,
            CustomerId = job.CustomerId,
            HaulierId = job.HaulierId,
            VehicleRegistration = job.VehicleRegistration,
            DriverName = job.DriverName,
            TransferNote = job.TransferNote,
            EntryDateTime = job.EntryDateTime,
            ExitDateTime = job.ExitDateTime,
            AddressModel = await addressService.GetAddressAsync(job.AddressId),
            Borough = job.Borough,
            Notes = job.Notes,
            WorksPermitNumber = job.WorksPermitNumber,
            IsInternalTransfer = job.IsInternalTransfer,
            InternalTransferToLocationId = job.InternalTransferToLocationId,
            InternalTransferFromLocationId = job.InternalTransferFromLocationId,
            Products = job.Products.Select(p => new JobProductModel
            {
                Id = p.Id,
                ProductId = p.ProductId,
                LineNumber = p.LineNumber,
                PONumber = p.PONumber,
                GrossWeight = p.GrossWeight,
                TareWeight = p.TareWeight,
                NetWeight = p.NetWeight,
                UnitPrice = p.UnitPrice,
                UnitTotal = p.UnitTotal,
                VATRate = p.VATRate,
                VATTotal = p.VATTotal,
                Total = p.Total,
                PricingMethod = p.PricingMethod,
            })
            .OrderBy(o => o.LineNumber)
            .ToList(),
            Documents = job.Documents.Select(d => new DocumentModel
            {
                Id = d.Id,
                Name = d.Name,
                AzureStorageURL = d.AzureStorageURL,
                UploadedDate = d.UploadedDate,
            }).ToList(),
        };

        return response;
    }

    public async Task<FormResponseModel<bool>> UpdateJobAsync(JobModel jobModel)
    {
        var response = new FormResponseModel<bool>();

        try
        {
            var job = await db.Jobs
                .Include(j => j.Products)
                .Include(j => j.Documents)
                .FirstOrDefaultAsync(j => j.Id == jobModel.Id);

            job.Type = (int)jobModel.Type;
            job.CustomerId = jobModel.CustomerId;
            job.HaulierId = jobModel.HaulierId;
            job.VehicleRegistration = jobModel.VehicleRegistration;
            job.DriverName = jobModel.DriverName;
            job.TransferNote = jobModel.TransferNote;
            job.EntryDateTime = jobModel.EntryDateTime;
            job.ExitDateTime = jobModel.ExitDateTime;
            job.Notes = jobModel.Notes;
            job.WorksPermitNumber = jobModel.WorksPermitNumber;
            job.IsInternalTransfer = jobModel.IsInternalTransfer;
            job.InternalTransferToLocationId = jobModel.InternalTransferToLocationId;
            job.InternalTransferFromLocationId = jobModel.InternalTransferFromLocationId;

            if (jobModel.AddressModel.HasAddress)
            {
                job.AddressId = await addressService.CreateUpdateAddressAsync(jobModel.AddressModel);
            }

            job.Borough = jobModel.Borough;

            // Delete products and add them again

            db.JobProducts.RemoveRange(job.Products);

            foreach (var product in jobModel.Products)
            {
                db.JobProducts.Add(new JobProduct
                {
                    Id = product.Id,
                    TenantId = currentUserService.TenantId,
                    JobId = job.Id,
                    PricingMethod = product.PricingMethod,
                    ProductId = product.ProductId,
                    LineNumber = product.LineNumber,
                    PONumber = product.PONumber,
                    GrossWeight = product.GrossWeight,
                    TareWeight = product.TareWeight,
                    NetWeight = product.NetWeight,
                    UnitPrice = product.UnitPrice,
                    UnitTotal = product.UnitTotal,
                    VATRate = product.VATRate,
                    VATTotal = product.VATTotal,
                    Total = product.Total,
                });
            }

            await portalEventService.LogAsync(PortalEventType.JobUpdated, jobModel.Id, description: "Updated job");
            await db.SaveChangesAsync();
        }
        catch (Exception)
        {
            response.AddError("Unable to update the job");
        }

        return response;
    }

    public async Task DeleteJobAsync(Guid jobId)
    {
        var job = await db.Jobs
            .Include(j => j.Products)
            .Include(j => j.Documents)
            .FirstOrDefaultAsync(j => j.Id == jobId);

        if (job == null)
        {
            throw new Exception("Job not found");
        }

        db.JobProducts.RemoveRange(job.Products);
        db.Documents.RemoveRange(job.Documents);
        db.Jobs.Remove(job);

        await portalEventService.LogAsync(PortalEventType.JobDeleted, jobId, "Deleted job");

        await db.SaveChangesAsync();
    }

    public async Task<JobProductModel> GetProductPriceAsync(JobProductModel productModel, Guid? customerId, JobType jobType)
    {

        productModel.UnitPrice = 0;
        productModel.UnitTotal = 0;
        productModel.VATRate = 0;
        productModel.VATTotal = 0;

        if (productModel.ProductId != null)
        {
            if (!customerId.HasValue)
            {
                customerId = Guid.Empty;
            }

            if (jobType == JobType.Product)
            {
                var price = await productPriceService.GetProductPriceAsync((Guid)productModel.ProductId, (Guid)customerId);
                decimal unitPrice = price.Price;
                decimal vatRate = price.VATPercent;
                productModel.UnitPrice = unitPrice;
                productModel.VATRate = vatRate;
            }
            else
            {
                var price = await productPriceService.GetProductPriceAsync((Guid)productModel.ProductId, (Guid)customerId, (ProductPricingMethod)productModel.PricingMethod);
                decimal unitPrice = price.Price;
                decimal vatRate = price.VATPercent;
                productModel.UnitPrice = unitPrice;
                productModel.VATRate = vatRate;
            }
        }

        productModel.VATTotal = productModel.VATRate != 0 ? productModel.UnitPrice * (productModel.VATRate / 100) : 0;
        productModel.Total = productModel.UnitPrice + productModel.VATTotal;

        return productModel;
    }

    public async Task<FormResponseModel<bool>> UpdateStatusAsync(JobModel jobModel, JobStatus status)
    {
        var response = new FormResponseModel<bool>(true);

        var job = await db.Jobs.FirstOrDefaultAsync(s => s.Id == jobModel.Id);
        job.Status = (int)status;

        await portalEventService.LogAsync(PortalEventType.JobStatusUpdated, jobModel.Id, $"Updated job status to {status}");

        await db.SaveChangesAsync();

        return response;
    }

    public async Task<FormResponseModel<string>> BulkCloseJobsAsync(JobBulkCloseModel jobBulkCloseModel)
    {
        var q = db.Jobs
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(s => s.Status == (int)ETicketStatus.Saved)
                   .AsQueryable();

        if (jobBulkCloseModel.GenerateFor == 2)
        {
            q = q.Where(w => w.EntryDateTime.Value.Date >= jobBulkCloseModel.StartDate.Value.Date && w.EntryDateTime.Value.Date <= jobBulkCloseModel.EndDate.Value.Date);
        }

        var response = new FormResponseModel<string>();

        foreach (var job in await q.ToListAsync())
        {
            job.Status = (int)JobStatus.Closed;
        }

        await portalEventService.LogAsync(PortalEventType.JobsBulkClosed, null, "Bulk closed jobs");

        await db.SaveChangesAsync();

        return response;
    }

    public async Task<JobPrintModel> GetPrintJobAsync(Guid id)
    {
        var jobPrintModel = await db.Jobs
                               .Where(i => i.Id == id)
                               .Select(s => new JobPrintModel
                               {
                                   Type = (JobType)s.Type,
                                   JobNumber = s.JobNumber.ToString(),
                                   CustomerName = s.CustomerId != null ? s.Customer.Name : string.Empty,
                                   HaulierName = s.HaulierId != null ? s.Haulier.Name : string.Empty,
                                   VehicleRegistration = s.VehicleRegistration,
                                   DriverName = s.DriverName,
                                   TransferNote = s.TransferNote,
                                   EntryDateTime = s.EntryDateTime,
                                   ExitDateTime = s.ExitDateTime,
                                   AddressLine1 = s.AddressId != null ? s.Address.AddressLine1 : string.Empty,
                                   AddressLine2 = s.AddressId != null ? s.Address.AddressLine2 : string.Empty,
                                   AddressLine3 = s.AddressId != null ? s.Address.AddressLine3 : string.Empty,
                                   AddressLine4 = s.AddressId != null ? s.Address.AddressLine4 : string.Empty,
                                   AddressPostcode = s.AddressId != null ? s.Address.AddressPostcode : string.Empty,
                                   Borough = s.Borough,
                                   Notes = s.Notes,
                                   WorksPermitNumber = s.WorksPermitNumber,
                                   CarrierLicence = s.Haulier.CarrierLicence,
                                   IsInternalTransfer = s.IsInternalTransfer,
                                   InternalTransferFromLocation = s.InternalTransferFromLocationId != null ? s.InternalTransferFromLocation.Name : string.Empty,
                                   InternalTransferToLocation = s.InternalTransferToLocationId != null ? s.InternalTransferToLocation.Name : string.Empty,

                                   Products = s.Products.Select(p => new JobProductPrintModel
                                   {
                                       LineNumber = p.LineNumber,
                                       PONumber = p.PONumber,
                                       ProductName = p.Job.Type == (int)JobType.Waste ? p.Product.EWCCode + " " + p.Product.Name : p.Product.Name,
                                       GrossWeight = p.GrossWeight,
                                       TareWeight = p.TareWeight,
                                       NetWeight = p.NetWeight,
                                       UnitPrice = p.UnitPrice,
                                       VATRate = p.VATRate,
                                       VATTotal = p.VATTotal,
                                       Total = p.Total,
                                       PricingMethod = p.PricingMethod,
                                   })
                                   .ToList()
                               })
                               .FirstOrDefaultAsync();

        return jobPrintModel;
    }

    public async Task<List<DropdownListItem>> ListInternalTransferLocationDropdownAsync()
    {
        var hauliers = await db.InternalTransferLocations
                .Where(w => w.TenantId == currentUserService.TenantId)
                .OrderBy(o => o.Name)
                .Select(s => new DropdownListItem
                {
                    Id = s.Id.ToString(),
                    Name = s.Name,
                })
                .ToListAsync();

        return hauliers;
    }
}
