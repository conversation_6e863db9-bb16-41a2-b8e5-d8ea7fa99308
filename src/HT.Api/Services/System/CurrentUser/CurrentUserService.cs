﻿using HT.Database;
using HT.Shared.Models.User;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services;

public class CurrentUserService(IHttpContextAccessor httpContext, DatabaseContext db)
{
    public Guid TenantId => new(httpContext.HttpContext!.User.Claims.FirstOrDefault(s => s.Type == "TenantId")?.Value ?? Guid.Empty.ToString());

    public Guid UserId => new(httpContext.HttpContext!.User.Claims.FirstOrDefault(s => s.Type == "UserId")?.Value ?? Guid.Empty.ToString());

    public async Task<UserSecurityModel> GetUserSecurityAsync()
    {
        var user = await db.Users.Where(s => s.Id == UserId).Select(s => new UserSecurityModel
        {
            UserId = s.Id,
            UserType = s.UserType,
            Sites = s.UserSiteLinks.Select(s => s.SiteId).ToList()
        }).FirstOrDefaultAsync();

        user ??= new UserSecurityModel
        {
            UserType = (int)UserType.SuperUser
        };

        return user;
    }



}
