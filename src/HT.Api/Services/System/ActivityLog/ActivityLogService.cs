﻿using HT.Database;
using HT.Shared.Enums;

namespace HT.Api.Services.Common.ActivityLog;

public class ActivityLogService(DatabaseContext db)
{
    public async Task Log(Guid? tenantId, Guid? userId, ActivityLogType activityLogType, string message, Guid? primaryObjectId)
    {
        var activityLog = new Database.Tables.ActivityLog
        {
            Id = Guid.NewGuid(),
            DateTimeUtc = DateTime.UtcNow,
            TenantId = tenantId,
            UserId = userId,
            Type = (int)activityLogType,
            Message = message,
            PrimaryObjectId = primaryObjectId,
        };

        db.ActivityLogs.Add(activityLog);
        await db.SaveChangesAsync();
    }
}
