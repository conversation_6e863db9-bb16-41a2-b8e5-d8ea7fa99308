﻿using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Models.Invoice;
using HT.Shared.Models.Product;
using HT.Shared.Models;
using HT.Shared.Models.User;
using Microsoft.EntityFrameworkCore;
using HT.Shared.Models.Report;
using HT.Shared.Models.Dashboard;
using HT.Shared.Models.HaulierRoute;
using HT.Shared.Enums;
using HT.Database.Migrations;
using HT.Shared.Models.Address;

namespace HT.Api.Services;

public class ReportService(
    DatabaseContext db,
    CurrentUserService currentUserService,
    ProductService productService)
{
    public async Task<PurchasesReportModel> GetPurchasesReportAsync(ReportFiltersModel reportFiltersModel)
    {
        var owings = await db.Suppliers
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.Purchases.Any(a => !a.Paid))
                   .Select(s => new SupplierOwingModel
                   {
                       Id = s.Id,
                       SupplierName = s.Name,
                       AmountOwed = s.Purchases.Sum(sum => sum.Total)
                   })
                   .ToListAsync();

        var purchases = await db.Purchases
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.PurchaseDate >= reportFiltersModel.StartDay)
                   .Where(w => w.PurchaseDate <= reportFiltersModel.EndDay)
                   .Select(s => new PurchaseListItemModel
                   {
                       PurchaseOrderNumber = s.PurchaseOrderNumber,
                       SupplierName = s.Supplier.Name,
                       PurchaseDate = s.PurchaseDate,
                       Total = s.Total,
                       Paid = s.Paid,
                   }).OrderByDescending(o => o.PurchaseDate)
                   .ToListAsync();

        var purchasesDue = await db.Purchases
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.PaymentDueDate == null || w.PaymentDueDate >= reportFiltersModel.StartDay && w.PaymentDueDate <= reportFiltersModel.EndDay)
                   .Select(s => new PurchaseListItemModel
                   {
                       PurchaseOrderNumber = s.PurchaseOrderNumber,
                       SupplierName = s.Supplier.Name,
                       PurchaseDate = s.PurchaseDate,
                       Total = s.Total,
                       Paid = s.Paid,
                       PaymentDueDate = s.PaymentDueDate,
                   })
                   .OrderBy(o => o.Paid)
                   .ThenBy(o => o.PaymentDueDate)
                   .ToListAsync();

        return new PurchasesReportModel { SupplierOwings = owings, Purchases = purchases, PurchasesDue = purchasesDue };
    }

    public async Task<GridResultModel<ETicketReportListItemModel>> ListETicketsAsync(GridParametersModel gridParametersModel)
    {
        var q = db.ETickets
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Select(s => new ETicketReportListItemModel
                   {
                       Id = s.Id,
                       SiteId = s.SiteId,
                       EntryDateTime = s.EntryDateTime,
                       Status = s.Status,
                       SiteETicketNumber = s.SiteETicketNumber,
                       Customer = s.Customer != null ? s.Customer.Name : "",
                       ContractNumber = s.Contract != null ? s.Contract.ContractNumber : 0,
                       ContractName = s.Contract != null ? s.Contract.Name : "",
                       Registration = s.HaulierVehicle != null ? s.HaulierVehicle.Registration : "",
                       EWCCode = s.Customer != null && s.Contract.SitePermitProductLink != null && s.Contract.SitePermitProductLink.Product != null ? s.Contract.SitePermitProductLink.Product.EWCCode : "",
                       ProductName = s.ProductId != null ? s.Product.Name : "",
                       Tonnes = Math.Round(s.NetWeight / 1000, 2),
                       ProductPrice = s.ProductPrice,
                       SurchargePrice = s.SurchargePrice,
                       TotalPrice = s.TotalPrice,
                       VATTotal = s.VATTotal,
                       Total = s.Total,
                       OrderNumber = s.OrderNumber ?? string.Empty,
                       InvoiceCompanyNumber = s.InvoiceLines.Any()
                                  ? s.InvoiceLines.FirstOrDefault().Invoice.InvoiceCompanyNumber
                                  : null,
                   })
                   .OrderByDescending(o => o.EntryDateTime)
                   .AsQueryable();

        var userSecurity = await currentUserService.GetUserSecurityAsync();

        if (userSecurity.UserType is not ((int)UserType.SuperUser) and not ((int)UserType.Admin))
        {
            q = q.Where(s => userSecurity.Sites.Contains(s.SiteId));
        }

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<GridResultModel<HaulierStopListModel>> ListHaulierStopsAsync(GridParametersModel gridParametersModel)
    {
        var q = db.HaulierStops
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.Type != (int)HaulierStopType.TipStop)
                   .Where(w => w.Status != (int)HaulierStopStatus.None)
                   .AsQueryable();

        if (gridParametersModel.ParameterId != Guid.Empty)
        {
            q = q.Where(w => w.ContractId == gridParametersModel.ParameterId);
        }

            var query = q.Select(s => new HaulierStopListModel
                   {
                       Id = s.Id,
                       StopNumber = s.StopNumber,
                       StopNumberString = s.StopNumber != null ? "Stop-" + s.StopNumber.ToString() : string.Empty,
                       OrderNumber = s.OrderNumber ?? string.Empty,
                       ProofOfDeliveryId = s.ProofOfDeliveryId,
                       ContractName = s.ContractLink != null ? s.ContractLink.Contract.Name : string.Empty,
                       CustomerName = s.ContractLink != null ? s.ContractLink.Customer.Name : string.Empty,
                       CustomerProduct = s.ContractLink != null ? s.ContractLink.Product.Name : string.Empty,
                       Status = s.Status,
                       RouteDate = s.Route.RouteDate,
                   })
                   .OrderByDescending(o => o.RouteDate)
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(query, gridParametersModel);
    }

    public async Task<GridResultModel<JobReportItemModel>> ListJobsAsync(GridParametersModel gridParametersModel)
    {
        var q = db.JobProducts
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Select(s => new JobReportItemModel
            {
                JobId = s.Job.Id,
                JobNumberString = "Job-" + s.Job.JobNumber.ToString(),
                Status = s.Job.Status,
                Type = s.Job.Type,
                CustomerName = s.Job.Customer != null ? s.Job.Customer.Name : string.Empty,
                HaulierName = s.Job.Haulier != null ? s.Job.Haulier.Name : string.Empty,
                VehicleRegistration = s.Job.VehicleRegistration,
                DriverName = s.Job.DriverName,
                EntryDateTime = s.Job.EntryDateTime,
                ExitDateTime = s.Job.ExitDateTime,
                JobNumber = s.Job.JobNumber,
                PONumber = s.PONumber != null ? s.PONumber : string.Empty,
                ProductName = s.Product.Name,
                PricingMethod = s.PricingMethod,
                GrossWeight = s.GrossWeight,
                TareWeight = s.TareWeight,
                NetWeight = s.NetWeight,
                UnitPrice = s.UnitPrice,
                UnitTotal = s.UnitTotal,
                VATRate = s.VATRate,
                VATTotal = s.VATTotal,
                Total = s.Total,
                InvoiceCompanyNumber = s.Job.InvoiceLines.Any() 
                                ? s.Job.InvoiceLines.FirstOrDefault(f => f.Description.ToLower().Contains(s.Product.Name.ToLower())) != null
                                    ? s.Job.InvoiceLines.FirstOrDefault(f => f.Description.ToLower().Contains(s.Product.Name.ToLower())).Invoice.InvoiceCompanyNumber
                                    : null
                                : null,
            })
            .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<GridResultModel<InvoiceReportListItemModel>> ListInvoicesAsync(GridParametersModel gridParametersModel)
    {
        var q = db.Invoices
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.Status == (int)InvoiceStatus.Sent)
                   .Select(s => new InvoiceReportListItemModel
                   {
                       Id = s.Id,
                       DateTime = s.DateTime,
                       CustomerName = s.Customer.Name,
                       ContractName = s.Contract.Name,
                       InvoiceNumber = s.InvoiceNumber,
                       InvoiceCompanyNumber = s.InvoiceCompanyNumber,
                       Lines = s.InvoiceLines.Count,
                       UnitTotal = s.UnitTotal,
                       VATTotal = s.VATTotal,
                       Total = s.Total,
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<GridResultModel<ProductsSoldReportListItemModel>> ListProductsSoldAsync(GridParametersModel gridParametersModel)
    {   
        var q = db.OrderLines
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.Order.Route != null)
                   .GroupBy(g => new { g.ProductId, g.Order.CustomerId })
                   .Select(s => new ProductsSoldReportListItemModel
                   {
                       OrderDate = s.FirstOrDefault().Order.OrderDate,
                       CustomerName = s.FirstOrDefault().Order.Customer.Name,
                       ProductName = s.FirstOrDefault().Product.Name,
                       Quantity = s.Sum(sum => sum.Quantity),
                       Total = s.Sum(sum => sum.Total)
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<GridResultModel<ProductsSoldIndividualReportListItemModel>> ListProductsSoldIndividuallyAsync(GridParametersModel gridParametersModel)
    {
        var q = db.OrderLines
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.Order.Route != null)
                   .Select(s => new ProductsSoldIndividualReportListItemModel
                   {
                       Route = s.Order.Route.Name,
                       Date = s.Order.Route.RouteDate,
                       CustomerName = s.Order.Customer.Name,
                       Address = new AddressModel(s.Order.Customer.CustomerAddress),
                       CustomerTags = s.Order.Customer.CustomerTagLinks.Select(t => t.CustomerTag.Name).ToList(),
                       ProductName = s.Product.Name,
                       Quantity = s.Quantity,
                       Total = s.Total,
                       PhoneNumber = !string.IsNullOrWhiteSpace(s.Order.Customer.PhoneNumber) && !string.IsNullOrWhiteSpace(s.Order.Customer.MobilePhoneNumber)
                                 ? $"{s.Order.Customer.PhoneNumber} - {s.Order.Customer.MobilePhoneNumber}"
                                 : !string.IsNullOrWhiteSpace(s.Order.Customer.PhoneNumber)
                                    ? s.Order.Customer.PhoneNumber
                                    : s.Order.Customer.MobilePhoneNumber
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<ProductsSoldIndividualReportTotalsModel> GetProductsSoldIndividuallyTotalsAsync(GridParametersModel gridParametersModel)
    {
        var q = db.OrderLines
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.Order.Route != null)
                   .Select(s => new ProductsSoldIndividualReportListItemModel
                   {
                       Route = s.Order.Route.Name,
                       Date = s.Order.Route.RouteDate,
                       CustomerName = s.Order.Customer.Name,
                       ProductName = s.Product.Name,
                       Quantity = s.Quantity,
                       Total = s.Total
                   })
                   .AsQueryable();

        var result = await GridHelper.GetGridTotalsAsync(q, gridParametersModel);
        var totals = new ProductsSoldIndividualReportTotalsModel
        {
            Total = result.Results.Sum(s => s.Total),
            Quantity = result.Results.Sum(s => s.Quantity)
        };

        return totals;
    }

    public async Task<GridResultModel<ProductsSoldIndividualReportListItemModel>> ListProductsSoldGroupedAsync(GridParametersModel gridParametersModel)
    {
        var q = db.OrderLines
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.Order.Route != null)
                   .Where(w => gridParametersModel.ParameterIds == null || gridParametersModel.ParameterIds.Count == 0 ||
                       w.Order.Customer.CustomerTagLinks.Any(a => gridParametersModel.ParameterIds.Contains(a.CustomerTagId)))
                   .OrderBy(o => o.Order.Customer.Name)
                   .Select(s => new ProductsSoldIndividualReportListItemModel
                   {
                       Id = s.ProductId ?? Guid.Empty,
                       Route = s.Order.Route.Name,
                       Date = s.Order.Route.RouteDate,
                       CustomerName = s.Order.Customer.Name,
                       ProductName = s.Product.Name,
                       Quantity = s.Quantity,
                       Total = s.Total,
                       PhoneNumber = !string.IsNullOrWhiteSpace(s.Order.Customer.PhoneNumber) && !string.IsNullOrWhiteSpace(s.Order.Customer.MobilePhoneNumber)
                                 ? $"{s.Order.Customer.PhoneNumber} - {s.Order.Customer.MobilePhoneNumber}"
                                 : !string.IsNullOrWhiteSpace(s.Order.Customer.PhoneNumber)
                                    ? s.Order.Customer.PhoneNumber
                                    : s.Order.Customer.MobilePhoneNumber
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<TotalTonnageReportModel> ListTotalTonnageAsync(ReportFiltersModel reportFiltersModel)
    {
        var totalTonnageReport = new TotalTonnageReportModel();

        // Invoices
        var invoiceTotalsList = await db.Invoices
                    .Where(w => w.TenantId == currentUserService.TenantId)
                    .Where(w => w.DateTime >= reportFiltersModel.StartDay)
                    .Where(w => w.DateTime <= reportFiltersModel.EndDay)
                    //.Where(w => w.Status == (int)InvoiceStatus.Sent) Do we need to check the status?
                    .Select(s => new InvoiceTotalsModel
                    {
                        InvoiceNumber = s.InvoiceNumber,
                        VATRate = s.InvoiceLines.First().VATRate, // Does this come from the lines? Is it always the same?
                        NetCost = s.UnitTotal,
                        VATCost = s.VATTotal,
                        TotalCost = s.Total,
                    })
                    .ToListAsync();

        totalTonnageReport.InvoiceTotals = invoiceTotalsList;

        // Get the route line ids
        var routeLineIds = await db.RouteLines
                        .Where(w => w.TenantId == currentUserService.TenantId)
                        .Where(w => w.Route.RouteDate >= reportFiltersModel.StartDay)
                        .Where(w => w.Route.RouteDate <= reportFiltersModel.EndDay)
                        .Select(s => s.Id)
                        .ToListAsync();

        // Get the order lines
        var orderLines = await db.OrderLines
                    .Include(i => i.Product)
                    .Include(i => i.Order)
                        .ThenInclude(ti => ti.RouteLines)
                            .ThenInclude(ti => ti.Route)
                    .Where(w => w.ProductId != null)
                    .Where(w => w.Order.RouteLines.Any(a => routeLineIds.Contains(a.Id)))
                    .ToListAsync();

        // Tonnage
        var productGroups = await productService.GetProductGroupsSelectListAsync();

        foreach (var productGroup in productGroups.Where(g => g.Name is not "Sticks" and not "Logs"))
        {
            var tonnageModel = new TonnageModel
            {
                ProductGroup = productGroup.Name,
                Kilograms = 0,
                Tonnes = 0,
                Price = 0,
                TonnesUnit = 0,
                KgsUnit = 0,
                Cost = 0,
                Profit = 0,
                Amount = 0,
                VATTotal = 0,
                Total = 0,

            };

            var productGroupOrderLines = orderLines.Where(w => w.Product.ProductGroupId == Guid.Parse(productGroup.Id)).ToList();

            if (productGroupOrderLines.Any())
            {

                tonnageModel.Kilograms = productGroupOrderLines.Sum(s => s.Quantity * s.Product.ProductWeightKilograms);
                tonnageModel.Price = productGroupOrderLines.Sum(s => s.Quantity * s.Product.Cost);

                tonnageModel.Profit = productGroupOrderLines.Sum(s => s.Quantity * s.Product.OperatingProfit);

                tonnageModel.VATTotal = productGroupOrderLines.Sum(s => s.VATTotal);
                tonnageModel.Total = productGroupOrderLines.Sum(s => s.Total);

                tonnageModel.Amount = tonnageModel.Total - tonnageModel.VATTotal;
                tonnageModel.Cost = tonnageModel.Amount - tonnageModel.Profit;

                if (tonnageModel.Kilograms > 0)
                {
                    tonnageModel.Tonnes = tonnageModel.Kilograms / 1000;

                    tonnageModel.TonnesUnit = (int)Math.Floor(tonnageModel.Kilograms / 1000);
                    tonnageModel.KgsUnit = (int)(tonnageModel.Kilograms % 1000 / 50);
                }

                totalTonnageReport.AllTonnages.Add(tonnageModel);
            }
        }

        // Total Tonnage by product

        foreach (var orderLineGroup in orderLines.Where(l => l.Product.ProductGroup == null || l.Product.ProductGroup.Name != "Sticks" && l.Product.ProductGroup.Name != "Logs").GroupBy(g => g.Product.Name))
        {
            var firstOrderLine = orderLineGroup.First();

            var tonnageModel = new TonnageModel
            {
                ProductName = firstOrderLine.Product.Name,
                Kilograms = 0,
                Tonnes = 0,
                Price = 0,
                TonnesUnit = 0,
                KgsUnit = 0,
                Cost = 0,
                Profit = 0,
                Amount = 0,
                VATTotal = 0,
                Total = 0,

            };

            tonnageModel.Kilograms = orderLineGroup.Sum(s => s.Quantity * s.Product.ProductWeightKilograms);
            tonnageModel.Price = orderLineGroup.Sum(s => s.Quantity * s.Product.Cost);

            tonnageModel.Profit = orderLineGroup.Sum(s => s.Quantity * s.Product.OperatingProfit);

            tonnageModel.VATTotal = orderLineGroup.Sum(s => s.VATTotal);
            tonnageModel.Total = orderLineGroup.Sum(s => s.Total);

            tonnageModel.Amount = tonnageModel.Total - tonnageModel.VATTotal;
            tonnageModel.Cost = tonnageModel.Amount - tonnageModel.Profit;

            if (tonnageModel.Kilograms > 0)
            {
                tonnageModel.Tonnes = tonnageModel.Kilograms / 1000;

                tonnageModel.TonnesUnit = (int)Math.Floor(tonnageModel.Kilograms / 1000);
                tonnageModel.KgsUnit = (int)(tonnageModel.Kilograms % 1000 / 50);                   
            }

            totalTonnageReport.ProductTonnages.Add(tonnageModel);
        }





        totalTonnageReport.InvoiceTotals = invoiceTotalsList;

        var startDate = ((DateTimeOffset)reportFiltersModel.StartDay).DateTime;
        var endDate = ((DateTimeOffset)reportFiltersModel.EndDay).DateTime;

        // Bags
        foreach (var productGroup in productGroups.Where(g => g.Name is "Sticks" or "Logs"))
        {
            var bagModel = new BagModel
            {
                ProductGroup = productGroup.Name,
                Bags = 0,
                Price = 0,
            };

            var productGroupOrderLines = orderLines.Where(w => w.Product.ProductGroupId == Guid.Parse(productGroup.Id)).ToList();

            bagModel.Bags = productGroupOrderLines.Sum(s => s.Quantity);
            bagModel.Price = productGroupOrderLines.Sum(s => s.Total);

            totalTonnageReport.AllBags.Add(bagModel);
        }

        // Products by day

        foreach (var productGroup in productGroups.Where(g => g.Name is not "Sticks" and not "Logs"))
        {
            var daysModel = new ProductGroupByDayModel
            {
                ProductGroup = productGroup.Name,
            };

            for (var routeDate = startDate; routeDate <= endDate; routeDate = routeDate.AddDays(1))
            {
                var todaysOrderLines = orderLines
                                    .Where(o => o.Order.RouteLines.First().Route.RouteDate.Date == routeDate.Date)
                                    .Where(o => o.Product.ProductGroupId == Guid.Parse(productGroup.Id))
                                    .ToList();
                decimal total = todaysOrderLines.Sum(s => s.Quantity * s.Product.ProductWeightKilograms);

                int tonnesUnit = (int)Math.Floor(total / 1000);
                int kgsUnit = (int)(total % 1000 / 50);

                daysModel.TotalsDict.Add(routeDate, $"{tonnesUnit} - {kgsUnit}");
            }

            totalTonnageReport.ProductGroupsByDay.Add(daysModel);

        }

        for (var routeDate = startDate; routeDate <= endDate; routeDate = routeDate.AddDays(1))
        {
            var todaysOrderLines = orderLines
                                .Where(o => o.Order.RouteLines.First().Route.RouteDate.Date == routeDate.Date)
                                .ToList();

            decimal total = todaysOrderLines.Sum(s => s.Quantity * s.Product.ProductWeightKilograms);

            int tonnesUnit = (int)Math.Floor(total / 1000);
            int kgsUnit = (int)(total % 1000 / 50);

            totalTonnageReport.ProductGroupsByDayTotalsList.Add(routeDate, $"{tonnesUnit} - {kgsUnit}");
        }


        return totalTonnageReport;
    }

}
