﻿using HT.Database;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Shared.Models.Report.OrdersReport;

namespace HT.Api.Services;

public class OrdersReportService(
    DatabaseContext db,
    CurrentUserService currentUserService)
{
    public async Task<GridResultModel<OrdersReportModel>> ListOrdersAsync(GridParametersModel gridParametersModel)
    {
        var q = db.Customers
                .Where(c => c.TenantId == currentUserService.TenantId)
                //.Where(c => c.CustomerTagLinks.Any(a => gridParametersModel.ParameterIds.Contains(a.CustomerTag.Id)))
                .Select(s => new OrdersReportModel
                {
                    CustomerId = s.Id,
                    CustomerName = s.Name,
                    CustomerTags = s.CustomerTagLinks.Select(t => t.CustomerTag.Name).ToList(),
                    ProductIds = s.Orders.SelectMany(o => o.OrderLines.Select(ol => (Guid)ol.ProductId)).ToList(),
                    Address = s.CustomerAddress != null
                            ? new Shared.Models.Address.AddressModel
                            {
                                AddressLine1 = s.CustomerAddress.AddressLine1,
                                AddressLine2 = s.CustomerAddress.AddressLine2,
                                AddressLine3 = s.CustomerAddress.AddressLine3,
                                AddressLine4 = s.CustomerAddress.AddressLine4,
                                AddressPostcode = s.CustomerAddress.AddressPostcode
                            }
                            : new Shared.Models.Address.AddressModel(),
                    OrdersTotal = s.Orders.Sum(s => s.Total),
                    PaymentsTotal = s.CustomerPayments.Sum(sum => sum.Total)
                })
                .OrderBy(o => o.CustomerName)
                .AsQueryable();

        var grid = await GridHelper.CreateGridAsync(q, gridParametersModel);

        return grid;
    }
}
