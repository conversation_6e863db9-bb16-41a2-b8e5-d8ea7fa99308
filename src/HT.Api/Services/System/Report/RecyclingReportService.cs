﻿using HT.Blazor.Models.Form;
using HT.Database;
using HT.Shared.Models.ETicket;
using HT.Shared.Models.Report.Recycling;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace HT.Api.Services;

public class RecyclingReportService(
    DatabaseContext db,
    CurrentUserService currentUserService)
{
    public async Task<decimal> GetTotalTonnageAsync(Guid siteId, DateTime previousDate)
    {
        // The total tonnage of all eTickets for this site(that aren't Void) since the previous row was added

        decimal tonnage = await db.ETickets
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.EntryDateTime >= previousDate)
            .Where(w => w.Status != (int)ETicketStatus.Void)
            .SumAsync(s => s.NetWeight);

        return tonnage;
    }

    public async Task<FormResponseModel<bool>> AddRecyclingAsync(RecyclingAddModel recyclingModel)
    {
        await db.Recycling.AddAsync(new Recycling
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            TotalTonnage = recyclingModel.TotalTonnage,
            TonnageRecycled = recyclingModel.TonnageRecycled,
        });

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>();
    }
}
