﻿using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Route;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;

namespace HT.Api.Services;

public class OwingsByTagReportService(
    DatabaseContext db,
    CurrentUserService currentUserService)
{
    public async Task<GridResultModel<RouteCustomersOwingModel>> ListOwingsAsync(GridParametersModel gridParametersModel)
    {
        var q = db.Customers
                .Where(c => c.TenantId == currentUserService.TenantId)

                .Where(c => c.CustomerTagLinks.Any(a => gridParametersModel.ParameterIds.Contains(a.CustomerTag.Id)))

                .Where(c => c.Orders.Where(o => o.Status != (int)OrderStatus.Cancelled &&
                                                o.RouteLines != null &&
                                                o.RouteLines.FirstOrDefault().Route.RouteDate.Date <= DateTime.Now.Date).Sum(s => s.Total)
                                                >
                                                c.CustomerPayments.Where(o => o.Date.Date <= DateTime.Now.Date).Sum(s => s.Total))

                .Where(c => c.Orders.Any(o => o.RouteId == null || o.Route.RouteDate.Date <= DateTime.Today.Date))

                .Select(s => new RouteCustomersOwingModel
                {
                    CustomerId = s.Id,
                    CustomerName = s.Name,
                    CustomerTags = s.CustomerTagLinks.Select(t => t.CustomerTag.Name).ToList(),
                    Address = s.CustomerAddress != null
                            ? new Shared.Models.Address.AddressModel
                            {
                                AddressLine1 = s.CustomerAddress.AddressLine1,
                                AddressLine2 = s.CustomerAddress.AddressLine2,
                                AddressLine3 = s.CustomerAddress.AddressLine3,
                                AddressLine4 = s.CustomerAddress.AddressLine4,
                                AddressPostcode = s.CustomerAddress.AddressPostcode
                            }
                            : new Shared.Models.Address.AddressModel(),
                    LastDeliveryDate = s.Orders
                                        .Where(o => o.RouteId != null)
                                        .Where(o => o.Route.RouteDate < DateTime.Today)
                                        .OrderByDescending(o => o.Route.RouteDate)
                                        .Select(s => s.Route.RouteDate)
                                        .FirstOrDefault(),

                    OrdersTotal = s.Orders.Where(o => o.Status != (int)OrderStatus.Cancelled && o.RouteLines != null &&
                                                      o.RouteLines.FirstOrDefault().Route.RouteDate.Date <= DateTime.Now.Date)
                                          .Sum(s => s.Total),

                    PaymentsTotal = s.CustomerPayments.Where(o => o.Date.Date <= DateTime.Now.Date).Sum(sum => sum.Total)
                })
                .OrderBy(o => o.CustomerName)
                .AsQueryable();

        var grid = await GridHelper.CreateGridAsync(q, gridParametersModel);

        return grid;
    }
}
