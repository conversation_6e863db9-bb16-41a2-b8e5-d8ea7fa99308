﻿using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Database.Tables;
using HT.Shared.Constants;
using HT.Shared.Models.PortalEvent;

namespace HT.Api.Services;

public class PortalEventService(
    DatabaseContext db,
    CurrentUserService currentUserService)
{
    public async Task LogAsync(Guid portalEventTypeId, Guid? relatedEntityId, string description = null)
    {
        var portalEvent = new PortalEvent
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            PortalEventTypeId = portalEventTypeId,
            UserId = currentUserService.UserId,
            Timestamp = DateTime.Now,
            Description = description,
            RelatedEntityId = relatedEntityId
        };

        db.PortalEvents.Add(portalEvent);
    }

    public async Task<GridResultModel<PortalEventListItem>> ListPortalEventsAsync(Guid relatedEntityId, GridParametersModel gridParametersModel)
    {
        var q = db.PortalEvents
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.RelatedEntityId == relatedEntityId)
            .Select(o => new PortalEventListItem
            {
                User = o.User.Email == "superuser" ? "System" : o.User.Email,
                Timestamp = o.Timestamp,
                Description = o.Description,
                PortalEventTypeId = o.PortalEventTypeId
            })
            .AsQueryable();

        var results = await GridHelper.CreateGridAsync(q, gridParametersModel);

        foreach (var result in results.Results)
        {
            result.PortalEventTypeDescription = PortalEventType.EventDescriptions.GetValueOrDefault(result.PortalEventTypeId, "Unknown Event");
        }

        return results;
    }
}
