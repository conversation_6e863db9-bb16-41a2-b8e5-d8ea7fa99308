﻿using HT.Api.Common.Extensions;
using HT.Api.Services.Common.Address;
using HT.Api.Services.Common.RouteXL;
using HT.Api.Services.Haulier;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Constants;
using HT.Shared.Enums;
using HT.Shared.Models.Address;
using HT.Shared.Models.HaulierRoute;
using HT.Shared.Models.Order;
using HT.Shared.Models.Route;
using HT.Shared.Models.Route.Calendar;
using HT.Shared.Models.User;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.HaulierRoute;

public class HaulierRouteProductService(
    DatabaseContext db,
    CurrentUserService currentUserService,
    ProductPriceService productPriceService,
    ProductService productService)
{

    public async Task<HaulierStopProductLineModel> CreateProductLineAsync(
        Guid productId,
        Guid contractLinkId)
    {
        var contractLink = await db.ContractLinks.FirstOrDefaultAsync(s => s.Id == contractLinkId);

        var productModel = await productService.GetProductAsync(productId);
        var price = await productPriceService.GetProductPriceAsync(productId, contractLink.CustomerId.Value);

        decimal VATTotal = price.VATPercent != 0 ? price.Price * (price.VATPercent / 100) : 0;

        var productLineModel = new HaulierStopProductLineModel
        {
            ProductName = productModel.Name,
            Quantity = 1,
            UnitPrice = price.Price,
            VATRate = price.VATPercent,
            UnitTotal = price.Price,
            VATTotal = VATTotal,
            Total = VATTotal + price.Price
        };
        
        return productLineModel;
    }

    public async Task AddProductLinesAsync(Guid haulierStopId, List<HaulierStopProductLineModel> productLineModels)
    {
        foreach (var line in productLineModels)
        {
            db.HaulierStopProductLines.Add(new HaulierStopProductLine
            {
                Id = Guid.NewGuid(),
                TenantId = currentUserService.TenantId,
                HaulierStopId = haulierStopId,
                LineNumber = line.LineNumber,
                ProductName = line.ProductName,
                Quantity = line.Quantity,
                UnitPrice = line.UnitPrice,
                VATRate = line.VATRate,
                UnitTotal = line.UnitTotal,
                VATTotal = line.VATTotal,
                Total = line.Total,
            });
        }

        await db.SaveChangesAsync();
    }

    public async Task DeleteAllProductLinesAsync(Guid haulierStopId)
    {
        var productLines = await db.HaulierStopProductLines
            .Where(o => o.HaulierStopId == haulierStopId)
            .ToListAsync();

        db.HaulierStopProductLines.RemoveRange(productLines);

        await db.SaveChangesAsync();
    }
}
