﻿using HT.Api.Common.Extensions;
using HT.Api.Services.Common.Address;
using HT.Api.Services.Common.RouteXL;
using HT.Api.Services.Haulier;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Address;
using HT.Shared.Models.HaulierRoute;
using HT.Shared.Models.Route;
using HT.Shared.Models.Route.Calendar;
using HT.Shared.Models.User;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.HaulierRoute;

public class HaulierRouteService(
    DatabaseContext db,
    CurrentUserService currentUserService,
    AddressService addressService,
    RouteXlService routeXlService,  
    HaulierService haulierService,
    ProofOfDeliveryService proofOfDeliveryService,
    HaulierRouteProductService haulierRouteProductService)
{

    /// <summary>
    /// Route calendar.
    /// </summary>
    /// <param name="weekStartDate"></param>
    /// <returns></returns>
    public async Task<RouteCalendarModel> GetRouteCalendarAsync(DateTime weekStartDate)
    {
        var userSecurity = await currentUserService.GetUserSecurityAsync();

        var routeCalendarModel = new RouteCalendarModel
        {
            StartDate = weekStartDate,
            Days = RouteCalendarHelper.GetSevenDays(weekStartDate)
        };

        foreach (var day in routeCalendarModel.Days)
        {
            var routes = db.Routes
                .Include(r => r.RouteLines)
                    .ThenInclude(rl => rl.HaulierStop)
                        .ThenInclude(hs => hs.Contract)
                .Include(r => r.RouteLines)
                    .ThenInclude(rl => rl.HaulierStop)
                        .ThenInclude(hs => hs.ContractLink)
                            .ThenInclude(cl => cl.Customer)
                .Include(r => r.HaulierVehicle)
                .Where(w => w.TenantId == currentUserService.TenantId)
                .Where(w => w.RouteDate.Date == day.Date)
                .AsQueryable();

            if (userSecurity.UserType == (int)UserType.Driver)
            {
                routes = routes.Where(w => w.HaulierDriverUserId == currentUserService.UserId);
            }

            var routesData = await routes.Select(s => new 
            {
                Id = s.Id,
                Status = s.Status,
                Name = s.Name,
                Vehicle = s.HaulierVehicle.Registration,
                VehicleRegistration = s.HaulierVehicle.Registration,
                Stops = s.RouteLines.Count,
                ContractCustomerPairs = s.RouteLines
                    .Where(rl => rl.HaulierStop != null && rl.HaulierStop.Contract != null)
                    .Select(rl => new 
                    {
                        ContractName = rl.HaulierStop.Contract.Name,
                        CustomerName = rl.HaulierStop.ContractLink != null && rl.HaulierStop.ContractLink.Customer != null 
                            ? rl.HaulierStop.ContractLink.Customer.Name
                            : string.Empty
                    })
                    .ToList()
            })
            .ToListAsync();

            var calendarRoutes = routesData.Select(r => new RouteCalendarDayItemModel
            {
                Id = r.Id,
                Status = r.Status,
                Name = r.Name,
                Vehicle = r.Vehicle,
                VehicleRegistration = r.VehicleRegistration,
                Stops = r.Stops,
                ContractNames = string.Join(", ", r.ContractCustomerPairs.Select(p => p.ContractName).Distinct()),
                ContractCustomerNames = string.Join(", ", r.ContractCustomerPairs
                    .Select(p => !string.IsNullOrEmpty(p.CustomerName) 
                        ? $"{p.ContractName} ({p.CustomerName})" 
                        : p.ContractName)
                    .Distinct())
            }).ToList();

            day.Routes = calendarRoutes;
        }

        return routeCalendarModel;
    }

    /// <summary>
    /// Creates a route for a haulier driver.
    /// </summary>
    /// <param name="routeCreateModel"></param>
    /// <returns></returns>
    public async Task<RouteCreateModel> CreateRouteAsync(RouteCreateModel routeCreateModel)
    {
        routeCreateModel.Id = Guid.NewGuid();

        HaulierDriverModel driverDetails = null;

        if (routeCreateModel.DriverUserId != null)
        {
            driverDetails = await haulierService.GetDriverDetailsAsync(routeCreateModel.DriverUserId.Value);
        }

        var route = new Database.Route
        {
            Id = routeCreateModel.Id,
            TenantId = currentUserService.TenantId,

            RouteDate = routeCreateModel.RouteDate.Value.DateTime,
            CreatedDateTime = DateTime.Now,

            Status = (int)RouteStatus.Open,

            Name = driverDetails?.DriverName ?? string.Empty,
            HaulierId = driverDetails?.HaulierId ?? null,
            HaulierVehicleId = driverDetails?.HaulierVehicleId ?? null,
            HaulierDriverUserId = routeCreateModel?.DriverUserId ?? null,

            RouteOptimisedStatus = (int)RouteOptimisedStatus.None,
        };

        db.Routes.Add(route);
        await db.SaveChangesAsync();


        return routeCreateModel;
    }

    /// <summary>
    /// Gets a route.
    /// </summary>
    /// <param name="routeId"></param>
    /// <returns></returns>
    public async Task<HaulierRouteModel> GetRouteAsync(Guid routeId)
    {
        var userSettings = await currentUserService.GetUserSecurityAsync();

        await this.RefreshRouteLineNumbers(routeId);

        var routeModel = await db.Routes
            .AsNoTracking()
            .Where(i => i.Id == routeId)
            .Select(o => new HaulierRouteModel
            {
                Id = o.Id,
                Status = o.Status,
                RouteDate = o.RouteDate,
                Name = o.Name,
                IsReadOnly = userSettings.UserType != (int)UserType.Admin && userSettings.UserType != (int)UserType.SuperUser,

                HaulierId = o.HaulierId,
                HaulierVehicleId = o.HaulierVehicleId,
                HaulierVehicleDriverId = o.HaulierDriverUserId,
                HaulierVehicleRegistration = o.HaulierVehicle != null ? o.HaulierVehicle.Registration : string.Empty,
                HaulierVehicleDriverName = o.HaulierDriverUser != null ? o.HaulierDriverUser.FirstName + " " + o.HaulierDriverUser.Surname : string.Empty,
                HaulierName = o.Haulier != null ? o.Haulier.Name : string.Empty,
                
                RouteLines = o.RouteLines.OrderBy(o => o.StopNumber).Select(l => new HaulierRouteLineModel
                {
                    Id = l.Id,
                    StopNumber = l.StopNumber,
                    HaulierStopNumber = l.HaulierStop.StopNumber,
                    Time = l.HaulierStop.Time,
                    HaulierStopId = l.HaulierStopId,
                    TipStopId = l.HaulierStop.TipStopId,

                    Status = l.HaulierStop.Status,
                    OrderNumber = l.HaulierStop.OrderNumber,
                    ProofOfDeliveryId = l.HaulierStop.ProofOfDeliveryId,

                    DeliveryAddressLatitude = l.HaulierStop != null ? l.HaulierStop.Address.Latitude : null,
                    DeliveryAddressLongitude = l.HaulierStop != null ? l.HaulierStop.Address.Longitude : null,

                    DeliveryAddressLine1 = l.HaulierStop.Address.AddressLine1,
                    DeliveryAddressPostcode = l.HaulierStop.Address.AddressPostcode,
                    DeliveryAddressLine2 = l.HaulierStop.Address.AddressLine2,
                    DeliveryAddressLine3 = l.HaulierStop.Address.AddressLine3,
                    DeliveryAddressLine4 = l.HaulierStop.Address.AddressLine4,

                    PhoneNumber = l.HaulierStop.PhoneNumber,
                    MobileNumber = l.HaulierStop.MobilePhoneNumber,

                    Notes = l.HaulierStop.Notes,
                    PhotoRequired = l.HaulierStop.PhotoRequired,
                    SignatureRequired = l.HaulierStop.SignatureRequired,
                    SignedByName = l.HaulierStop.SignedByName,
                    Type = l.HaulierStop.Type,

                    ContractLinkId = l.HaulierStop.ContractLinkId,
                    ContractName = l.HaulierStop.ContractLink != null ? l.HaulierStop.ContractLink.Contract.Name : string.Empty,
                    CustomerProduct = l.HaulierStop.ContractLink != null ? $"{l.HaulierStop.ContractLink.Customer.Name} / {l.HaulierStop.ContractLink.Product.Name}" : string.Empty,
                    Tonnes = l.HaulierStop.Tonnes,
                    PricePerTonne = l.HaulierStop.PricePerTonne,
                    PricePerLoad = l.HaulierStop.PricePerLoad,
                    HaulierPrice = l.HaulierStop.HaulierPrice,

                    TareWeight = l.HaulierStop.TareWeight,
                    GrossWeight = l.HaulierStop.GrossWeight,

                    Photos = l.HaulierStop.HaulierStopFiles.Select(s => new HaulierRoutePhotoModel 
                    {
                        Id = s.Id,
                        Url = s.AzureStorageURL,
                        IsSignature = s.IsSignature,
                        SignatureName = s.SignatureName
                    }).ToList(),

                    ProductLines = l.HaulierStop.ProductLines.Select(s => new HaulierStopProductLineModel
                    {
                        Id = s.Id,
                        ProductName = s.ProductName,
                        Quantity = s.Quantity,
                        UnitPrice = s.UnitPrice,
                        VATRate = s.VATRate,
                        UnitTotal = s.UnitTotal,
                        VATTotal = s.VATTotal,
                        Total = s.Total,
                    }).ToList(),

                }).OrderBy(o => o.StopNumber).ToList(),

                RouteOptimisedStatus = o.RouteOptimisedStatus,
            })
            .FirstOrDefaultAsync();

        return routeModel;
    }

    /// <summary>
    /// Delete a route.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> DeleteRouteAsync(Guid id)
    {
        var response = new FormResponseModel<bool>(true);

        try
        {
            var route = await db.Routes
                    .Include(i => i.RouteCashAmounts)
                    .FirstOrDefaultAsync(r => r.Id == id);

            db.RouteCashAmounts.RemoveRange(route.RouteCashAmounts);
            db.Routes.Remove(route);

            await db.SaveChangesAsync();

        }
        catch (Exception)
        {
            response.AddError("Failed to delete route. Please check all route stops have been deleted.");
        }

        return response;
    }

    /// <summary>
    /// Delete visit from route.
    /// </summary>
    /// <param name="routeLineId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> DeleteStopOnRouteAsync(Guid routeLineId)
    {
        var routeLine = await db.RouteLines
                        .Include(i => i.HaulierStop)
                            .ThenInclude(i => i.HaulierStopFiles)
                        .Include(r => r.Route)
                        .Include(i => i.HaulierStop)
                            .ThenInclude(i => i.ProductLines)
                        .FirstOrDefaultAsync(s => s.Id == routeLineId);

        var parentStop = await db.HaulierStops.FirstOrDefaultAsync(w => w.TipStopId == routeLine.HaulierStop.Id);
        if (parentStop != null)
        {
            parentStop.TipStopId = null;
        }

        routeLine.Route.RouteOptimisedStatus = (int)RouteOptimisedStatus.None;

        db.HaulierStopProductLines.RemoveRange(routeLine.HaulierStop.ProductLines);
        db.HaulierStopFiles.RemoveRange(routeLine.HaulierStop.HaulierStopFiles);
        db.HaulierStops.Remove(routeLine.HaulierStop);
        db.RouteLines.Remove(routeLine);

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// List of routes.
    /// </summary>
    /// <param name="gridParametersModel"></param>
    /// <returns></returns>
    public async Task<GridResultModel<RouteListItemModel>> ListRoutesAsync(GridParametersModel gridParametersModel)
    {
        var userSecurity = await currentUserService.GetUserSecurityAsync();

        var q = db.Routes
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .AsQueryable();

        if (gridParametersModel.ParameterId != Guid.Empty)
        {
            q = q.Where(w => w.HaulierDriverUserId == gridParametersModel.ParameterId);
        }

        if (userSecurity.UserType == (int)UserType.Driver)
        {
            q = q.Where(w => w.HaulierDriverUserId == currentUserService.UserId);
        }

        var query = q.Select(o => new RouteListItemModel
        {
            Id = o.Id,
            Status = o.Status,
            RouteDate = o.RouteDate,
            Name = o.Name,
            Stops = o.RouteLines.Count,
            Vehicle = o.HaulierVehicle != null ? o.HaulierVehicle.Registration : string.Empty,
            VehicleDriver = o.HaulierDriverUser != null ? o.HaulierDriverUser.FirstName + " " + o.HaulierDriverUser.Surname : string.Empty,
        })
        .AsQueryable();

        return await GridHelper.CreateGridAsync(query, gridParametersModel);
    }

    /// <summary>
    /// List of routes.
    /// </summary>
    /// <param name="gridParametersModel"></param>
    /// <returns></returns>
    public async Task<List<RouteListItemModel>> ListTodaysRoutesAsync()
    {
        var routes = await db.Routes
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.RouteDate == DateTime.Today)
                   .Select(o => new RouteListItemModel
                   {
                       Id = o.Id,
                       Status = o.Status,
                       RouteDate = o.RouteDate,
                       Name = o.Name,
                       Stops = o.RouteLines.Count,
                       Vehicle = o.Vehicle.Registration,
                       AreaName = o.RouteArea.Name,
                       VehicleDriver = o.VehicleDriver.FirstName + " " + o.VehicleDriver.LastName,
                   })
                   .ToListAsync();

        return routes;
    }

    /// <summary>
    /// Update route details.
    /// </summary>
    /// <param name="routeModel"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> UpdateRouteDetailsAsync(RouteModel routeModel)
    {
        var route = await db.Routes.Where(i => i.Id == routeModel.Id).FirstOrDefaultAsync();

        route.Name = routeModel.HaulierVehicleDriverName;
        route.HaulierDriverUserId = routeModel.HaulierVehicleDriverId;
        route.HaulierId = routeModel.HaulierId;
        route.HaulierVehicleId = routeModel.HaulierVehicleId;
        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Updates the stop numbers on the route lines
    /// </summary>
    /// <param name="routeModel"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> UpdateRouteLinesOrder(HaulierRouteModel routeModel)
    {
        var route = await db.Routes.Where(w => w.TenantId == currentUserService.TenantId)
                                    .Where(w => w.Id == routeModel.Id)
                                    .Include(i => i.RouteLines)
                                    .FirstOrDefaultAsync();

        route.RouteOptimisedStatus = (int)RouteOptimisedStatus.Manual;

        foreach (var routeline in route.RouteLines)
        {
            routeline.StopNumber = routeModel.RouteLines.FirstOrDefault(s => s.Id == routeline.Id).StopNumber;
        }

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>();
    }

    /// <summary>
    /// Adds a order to route.
    /// </summary>
    /// <param name="routeId"></param>
    /// <param name="orderId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<Guid>> CreateStopAndAddToRouteAsync(HaulierStopModel haulierStopModel)
    {
        var response = new FormResponseModel<Guid>();

        try
        {
            var addressModel = new AddressModel
            {
                Id = Guid.NewGuid(),
                AddressLine1 = haulierStopModel.Address.AddressLine1,
                AddressLine2 = haulierStopModel.Address.AddressLine2,
                AddressLine3 = haulierStopModel.Address.AddressLine3,
                AddressLine4 = haulierStopModel.Address.AddressLine4,
                AddressPostcode = haulierStopModel.Address.AddressPostcode,
                Latitude = haulierStopModel.Address.Latitude,
                Longitude = haulierStopModel.Address.Longitude,
            };

            var addressId = await addressService.CreateUpdateAddressAsync(addressModel);

            int lastStopNumber = await db.HaulierStops
                .Where(s => s.TenantId == currentUserService.TenantId)
                .Where(s => s.Type != (int)HaulierStopType.TipStop)
                .Select(s => s.StopNumber ?? 0)
                .MaxAsync();

            var stop = new HaulierStop
            {
                Id = Guid.NewGuid(),
                Status = (int)HaulierStopStatus.Open,
                StopNumber = lastStopNumber + 1,
                OrderNumber = haulierStopModel.OrderNumber,
                TenantId = currentUserService.TenantId,
                ContractId = haulierStopModel.ContractId,
                ContractLinkId = haulierStopModel.ContractLinkId,
                RouteId = haulierStopModel.RouteId,
                Notes = haulierStopModel.Notes,
                CreatedDateTime = DateTime.Now,
                PhoneNumber = haulierStopModel.PhoneNumber,
                MobilePhoneNumber = haulierStopModel.MobilePhoneNumber,
                Time = haulierStopModel.Time,
                PhotoRequired = haulierStopModel.PhotoRequired,
                SignatureRequired = haulierStopModel.SignatureRequired,
                Type = haulierStopModel.Type,
                AddressId = addressId,
                Tonnes = haulierStopModel.Tonnes,
                PricePerTonne = haulierStopModel.PricePerTonne,
                PricePerLoad = haulierStopModel.PricePerLoad,
                HaulierPrice = haulierStopModel.HaulierPrice,
                TareWeight = haulierStopModel.TareWeight,
                GrossWeight = haulierStopModel.GrossWeight,
            };

            db.HaulierStops.Add(stop);

            db.RouteLines.Add(new RouteLine
            {
                Id = Guid.NewGuid(),
                TenantId = currentUserService.TenantId,

                RouteId = haulierStopModel.RouteId.Value,
                HaulierStopId = stop.Id,
                StopNumber = 999,
            });

            var routeData = await db.Routes.Where(w => w.Id == haulierStopModel.RouteId.Value).FirstOrDefaultAsync();
            routeData.RouteOptimisedStatus = (int)RouteOptimisedStatus.None;

            await db.SaveChangesAsync();

            if (haulierStopModel.ProductLines.Any())
            {
                await haulierRouteProductService.AddProductLinesAsync(stop.Id, haulierStopModel.ProductLines);
            }

            await this.RefreshRouteLineNumbers(haulierStopModel.RouteId.Value);

            if (haulierStopModel.TipAddress != null)
            {
                haulierStopModel.Type = (int)HaulierStopType.TipStop;
                haulierStopModel.Address = haulierStopModel.TipAddress;
                haulierStopModel.TipAddress = null;
                haulierStopModel.Time = string.Empty;
                haulierStopModel.PhotoRequired = false;
                haulierStopModel.SignatureRequired = false;
                haulierStopModel.PricePerLoad = 0;
                haulierStopModel.PricePerLoad = 0;
                haulierStopModel.Tonnes = 0;
                haulierStopModel.ProductLines.Clear();

                var tipResult = await this.CreateStopAndAddToRouteAsync(haulierStopModel);

                if (tipResult.Success)
                {
                    stop.TipStopId = tipResult.Data;
                    await db.SaveChangesAsync();
                }
                else
                {
                    response.AddError("Unable to add tip stop to route");
                }
            }

            response.Data = stop.Id;
        }
        catch (Exception)
        {
            response.AddError("Unable to add stop to route");
        }

        return response;
    }

    /// <summary>
    /// Get's the haulier stop details
    /// </summary>
    /// <param name="stopId"></param>
    /// <returns></returns>
    public async Task<HaulierStopModel> GetHaulierStopAsync(Guid haulierStopId)
    {
        var stopModel = await db.HaulierStops
            .AsNoTracking()
            .Where(i => i.Id == haulierStopId)
            .Select(s => new HaulierStopModel
            {
                Id = s.Id,
                Status = s.Status,
                StopNumber = s.StopNumber,
                OrderNumber = s.OrderNumber,
                ContractId = s.ContractId,
                ContractName = s.Contract.Name,
                Notes = s.Notes,
                AddressId = s.AddressId,
                Address = new AddressModel
                {
                    AddressLine1 = s.Address.AddressLine1,
                    AddressLine2 = s.Address.AddressLine2,
                    AddressLine3 = s.Address.AddressLine3,
                    AddressLine4 = s.Address.AddressLine4,
                    AddressPostcode = s.Address.AddressPostcode,
                    Longitude = s.Address.Longitude ?? 0,
                    Latitude = s.Address.Latitude ?? 0,
                },

                PhoneNumber = s.PhoneNumber,
                MobilePhoneNumber = s.MobilePhoneNumber,
                Time = s.Time,
                PhotoRequired = s.PhotoRequired,
                SignatureRequired = s.SignatureRequired,
                Type = s.Type,
                ContractLinkId = s.ContractLinkId,
                CustomerProduct = $"{s.ContractLink.Customer} / {s.ContractLink.Product}",
                Tonnes = s.Tonnes,
                PricePerTonne = s.PricePerTonne,
                PricePerLoad = s.PricePerLoad,
                HaulierPrice = s.HaulierPrice,
                TipStopId = s.TipStopId,
                TareWeight = s.TareWeight,
                GrossWeight = s.GrossWeight,
                ProductLines = s.ProductLines.Select(s => new HaulierStopProductLineModel
                {
                    Id = s.Id,
                    ProductName = s.ProductName,
                    Quantity = s.Quantity,
                    UnitPrice = s.UnitPrice,
                    VATRate = s.VATRate,
                    UnitTotal = s.UnitTotal,
                    VATTotal = s.VATTotal,
                    Total = s.Total,
                }).ToList(),
            })
            .FirstOrDefaultAsync();

        return stopModel;
    }

    public async Task<FormResponseModel<Guid>> UpdateHaulierStopAsync(HaulierStopModel haulierStopModel)
    {
        var response = new FormResponseModel<Guid>();

        try
        {
            var haulierStop = await db.HaulierStops
                .Include(i => i.Address)
                .Include(i => i.TipStop)
                .Include(i => i.ProofOfDelivery)
                .FirstAsync(i => i.Id == haulierStopModel.Id);

            haulierStop.Address.AddressLine1 = haulierStopModel.Address.AddressLine1;
            haulierStop.Address.AddressLine2 = haulierStopModel.Address.AddressLine2;
            haulierStop.Address.AddressLine3 = haulierStopModel.Address.AddressLine3;
            haulierStop.Address.AddressLine4 = haulierStopModel.Address.AddressLine4;
            haulierStop.Address.AddressPostcode = haulierStopModel.Address.AddressPostcode;
            haulierStop.Address.Latitude = haulierStopModel.Address.Latitude;
            haulierStop.Address.Longitude = haulierStopModel.Address.Longitude;

            haulierStop.OrderNumber = haulierStopModel.OrderNumber;
            haulierStop.ContractId = haulierStopModel.ContractId;
            haulierStop.Notes = haulierStopModel.Notes;
            haulierStop.PhoneNumber = haulierStopModel.PhoneNumber;
            haulierStop.MobilePhoneNumber = haulierStopModel.MobilePhoneNumber;
            haulierStop.Time = haulierStopModel.Time;
            haulierStop.PhotoRequired = haulierStopModel.PhotoRequired;
            haulierStop.SignatureRequired = haulierStopModel.SignatureRequired;
            haulierStop.Type = haulierStopModel.Type;
            haulierStop.ContractLinkId = haulierStopModel.ContractLinkId.Value;

            haulierStop.Tonnes = haulierStopModel.Tonnes;
            haulierStop.PricePerTonne = haulierStopModel.PricePerTonne;
            haulierStop.PricePerLoad = haulierStopModel.PricePerLoad;
            haulierStop.HaulierPrice = haulierStopModel.HaulierPrice;
            haulierStop.TareWeight = haulierStopModel.TareWeight;
            haulierStop.GrossWeight = haulierStopModel.GrossWeight;

            if (haulierStop.TipStop != null)
            {
                haulierStop.TipStop.ContractLinkId = haulierStopModel.ContractLinkId;
            }

            if (haulierStop.TipStopId == null && haulierStop.ProofOfDeliveryId != null)
            {
                haulierStop.ProofOfDelivery.GrossWeight = haulierStopModel.GrossWeight;
                haulierStop.ProofOfDelivery.TareWeight = haulierStopModel.TareWeight;
            }

            await db.SaveChangesAsync();

            await haulierRouteProductService.DeleteAllProductLinesAsync(haulierStop.Id);
            if (haulierStopModel.ProductLines.Any())
            {
                await haulierRouteProductService.AddProductLinesAsync(haulierStop.Id, haulierStopModel.ProductLines);
            }
        }
        catch (Exception)
        {
            response.AddError("Could not update the stop.");
        }

        return response;
    }


    /// <summary>
    /// Delete order from route.
    /// </summary>
    /// <param name="routeLineId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> DeleteStopOnRouteAsync(Guid routeId, Guid stopId)
    {
        var routeline = await db.RouteLines
                        .Include(i => i.Route)
                        .Where(w => w.RouteId == routeId)
                        .Where(w => w.HaulierStopId == stopId)
                        .FirstOrDefaultAsync();

        routeline.Route.RouteOptimisedStatus = (int)RouteOptimisedStatus.None;

        var stop = await db.HaulierStops.FirstOrDefaultAsync(o => o.Id == routeline.HaulierStopId);

        db.HaulierStops.Remove(stop);
        db.RouteLines.Remove(routeline);

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Reoragnises the route in optimum order.
    /// </summary>
    /// <param name="routeId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> OptimiseRoute(Guid routeId)
    {
        try
        {
            // Retrieve route lines with related entities
            var routeLines = await db.RouteLines
                .Include(rl => rl.HaulierStop)
                    .ThenInclude(hs => hs.Address)
                .Where(rl => rl.RouteId == routeId)
                .OrderBy(rl => rl.StopNumber)
                .ToListAsync();

            var routeLinesToProcess = routeLines.Skip(1).Take(routeLines.Count - 2).ToList();

            if (routeLinesToProcess == null || routeLinesToProcess.Count < 2)
            {
                return new FormResponseModel<bool>(false); // Ensure valid route lines
            }

            // Get first and last stops
            var firstStop = routeLines.FirstOrDefault();
            var lastStop = routeLines.LastOrDefault();

            if (firstStop?.HaulierStop?.Address == null || lastStop?.HaulierStop?.Address == null)
            {
                return new FormResponseModel<bool>(false); // Validate addresses
            }

            // Extract start and end coordinates
            string startLatitude = firstStop.HaulierStop.Address.Latitude?.ToString();
            string startLongitude = firstStop.HaulierStop.Address.Longitude?.ToString();
            string endLatitude = lastStop.HaulierStop.Address.Latitude?.ToString();
            string endLongitude = lastStop.HaulierStop.Address.Longitude?.ToString();

            // Prepare locations for optimization
            var locations = new List<RouteXLLocation>
            {
                new() { address = "start", lat = startLatitude, lng = startLongitude }
            };

            foreach (var routeLine in routeLinesToProcess)
            {
                var location = new RouteXLLocation
                {
                    address = routeLine.Id.ToString(),
                    lat = routeLine.HaulierStop.Address.Latitude?.ToString(),
                    lng = routeLine.HaulierStop.Address.Longitude?.ToString()
                };
                locations.Add(location);
            }

            locations.Add(new RouteXLLocation { address = "end", lat = endLatitude, lng = endLongitude });

            var routeOptimised1 = await routeXlService.GetOptimisedRoute(locations);

            foreach (var routeline in routeLines)
            {
                KeyValuePair<string, RouteStop> routeKey;

                if (routeline.StopNumber == 1)
                {
                    routeKey = routeOptimised1.FirstOrDefault(s => s.Value.name == "start");
                }
                else
                {
                    routeKey = routeline.StopNumber == routeLines.Count
                        ? routeOptimised1.FirstOrDefault(s => s.Value.name == "end")
                        : routeOptimised1.FirstOrDefault(s => s.Value.name == routeline.Id.ToString());
                }

                routeline.StopNumber = Convert.ToInt32(routeKey.Key);
            }

            var routeData = await db.Routes.FirstOrDefaultAsync(w => w.Id == routeId);
            routeData.RouteOptimisedStatus = (int)RouteOptimisedStatus.Automatic;

            await db.SaveChangesAsync();
        }
        catch (Exception)
        {

            return new FormResponseModel<bool>(false);
        }

        return new FormResponseModel<bool>();
    }

    /// <summary>
    /// Refresh stop numbers for Route.
    /// </summary>
    /// <returns></returns>
    public async Task RefreshRouteLineNumbers(Guid routeId)
    {
        var routeLines = await db.RouteLines
                        .Where(w => w.RouteId == routeId)
                        .ToListAsync();

        int stopNumber = 1;

        foreach (var routeLine in routeLines.OrderBy(o => o.StopNumber))
        {
            routeLine.StopNumber = stopNumber;
            stopNumber++;
        }

        await db.SaveChangesAsync();
    }

    public async Task<List<DropdownListItem>> ListContractCustomerProductsAsync(Guid contractId)
    {
        var customerProducts = await db.ContractLinks
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.ContractId == contractId)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = $"{s.Customer.Name} / {s.Product.Name}"
            })
            .ToListAsync();

        return customerProducts;
    }

    /// <summary>
    /// Delete visit from route.
    /// </summary>
    /// <param name="routeLineId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> MarkStopAsCompletedAsync(Guid stopId, bool createProofOfDelivery)
    {
        var stop = await db.HaulierStops.FirstOrDefaultAsync(s => s.Id == stopId);

        stop.Status = (int)HaulierStopStatus.Completed;
        stop.CompletedDateTime = DateTime.Now;

        await db.SaveChangesAsync();

        if (createProofOfDelivery)
        {
            var stopLine = await db.RouteLines
                .Where(w => w.TenantId == currentUserService.TenantId)
                .Where(w => w.HaulierStopId == stopId)
                .FirstOrDefaultAsync();

            _ = await proofOfDeliveryService.CreateProofOfDeliveryStopAsync(stopLine.Id);
        }

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Delete visit from route.
    /// </summary>
    /// <param name="routeLineId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> MarkTipStopAsCompletedAsync(Guid tipStopId, bool createProofOfDelivery)
    {
        var stop = await db.HaulierStops.FirstOrDefaultAsync(s => s.Id == tipStopId);

        stop.Status = (int)HaulierStopStatus.Completed;
        stop.CompletedDateTime = DateTime.Now;

        await db.SaveChangesAsync();

        if (createProofOfDelivery)
        {
            var stopLine = await db.RouteLines
                .Where(w => w.TenantId == currentUserService.TenantId)
                .Where(w => w.HaulierStopId == tipStopId)
                .FirstOrDefaultAsync();

            _ = await proofOfDeliveryService.CreateProofOfDeliveryTipStopAsync(stopLine.Id);
        }

        return new FormResponseModel<bool>(true);
    }


    public async Task<FormResponseModel<bool>> UpdateMobileViewDetailsAsync(HaulierRouteLineModel haulierRouteLineModel)
    {
        var stop = await db.HaulierStops.FirstOrDefaultAsync(s => s.Id == haulierRouteLineModel.HaulierStopId);

        stop.TareWeight = haulierRouteLineModel.TareWeight;
        stop.GrossWeight = haulierRouteLineModel.GrossWeight;
        stop.SignedByName = haulierRouteLineModel.SignedByName;

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    public async Task<FormResponseModel<bool>> DeletePhoto(Guid photoId)
    {
        var stop = await db.HaulierStopFiles.FirstOrDefaultAsync(s => s.Id == photoId);

        db.HaulierStopFiles.Remove(stop);

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }
}
