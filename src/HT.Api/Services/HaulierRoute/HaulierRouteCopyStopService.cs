﻿using HT.Api.Services.Common.Address;
using HT.Api.Services.Common.RouteXL;
using HT.Api.Services.Haulier;
using HT.Blazor.Models.Form;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Address;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.HaulierRoute;

public class HaulierRouteCopyStopService(
    DatabaseContext db,
    CurrentUserService currentUserService,
    AddressService addressService)
{
    public async Task<FormResponseModel<bool>> CopyStopAsync(Guid stopId, int copies)
    {
        var haulierStop = await db.HaulierStops
            .Include(i => i.Address)
            .Include(i => i.TipStop)
                .ThenInclude(ti => ti.Address)
            .Include(i => i.ProductLines)
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.Id == stopId)
            .FirstOrDefaultAsync();

        int routeStopNumber = await db.RouteLines
                        .Where(w => w.RouteId == haulierStop.RouteId)
                        .OrderBy(o => o.StopNumber)
                        .MaxAsync(m => m.StopNumber);

        int lastStopNumber = await db.HaulierStops
            .Where(s => s.TenantId == currentUserService.TenantId)
            .Where(s => s.Type != (int)HaulierStopType.TipStop)
            .Select(s => s.StopNumber ?? 0)
            .MaxAsync();

        for (int i = 0; i < copies; i++)
        {
            (routeStopNumber, lastStopNumber) = await CopyStop(haulierStop, routeStopNumber, lastStopNumber);
        }

        var routeData = await db.Routes.Where(w => w.Id == haulierStop.RouteId.Value).FirstOrDefaultAsync();
        routeData.RouteOptimisedStatus = (int)RouteOptimisedStatus.None;

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>();
    }

    private async Task<(int routeStopNumber, int lastStopNumber)> CopyStop(HaulierStop haulierStop, int routeStopNumber, int lastStopNumber)
    {
        lastStopNumber++;
        routeStopNumber++;

        var addressModel = new AddressModel
        {
            Id = Guid.NewGuid(),
            AddressLine1 = haulierStop.Address.AddressLine1,
            AddressLine2 = haulierStop.Address.AddressLine2,
            AddressLine3 = haulierStop.Address.AddressLine3,
            AddressLine4 = haulierStop.Address.AddressLine4,
            AddressPostcode = haulierStop.Address.AddressPostcode,
            Latitude = haulierStop.Address.Latitude.Value,
            Longitude = haulierStop.Address.Longitude.Value,
        };

        var addressId = await addressService.CreateUpdateAddressAsync(addressModel);

        var stop = new HaulierStop
        {
            Id = Guid.NewGuid(),
            Status = (int)HaulierStopStatus.Open,
            StopNumber = lastStopNumber,
            OrderNumber = haulierStop.OrderNumber,
            TenantId = currentUserService.TenantId,
            ContractId = haulierStop.ContractId,
            ContractLinkId = haulierStop.ContractLinkId,
            RouteId = haulierStop.RouteId,
            Notes = haulierStop.Notes,
            CreatedDateTime = DateTime.Now,
            PhoneNumber = haulierStop.PhoneNumber,
            MobilePhoneNumber = haulierStop.MobilePhoneNumber,
            Time = haulierStop.Time,
            PhotoRequired = haulierStop.PhotoRequired,
            SignatureRequired = haulierStop.SignatureRequired,
            Type = haulierStop.Type,
            AddressId = addressId,
            Tonnes = haulierStop.Tonnes,
            PricePerTonne = haulierStop.PricePerTonne,
            PricePerLoad = haulierStop.PricePerLoad,
            HaulierPrice = haulierStop.HaulierPrice,
        };

        db.HaulierStops.Add(stop);

        db.RouteLines.Add(new RouteLine
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,

            RouteId = haulierStop.RouteId.Value,
            HaulierStopId = stop.Id,
            StopNumber = routeStopNumber,
        });

        if (haulierStop.ProductLines.Any())
        {
            foreach (var line in haulierStop.ProductLines)
            {
                db.HaulierStopProductLines.Add(new HaulierStopProductLine
                {
                    Id = Guid.NewGuid(),
                    TenantId = currentUserService.TenantId,
                    HaulierStopId = stop.Id,
                    LineNumber = line.LineNumber,
                    ProductName = line.ProductName,
                    Quantity = line.Quantity,
                    UnitPrice = line.UnitPrice,
                    VATRate = line.VATRate,
                    UnitTotal = line.UnitTotal,
                    VATTotal = line.VATTotal,
                    Total = line.Total,
                });
            }
        }

        if (haulierStop.TipStopId != null)
        {
            (routeStopNumber, lastStopNumber) = await CopyTipStop(haulierStop, routeStopNumber, lastStopNumber);
        }

        return (routeStopNumber, lastStopNumber);
    }

    private async Task<(int routeStopNumber, int lastStopNumber)> CopyTipStop(HaulierStop haulierStop, int routeStopNumber, int lastStopNumber)
    {
        lastStopNumber++;
        routeStopNumber++;

        var tipAddressModel = new AddressModel
        {
            Id = Guid.NewGuid(),
            AddressLine1 = haulierStop.TipStop.Address.AddressLine1,
            AddressLine2 = haulierStop.TipStop.Address.AddressLine2,
            AddressLine3 = haulierStop.TipStop.Address.AddressLine3,
            AddressLine4 = haulierStop.TipStop.Address.AddressLine4,
            AddressPostcode = haulierStop.TipStop.Address.AddressPostcode,
            Latitude = haulierStop.TipStop.Address.Latitude.Value,
            Longitude = haulierStop.TipStop.Address.Longitude.Value,
        };

        var tipAddressId = await addressService.CreateUpdateAddressAsync(tipAddressModel);

        var tipStop = new HaulierStop
        {
            Id = Guid.NewGuid(),
            Status = (int)HaulierStopStatus.Open,
            StopNumber = lastStopNumber,
            OrderNumber = haulierStop.TipStop.OrderNumber,
            TenantId = currentUserService.TenantId,
            ContractId = haulierStop.TipStop.ContractId,
            ContractLinkId = haulierStop.TipStop.ContractLinkId,
            RouteId = haulierStop.TipStop.RouteId,
            Notes = haulierStop.TipStop.Notes,
            CreatedDateTime = DateTime.Now,
            PhoneNumber = haulierStop.TipStop.PhoneNumber,
            MobilePhoneNumber = haulierStop.TipStop.MobilePhoneNumber,
            Time = string.Empty,
            PhotoRequired = false,
            SignatureRequired = false,
            Type = (int)HaulierStopType.TipStop,
            AddressId = tipAddressId,
            Tonnes = 0,
            PricePerTonne = 0,
            PricePerLoad = 0,
            HaulierPrice = 0,
        };

        db.HaulierStops.Add(tipStop);

        db.RouteLines.Add(new RouteLine
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,

            RouteId = haulierStop.RouteId.Value,
            HaulierStopId = tipStop.Id,
            StopNumber = routeStopNumber,
        });
        return (routeStopNumber, lastStopNumber);
    }
}
