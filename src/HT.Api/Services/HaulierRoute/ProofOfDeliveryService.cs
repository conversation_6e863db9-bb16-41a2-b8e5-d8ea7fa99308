﻿using System;
using System.IO.Compression;
using HT.Api.Common.Extensions;
using HT.Api.Services.Common.Address;
using HT.Api.Services.Common.Email;
using HT.Api.Services.Common.PDF;
using HT.Api.Services.Common.RouteXL;
using HT.Api.Services.Haulier;
using HT.Blazor.Models.Form;
using HT.Database;
using HT.Shared.Common;
using HT.Shared.Constants;
using HT.Shared.Enums;
using HT.Shared.Models.HaulierRoute;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.HaulierRoute;

public class ProofOfDeliveryService(
    DatabaseContext db,
    CurrentUserService currentUserService,
    AddressService addressService,
    EmailService emailService,
    UrlToPdf urlToPdf)
{
    public async Task<FormResponseModel<Guid>> CreateProofOfDeliveryTipStopAsync(Guid stopLineId)
    {
        var response = new FormResponseModel<Guid>();

        var tipStopModel = await db.RouteLines
                            .Where(w => w.TenantId == currentUserService.TenantId)
                            .Where(w => w.Id == stopLineId)
                            .Select(s => new
                            {
                                StopId = s.HaulierStopId,
                                s.HaulierStop.TipStopId,
                                StopAddressId = s.HaulierStop.AddressId,
                                s.HaulierStop.TareWeight,
                                s.HaulierStop.GrossWeight,
                                s.HaulierStop.CompletedDateTime,
                            })
                            .FirstOrDefaultAsync();

            var mainStopModel = await db.RouteLines
                            .Where(w => w.TenantId == currentUserService.TenantId)
                            .Where(w => w.HaulierStop.TipStopId == tipStopModel.StopId)
                            .Select(s => new
                            {
                                StopId = s.HaulierStopId,
                                s.HaulierStop.ContractLink.ContractId,
                                ContractName = s.HaulierStop.ContractLink.Contract.Name,
                                CustomerName = s.HaulierStop.ContractLink.Customer.Name,
                                StopAddressId = s.HaulierStop.AddressId,
                                DriverName = $"{s.Route.HaulierVehicle.DriverUser.FirstName} {s.Route.HaulierVehicle.DriverUser.Surname}",
                                VehicleRegistration = s.Route.HaulierVehicle.Registration,
                                SignatureFile = s.HaulierStop.HaulierStopFiles
                                                .OrderByDescending(o => o.UploadedDate)
                                                .FirstOrDefault(w => w.IsSignature),
                                PhotoFile = s.HaulierStop.HaulierStopFiles.FirstOrDefault(w => !w.IsSignature),
                                s.HaulierStop.CompletedDateTime,
                                s.HaulierStop.OrderNumber,
                                s.HaulierStop.ContractLink.Product.EWCCode,
                                ProductName = s.HaulierStop.ContractLink.Product.Name,
                            })
                            .FirstOrDefaultAsync();

        if (tipStopModel == null || mainStopModel == null)
        {
            response.AddError("Unable to create proof of delivery");
            return response;
        }
        
        var proofOfDelivery = new ProofOfDelivery
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            OrderNumber = mainStopModel.OrderNumber,
            ContractId = mainStopModel.ContractId,
            ContractName = mainStopModel.ContractName,
            CustomerName = mainStopModel.CustomerName,
            SignedByName = mainStopModel.SignatureFile?.SignatureName,
            SignatureFileId = mainStopModel.SignatureFile?.Id,
            PhotoFileId = mainStopModel.PhotoFile?.Id,
            StopCompletedDate = mainStopModel.CompletedDateTime,
            TipStopCompletedDate = tipStopModel.CompletedDateTime,
            StopAddressId = mainStopModel.StopAddressId,
            TipStopAddressId = tipStopModel.StopAddressId,
            TareWeight = tipStopModel.TareWeight,
            GrossWeight = tipStopModel.GrossWeight,
            VehicleRegistration = mainStopModel.VehicleRegistration,
            DriverName = mainStopModel.DriverName,
            EWCCode = mainStopModel.EWCCode,
            ProductName = mainStopModel.ProductName,
        };

        db.ProofOfDeliveries.Add(proofOfDelivery);

        var mainStop = await db.HaulierStops.FirstOrDefaultAsync(s => s.Id == mainStopModel.StopId);
        var tipStop = await db.HaulierStops.FirstOrDefaultAsync(s => s.Id == tipStopModel.StopId);

        mainStop.ProofOfDeliveryId = proofOfDelivery.Id;
        tipStop.ProofOfDeliveryId = proofOfDelivery.Id;

        await db.SaveChangesAsync();

        try
        {
            await SendProofOfDeliveryEmail(proofOfDelivery.Id);
        }
        catch (Exception)
        {
        }
        

        response.Data = proofOfDelivery.Id;
        return response;
    }

    public async Task<FormResponseModel<Guid>> CreateProofOfDeliveryStopAsync(Guid stopLineId)
    {
        var response = new FormResponseModel<Guid>();

        var mainStopModel = await db.RouteLines
                        .Where(w => w.TenantId == currentUserService.TenantId)
                        .Where(w => w.Id == stopLineId)
                        .Select(s => new
                        {
                            StopId = s.HaulierStopId,
                            s.HaulierStop.ContractLink.ContractId,
                            ContractName = s.HaulierStop.ContractLink.Contract.Name,
                            CustomerName = s.HaulierStop.ContractLink.Customer.Name,
                            StopAddressId = s.HaulierStop.AddressId,
                            DriverName = $"{s.Route.HaulierVehicle.DriverUser.FirstName} {s.Route.HaulierVehicle.DriverUser.Surname}",
                            VehicleRegistration = s.Route.HaulierVehicle.Registration,
                            SignatureFile = s.HaulierStop.HaulierStopFiles.FirstOrDefault(w => w.IsSignature),
                            PhotoFile = s.HaulierStop.HaulierStopFiles.FirstOrDefault(w => !w.IsSignature),
                            s.HaulierStop.CompletedDateTime,
                            s.HaulierStop.OrderNumber,
                            s.HaulierStop.ContractLink.Product.EWCCode,
                            ProductName = s.HaulierStop.ContractLink.Product.Name,
                            s.HaulierStop.TareWeight,
                            s.HaulierStop.GrossWeight,
                        })
                        .FirstOrDefaultAsync();

        if (mainStopModel == null)
        {
            response.AddError("Unable to create proof of delivery");
            return response;
        }

        var proofOfDelivery = new ProofOfDelivery
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            OrderNumber = mainStopModel.OrderNumber,
            ContractId = mainStopModel.ContractId,
            ContractName = mainStopModel.ContractName,
            CustomerName = mainStopModel.CustomerName,
            SignedByName = mainStopModel.SignatureFile?.SignatureName,
            SignatureFileId = mainStopModel.SignatureFile?.Id,
            PhotoFileId = mainStopModel.PhotoFile?.Id,
            StopCompletedDate = mainStopModel.CompletedDateTime,
            StopAddressId = mainStopModel.StopAddressId,
            TareWeight = mainStopModel.TareWeight,
            GrossWeight = mainStopModel.GrossWeight,
            VehicleRegistration = mainStopModel.VehicleRegistration,
            DriverName = mainStopModel.DriverName,
            EWCCode = mainStopModel.EWCCode,
            ProductName = mainStopModel.ProductName,
        };

        db.ProofOfDeliveries.Add(proofOfDelivery);

        var mainStop = await db.HaulierStops.FirstOrDefaultAsync(s => s.Id == mainStopModel.StopId);
        mainStop.ProofOfDeliveryId = proofOfDelivery.Id;

        await db.SaveChangesAsync();

        await SendProofOfDeliveryEmail(proofOfDelivery.Id);

        response.Data = proofOfDelivery.Id;
        return response;
    }

    public async Task SendProofOfDeliveryEmail(Guid podId)
    {
        var pod = await db.ProofOfDeliveries
             .Where(p => p.Id == podId)
             .Select(s => new
             {
                 s.TenantId,
                 s.OrderNumber,
                 Date = s.TipStopCompletedDate.Value.ToString("F"),
                 CompanyName = s.Contract.SitePermit.Site.Name,
                 s.Contract.SiteEmail,
             })
            .FirstOrDefaultAsync();

        if (string.IsNullOrWhiteSpace(pod.SiteEmail))
        {
            return;
        }

        var dataReplacements = new Dictionary<string, string>
        {
            { "OrderNumber", pod.OrderNumber ?? string.Empty },
            { "Date", pod.Date ?? string.Empty },
            { "CompanyName", pod.CompanyName ?? string.Empty },
        };

        var template = await db.EmailTemplates.FirstOrDefaultAsync(w => w.Type == (int)EmailType.ProofOfDelivery);

        string subject = template.Subject.ReplaceWithDictionaryKeys(dataReplacements);
        string textBody = template.TextTemplate.ReplaceWithDictionaryKeys(dataReplacements);
        string htmlBody = template.HtmlTemplate.ReplaceWithDictionaryKeys(dataReplacements);

        var email = new HT.Database.Email
        {
            Id = Guid.NewGuid(),
            TenantId = pod.TenantId,
            Status = (int)EmailStatus.Queued,
            CreatedDate = DateTime.Now,
            From = "<EMAIL>",
            To = pod.SiteEmail,
            CC = string.Empty,
            HtmlTemplate = htmlBody,
            TextTemplate = textBody,
            Subject = subject,
            PostmarkClientServerKey = Constants.PostmarkClientServerKey,
        };
        db.Emails.Add(email);

        var emailAttachment = new HT.Database.EmailAttachment
        {
            Id = Guid.NewGuid(),
            EmailId = email.Id,
            FileContentType = "application/pdf",
            FileName = $"ProofOfDelivery-{pod.OrderNumber}.pdf",
            FileUrl = $"https://app-weigh-technology-api.azurewebsites.net/ProofOfDelivery/PrintPdf?id={podId}&orderNumber={pod.OrderNumber}"
        };
        db.EmailAttachments.Add(emailAttachment);

        await db.SaveChangesAsync();

        await emailService.SendEmail(email.Id);
    }

    public async Task<ProofOfDeliveryModel> GetProofOfDeliveryAsync(Guid id)
    {
        var pod = await db.ProofOfDeliveries
            //.Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.Id == id)
            .Select(s => new ProofOfDeliveryModel
            {
                OrderNumber = s.OrderNumber,
                ContractName = s.ContractName,
                CustomerName = s.CustomerName,
                SignedByName = s.SignedByName,
                StopCompletedDate = s.StopCompletedDate,
                TipStopCompletedDate = s.TipStopCompletedDate,
                StopAddressId = s.StopAddressId,
                TipStopAddressId = s.TipStopAddressId,
                TareWeight = s.TareWeight,
                GrossWeight = s.GrossWeight,
                VehicleRegistration = s.VehicleRegistration,
                DriverName = s.DriverName,
                ProductName = s.ProductName,
                EWCCode = s.EWCCode,
                SignatureUrl = s.SignatureFileId.HasValue  
                    ? s.SignatureFile.AzureStorageURL
                    : null,
            })
            .FirstAsync();

        pod.StopAddress = await addressService.GetAddressAsync(pod.StopAddressId);
        pod.TipStopAddress = await addressService.GetAddressAsync(pod.TipStopAddressId);

        var haulierStop = await db.HaulierStops
            .Include(i => i.HaulierStopFiles)
            .Where(w => w.ProofOfDeliveryId == id)
            .Where(w => w.Type != (int)HaulierStopType.TipStop)
            .FirstOrDefaultAsync();

        pod.PhotoUrls = haulierStop.HaulierStopFiles
                    .Where(f => !f.IsSignature)
                    .Select(s => s.AzureStorageURL)
                    .ToList();

        pod.StopNumber = haulierStop?.StopNumber;

        return pod;
    }

    public async Task<byte[]> GenerateBulkPdfZipAsync(List<Guid> proofOfDeliveryIds)
    {
        using var memoryStream = new MemoryStream();
        using var zipArchive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true);

        // Get order numbers for all proof of deliveries to use in file naming
        var proofOfDeliveries = await db.ProofOfDeliveries
            .Where(p => proofOfDeliveryIds.Contains(p.Id))
            .Select(p => new { p.Id, p.OrderNumber })
            .ToListAsync();

        // Generate PDFs in parallel for better performance
        var pdfTasks = proofOfDeliveries.Select(async pod =>
        {
            try
            {
                string url = $"https://app-weigh-technology.azurewebsites.net/ProofOfDeliveryPrint/{pod.Id}";
                byte[] pdfBytes = await urlToPdf.BuildPdf(url);
                
                return new
                {
                    pod.Id,
                    pod.OrderNumber,
                    PdfBytes = pdfBytes,
                    Success = true
                };
            }
            catch (Exception)
            {
                return new
                {
                    pod.Id,
                    pod.OrderNumber,
                    PdfBytes = (byte[])null,
                    Success = false
                };
            }
        });

        var pdfResults = await Task.WhenAll(pdfTasks);

        // Add successful PDFs to the ZIP archive
        foreach (var result in pdfResults.Where(r => r.Success))
        {
            string fileName = $"ProofOfDelivery-{result.OrderNumber ?? result.Id.ToString()}.pdf";
            var zipEntry = zipArchive.CreateEntry(fileName);
            
            using var entryStream = zipEntry.Open();
            await entryStream.WriteAsync(result.PdfBytes, 0, result.PdfBytes.Length);
        }

        zipArchive.Dispose();
        return memoryStream.ToArray();
    }
}
