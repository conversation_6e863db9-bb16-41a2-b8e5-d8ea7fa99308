﻿using HT.Api.Common.Extensions;
using HT.Api.Services.Common.Address;
using HT.Api.Services.Common.RouteXL;
using HT.Api.Services.Customer;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using HT.Shared.Models.Address;
using HT.Shared.Models.Route;
using HT.Shared.Models.Route.Calendar;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Route;

public class RouteService(
    DatabaseContext db,
    CurrentUserService currentUserService,
    AddressService addressService,
    CustomerPaymentService customerLedgerService,
    RouteXlService routeXlService)
{
    /// <summary>
    /// Creates a route.
    /// </summary>
    /// <param name="routeCreateModel"></param>
    /// <returns></returns>
    public async Task<RouteCreateModel> CreateRouteAsync(RouteCreateModel routeCreateModel)
    {
        routeCreateModel.Id = Guid.NewGuid();
        var route = new Database.Route
        {
            Id = routeCreateModel.Id,
            TenantId = currentUserService.TenantId,

            RouteDate = routeCreateModel.RouteDate.Value.DateTime,
            CreatedDateTime = DateTime.Now,

            Status = (int)RouteStatus.Open,

            Name = routeCreateModel.Name,
            VehicleId = routeCreateModel.VehicleId,
            VehicleDriverId = routeCreateModel.VehicleDriverId,
            RouteAreaId = routeCreateModel.RouteAreaId,

            RouteOptimisedStatus = (int)RouteOptimisedStatus.None,
        };

        var routeArea = await db.RouteAreas.Where(s => s.Id == routeCreateModel.RouteAreaId).Select(s => new
        {
            StartAddressLine1 = s.StartAddress.AddressLine1,
            StartAddressLine2 = s.StartAddress.AddressLine2,
            StartAddressLine3 = s.StartAddress.AddressLine3,
            StartAddressLine4 = s.StartAddress.AddressLine4,
            StartAddressPostcode = s.StartAddress.AddressPostcode,
            StartAddressLatitude = s.StartAddress.Latitude,
            StartAddressLongitude = s.StartAddress.Longitude,

            EndAddressLine1 = s.EndAddress.AddressLine1,
            EndAddressLine2 = s.EndAddress.AddressLine2,
            EndAddressLine3 = s.EndAddress.AddressLine3,
            EndAddressLine4 = s.EndAddress.AddressLine4,
            EndAddressPostcode = s.EndAddress.AddressPostcode,
            EndAddressLatitude = s.EndAddress.Latitude,
            EndAddressLongitude = s.EndAddress.Longitude,

        }).FirstOrDefaultAsync();

        route.StartAddressId = await addressService.CreateUpdateAddressAsync(new Shared.Models.Address.AddressModel
        {
            AddressLine1 = routeArea.StartAddressLine1,
            AddressLine2 = routeArea.StartAddressLine2,
            AddressLine3 = routeArea.StartAddressLine3,
            AddressLine4 = routeArea.StartAddressLine4,
            AddressPostcode = routeArea.StartAddressPostcode,

            Latitude = (decimal)routeArea.StartAddressLatitude,
            Longitude = (decimal)routeArea.StartAddressLongitude,
        });

        route.EndAddressId = await addressService.CreateUpdateAddressAsync(new Shared.Models.Address.AddressModel
        {
            AddressLine1 = routeArea.EndAddressLine1,
            AddressLine2 = routeArea.EndAddressLine2,
            AddressLine3 = routeArea.EndAddressLine3,
            AddressLine4 = routeArea.EndAddressLine4,
            AddressPostcode = routeArea.EndAddressPostcode,

            Latitude = (decimal)routeArea.EndAddressLatitude,
            Longitude = (decimal)routeArea.EndAddressLongitude,
        });

        db.Routes.Add(route);
        await db.SaveChangesAsync();


        return routeCreateModel;
    }

    /// <summary>
    /// Gets a route.
    /// </summary>
    /// <param name="routeId"></param>
    /// <returns></returns>
    public async Task<RouteModel> GetRouteAsync(
        Guid routeId,
        bool includeCustomerBalance,
        bool includeCustomersOwing)
    {
        await this.RefreshRouteProducts(routeId);

        await CheckCashAmounts(routeId);

        var routeModel = await db.Routes
            .AsNoTracking()
            .Where(i => i.Id == routeId)
            .Select(o => new RouteModel
            {
                Id = o.Id,
                Status = o.Status,
                RouteDate = o.RouteDate,
                Name = o.Name,
                VehicleId = o.VehicleId,
                VehicleDriverId = o.VehicleDriverId,
                Vehicle = o.Vehicle != null ? o.Vehicle.Registration : string.Empty,
                VehicleCapacity = o.Vehicle != null ? o.Vehicle.Capacity : 0,
                VehicleDriver = o.VehicleDriver != null ? o.VehicleDriver.FirstName + " " + o.VehicleDriver.LastName : string.Empty,
                RouteArea = o.RouteArea.Name,
                RouteAreaId = o.RouteAreaId,

                RouteLines = o.RouteLines.Select(l => new RouteLineModel
                {
                    Id = l.Id,
                    CustomerId = l.Order != null ? l.Order.CustomerId : l.Visit.CustomerId,
                    StopNumber = l.StopNumber,

                    OrderDeliveryNotes = l.Order != null ? l.Order.OrderDeliveryNotes : l.Visit.OrderDeliveryNotes,

                    OrderId = l.OrderId,
                    OrderNumber = l.Order != null ? l.Order.OrderNumber : null,
                    RoundOrder = l.Order != null && l.Order.RoundOrder,

                    VisitId = l.VisitId,

                    DeliveryAddressLatitude = l.Order != null ? l.Order.DeliveryAddress.Latitude : null,
                    DeliveryAddressLongitude = l.Order != null ? l.Order.DeliveryAddress.Longitude : null,

                    Products = l.Order != null && l.Order.OrderLines != null ? l.Order.OrderLines.Select(ol => new RouteLineProductModel
                    {
                        ProductCode = ol.Product != null ? ol.Product.ProductCode : string.Empty,
                        Name = ol.Product != null ? ol.Product.Name : string.Empty,
                        QuantityTotal = (int)ol.Quantity,
                        UnitWeight = ol.Product != null ? ol.Product.ProductWeightKilograms : 0,
                    }).OrderBy(o => o.Name).ToList() : new(),

                    Total = l.Order != null ? l.Order.Total : 0,

                    DeliveryStatus = l.VisitId != null ? (int)RouteLineDeliveryStatus.VisitOnly : l.DeliveryStatus,
                    PaymentType = l.PaymentType,
                    PaymentStatus = l.PaymentStatus,
                    PaymentAmount = l.PaymentAmount,
                }).OrderBy(o => o.StopNumber).ToList(),

                Products = o.RouteProducts.Select(p => new RouteLineProductModel
                {
                    Id = p.Id,

                    ProductId = p.ProductId,
                    ProductCode = p.Product.ProductCode,
                    Name = p.Product.Name,

                    QuantityFromOrders = p.QuantityFromOrders,
                    QuantityFromExtras = p.QuantityFromExtras,
                    QuantityTotal = p.QuantityTotal,

                    UnitWeight = p.Product.ProductWeightKilograms,
                }).OrderBy(o => o.Name).ToList(),

                StartAddressId = o.StartAddressId,
                EndAddressId = o.EndAddressId,
                RouteOptimisedStatus = o.RouteOptimisedStatus,

                CashAmounts = o.RouteCashAmounts
                            .Select(c => new RouteCashAmountModel
                            {
                                Id = c.Id,
                                RouteId = c.RouteId,
                                Amount = c.Amount,
                                Type = c.Type,
                                Display = c.Display,
                                Reason = c.Reason
                            })
                            .ToList()

            })
            .FirstOrDefaultAsync();

        // Get the future round data and customer data

        var customerIds = routeModel.RouteLines.Select(s => s.CustomerId).ToList();

        var customers = await db.Customers
                        .Include(i => i.CustomerTagLinks)
                            .ThenInclude(i => i.CustomerTag)
                        .Include(i => i.Orders)
                            .ThenInclude(i => i.OrderLines)
                        .Where(w => w.TenantId == currentUserService.TenantId)
                        .Where(w => customerIds.Contains(w.Id))
                        .Select(c => new
                        {
                            Customer = c,
                            c.CustomerAddress,
                            c.RoundNotes,
                            c.RouteAreaId,
                            c.FirstRoundOrderDate,
                            c.OrderFrequencyDays,
                            c.OnStop,
                            LatestOrder = c.Orders
                                .Where(o => o.Status != (int)OrderStatus.Cancelled)
                                .OrderByDescending(o => o.OrderDate)
                                .FirstOrDefault(),
                            LatestRoundOrder = c.Orders
                                .Where(o => o.Status != (int)OrderStatus.Cancelled)
                                .Where(o => o.RoundOrder)
                                .OrderByDescending(o => o.OrderDate)
                                .FirstOrDefault(),
                            FutureOrders = c.Orders
                                        .Where(o => o.Status != (int)OrderStatus.Cancelled)
                                        .Where(w => w.Route != null)
                                        .Where(w => w.Route.RouteDate >= routeModel.RouteDate)
                                        .Select(s => new DateIdItemModel { Date = s.Route.RouteDate, Id = s.Id })
                                        .ToList(),
                            FutureVisits = c.Visits
                                        .Where(w => w.VisitDate >= routeModel.RouteDate)
                                        .Select(s => new DateIdItemModel { Date = s.Route.RouteDate, Id = s.Id })
                                        .ToList(),
                        })
                        .ToListAsync();

        foreach (var line in routeModel.RouteLines)
        {
            var customerModel = customers.First(c => c.Customer.Id == line.CustomerId);

            // Get customer details

            line.AddressName = customerModel.Customer.AddressName ?? string.Empty;
            line.DeliveryAddressLine1 = customerModel.CustomerAddress.AddressLine1;
            line.DeliveryAddressPostcode = customerModel.CustomerAddress.AddressPostcode;
            line.DeliveryAddressLine2 = customerModel.CustomerAddress.AddressLine2;
            line.DeliveryAddressLine3 = customerModel.CustomerAddress.AddressLine3;
            line.DeliveryAddressLine4 = customerModel.CustomerAddress.AddressLine4;

            line.PhoneNumber = customerModel.Customer.PhoneNumber;
            line.MobileNumber = customerModel.Customer.MobilePhoneNumber;
            line.CustomerTags = customerModel.Customer.CustomerTagLinks.Select(ctl => ctl.CustomerTag.Name).ToList();
            line.CustomerDeliveryNotes = customerModel.Customer.DeliveryNotes;
            line.RoundNotes = customerModel.RoundNotes;
            line.OnStop = customerModel.OnStop;

            line.OrderFrequencyDays = customerModel.Customer.OrderFrequencyDays;

            line.DeliveryAddressLatitude ??= customerModel.CustomerAddress.Latitude;

            line.DeliveryAddressLongitude ??= customerModel.CustomerAddress.Longitude;

            var dateToAdd = customerModel.FirstRoundOrderDate;

            if (dateToAdd == null)
            {
                continue;
            }

            // Loop and add the frequency to the dateToAdd until it is the nearest past date to today.

            while (dateToAdd <= routeModel.RouteDate)
            {
                dateToAdd = dateToAdd.Value.AddDays(line.OrderFrequencyDays);
            }

            var roundDates = new List<DateTime>();

            if (line.OrderFrequencyDays != 0 && line.CustomerTags.Contains("Rounds") && line.RoundOrder)
            {
                for (int i = 0; i < 5; i++)
                {
                    roundDates.Add(dateToAdd.Value.Date);
                    dateToAdd = dateToAdd.Value.AddDays(line.OrderFrequencyDays);
                }

                foreach (var roundDate in roundDates.OrderBy(o => o))
                {
                    var order = customerModel.FutureOrders.FirstOrDefault(o => o.Date == roundDate);
                    var visit = customerModel.FutureVisits.FirstOrDefault(o => o.Date == roundDate);

                    var futureRoundItemModel = new RouteFutureRoundItemModel
                    {
                        Date = roundDate,
                        CustomerId = (Guid)line.CustomerId,
                        DefaultRouteId = customerModel.RouteAreaId,
                        RouteOrderId = line.OrderId,
                        FutureOrderId = order?.Id,
                        FutureVisitId = visit?.Id,
                        LastOrderId = customerModel.LatestRoundOrder != null ? customerModel.LatestRoundOrder.Id : customerModel.LatestOrder?.Id,

                        AdditionalOrders = customerModel.FutureOrders
                                    .Where(o => o.Date > roundDate)
                                    .Where(o => o.Date < roundDate.AddDays(customerModel.OrderFrequencyDays))
                                    .Select(s => new AdditionalOrderModel { Date = s.Date, OrderId = s.Id })
                                    .OrderBy(o => o.Date)
                                    .ToList(),
                    };

                    if (futureRoundItemModel.AdditionalOrders.Any() && futureRoundItemModel.FutureOrderId != null)
                    {
                        futureRoundItemModel.AdditionalOrders.Add(new AdditionalOrderModel
                        {
                            OrderId = futureRoundItemModel.FutureOrderId.Value,
                            Date = roundDate
                        });
                    }

                    line.FutureRoundItemModels.Add(futureRoundItemModel);
                }
            }
        }


        // See if we can delete this. I don't think it's used.
        if (includeCustomersOwing)
        {
            var routeCustomers = await db.Customers
            .Include(i => i.Orders.Where(o => o.Status != (int)OrderStatus.Cancelled))
                .ThenInclude(i => i.Route)
            .Include(i => i.CustomerPayments)
            .Include(i => i.CustomerAddress)
            .Where(c => c.RouteAreaId == routeModel.RouteAreaId)
            .ToListAsync();

            routeModel.RouteCustomersOwing = routeCustomers
                            .Where(c => c.Orders.Sum(s => s.Total) > c.CustomerPayments.Sum(s => s.Total))
                            .Where(c => c.Orders
                                    .Any(o => o.RouteId == null || o.Route.RouteDate.Date <= DateTime.Today.Date)
                                    )
                            .Select(s => new RouteCustomersOwingModel
                            {
                                CustomerId = s.Id,
                                CustomerName = s.Name,
                                Address = s.CustomerAddress != null
                                        ? new Shared.Models.Address.AddressModel
                                        {
                                            AddressLine1 = s.CustomerAddress.AddressLine1,
                                            AddressLine2 = s.CustomerAddress.AddressLine2,
                                            AddressLine3 = s.CustomerAddress.AddressLine3,
                                            AddressLine4 = s.CustomerAddress.AddressLine4,
                                            AddressPostcode = s.CustomerAddress.AddressPostcode
                                        }
                                        : new Shared.Models.Address.AddressModel(),
                                LastDeliveryDate = s.Orders
                                                    .Where(o => o.RouteId != null)
                                                    .Where(o => o.Route.RouteDate < DateTime.Today)
                                                    .OrderByDescending(o => o.Route.RouteDate)
                                                    .Select(s => s.Route.RouteDate)
                                                    .FirstOrDefault(),
                                OrdersTotal = s.Orders.Sum(s => s.Total),
                                PaymentsTotal = s.CustomerPayments.Sum(sum => sum.Total)
                            })
                            .ToList();
        }


        routeModel.StartAddress = await addressService.GetAddressAsync(routeModel.StartAddressId);
        routeModel.EndAddress = await addressService.GetAddressAsync(routeModel.EndAddressId);

        if (routeModel.Status == (int)RouteStatus.DriverReturned || includeCustomerBalance)
        {
            foreach (var routeLine in routeModel.RouteLines.Where(s => s.CustomerId != null))
            {
                var customerLedger = await customerLedgerService.GetCustomerLedger(routeLine.CustomerId.Value, routeModel.RouteDate);
                routeLine.CustomerBalance = customerLedger.OrdersTodayAndBeforeBalance;
            }
        }

        return routeModel;
    }

    public async Task<RouteModel> GetRouteLoadsPrintAsync(Guid routeId)
    {
        await this.RefreshRouteProducts(routeId);

        var routeModel = await db.Routes
            .AsNoTracking()
            .Where(i => i.Id == routeId)
            .Select(o => new RouteModel
            {
                Id = o.Id,
                Status = o.Status,
                RouteDate = o.RouteDate,
                Name = o.Name,
                VehicleId = o.VehicleId,
                VehicleDriverId = o.VehicleDriverId,
                Vehicle = o.Vehicle != null ? o.Vehicle.Registration : string.Empty,
                VehicleCapacity = o.Vehicle != null ? o.Vehicle.Capacity : 0,
                VehicleDriver = o.VehicleDriver != null ? o.VehicleDriver.FirstName + " " + o.VehicleDriver.LastName : string.Empty,
                RouteArea = o.RouteArea.Name,
                RouteAreaId = o.RouteAreaId,

                RouteLines = o.RouteLines.Select(l => new RouteLineModel
                {
                    Id = l.Id,
                    StopNumber = l.StopNumber,
                }).OrderBy(o => o.StopNumber).ToList(),

                Products = o.RouteProducts.Select(p => new RouteLineProductModel
                {
                    Id = p.Id,

                    ProductId = p.ProductId,
                    ProductCode = p.Product.ProductCode,
                    Name = p.Product.Name,

                    QuantityFromOrders = p.QuantityFromOrders,
                    QuantityFromExtras = p.QuantityFromExtras,
                    QuantityTotal = p.QuantityTotal,

                    UnitWeight = p.Product.ProductWeightKilograms,
                }).OrderBy(o => o.Name).ToList(),
            })
            .FirstOrDefaultAsync();

        return routeModel;
    }

    public async Task<RouteModel> GetRouteOwingsPrintAsync(Guid routeId)
    {
        var routeModel = await db.Routes
            .AsNoTracking()
            .Where(i => i.Id == routeId)
            .Select(o => new RouteModel
            {
                Id = o.Id,
                Status = o.Status,
                RouteDate = o.RouteDate,
                Name = o.Name,
                VehicleId = o.VehicleId,
                VehicleDriverId = o.VehicleDriverId,
                Vehicle = o.Vehicle != null ? o.Vehicle.Registration : string.Empty,
                VehicleCapacity = o.Vehicle != null ? o.Vehicle.Capacity : 0,
                VehicleDriver = o.VehicleDriver != null ? o.VehicleDriver.FirstName + " " + o.VehicleDriver.LastName : string.Empty,
                RouteArea = o.RouteArea.Name,
                RouteAreaId = o.RouteAreaId,

                RouteLines = o.RouteLines.Select(l => new RouteLineModel
                {
                    Id = l.Id,
                    StopNumber = l.StopNumber,
                }).OrderBy(o => o.StopNumber).ToList(),
            })
            .FirstOrDefaultAsync();

        var routeCustomers = await db.Customers
            .Include(i => i.Orders.Where(o => o.Status != (int)OrderStatus.Cancelled))
                .ThenInclude(i => i.Route)
            .Include(i => i.CustomerPayments)
            .Include(i => i.CustomerAddress)
            .Where(c => c.RouteAreaId == routeModel.RouteAreaId)
            .ToListAsync();

        routeModel.RouteCustomersOwing = routeCustomers
                        .Where(c => c.Orders.Sum(s => s.Total) > c.CustomerPayments.Sum(s => s.Total))
                        .Where(c => c.Orders
                                .Any(o => o.RouteId == null || o.Route.RouteDate.Date <= DateTime.Today.Date)
                                )
                        .Select(s => new RouteCustomersOwingModel
                        {
                            CustomerId = s.Id,
                            CustomerName = s.Name,
                            Address = s.CustomerAddress != null
                                    ? new Shared.Models.Address.AddressModel
                                    {
                                        AddressLine1 = s.CustomerAddress.AddressLine1,
                                        AddressLine2 = s.CustomerAddress.AddressLine2,
                                        AddressLine3 = s.CustomerAddress.AddressLine3,
                                        AddressLine4 = s.CustomerAddress.AddressLine4,
                                        AddressPostcode = s.CustomerAddress.AddressPostcode
                                    }
                                    : new Shared.Models.Address.AddressModel(),
                            LastDeliveryDate = s.Orders
                                                .Where(o => o.RouteId != null)
                                                .Where(o => o.Route.RouteDate < DateTime.Today)
                                                .OrderByDescending(o => o.Route.RouteDate)
                                                .Select(s => s.Route.RouteDate)
                                                .FirstOrDefault(),
                            OrdersTotal = s.Orders.Sum(s => s.Total),
                            PaymentsTotal = s.CustomerPayments.Sum(sum => sum.Total)
                        })
                        .ToList();

        return routeModel;
    }

    public async Task<RouteModel> GetRoutePrintAsync(Guid routeId)
    {
        var routeModel = await db.Routes
            .AsNoTracking()
            .Where(i => i.Id == routeId)
            .Select(o => new RouteModel
            {
                Id = o.Id,
                Status = o.Status,
                RouteDate = o.RouteDate,
                Name = o.Name,
                VehicleId = o.VehicleId,
                VehicleDriverId = o.VehicleDriverId,
                Vehicle = o.Vehicle != null ? o.Vehicle.Registration : string.Empty,
                VehicleCapacity = o.Vehicle != null ? o.Vehicle.Capacity : 0,
                VehicleDriver = o.VehicleDriver != null ? o.VehicleDriver.FirstName + " " + o.VehicleDriver.LastName : string.Empty,
                RouteArea = o.RouteArea.Name,
                RouteAreaId = o.RouteAreaId,

                RouteLines = o.RouteLines.Select(l => new RouteLineModel
                {
                    Id = l.Id,
                    CustomerId = l.Order != null ? l.Order.CustomerId : l.Visit.CustomerId,
                    StopNumber = l.StopNumber,

                    AddressName = l.Order != null ? l.Order.Customer.AddressName : l.Visit.Customer.AddressName,
                    DeliveryAddressLine1 = l.Order != null ? l.Order.DeliveryAddress.AddressLine1 : l.Visit.Customer.CustomerAddress.AddressLine1,
                    DeliveryAddressPostcode = l.Order != null ? l.Order.DeliveryAddress.AddressPostcode : l.Visit.Customer.CustomerAddress.AddressPostcode,
                    DeliveryAddressLine2 = l.Order != null ? l.Order.DeliveryAddress.AddressLine2 : l.Visit.Customer.CustomerAddress.AddressLine2,
                    DeliveryAddressLine3 = l.Order != null ? l.Order.DeliveryAddress.AddressLine3 : l.Visit.Customer.CustomerAddress.AddressLine3,
                    DeliveryAddressLine4 = l.Order != null ? l.Order.DeliveryAddress.AddressLine4 : l.Visit.Customer.CustomerAddress.AddressLine4,
                    DeliveryAddressLatitude = l.Order != null ? l.Order.DeliveryAddress.Latitude : l.Visit.Customer.CustomerAddress.Latitude,
                    DeliveryAddressLongitude = l.Order != null ? l.Order.DeliveryAddress.Longitude : l.Visit.Customer.CustomerAddress.Longitude,
                    PhoneNumber = l.Order != null ? l.Order.Customer.PhoneNumber : l.Visit.Customer.PhoneNumber,
                    MobileNumber = l.Order != null ? l.Order.Customer.MobilePhoneNumber : l.Visit.Customer.MobilePhoneNumber,

                    CustomerTags = l.Order != null
                                    ? l.Order.Customer.CustomerTagLinks.Select(ctl => ctl.CustomerTag.Name).ToList()
                                    : l.Visit.Customer.CustomerTagLinks.Select(ctl => ctl.CustomerTag.Name).ToList(),
                    CustomerDeliveryNotes = l.Order != null ? l.Order.Customer.DeliveryNotes : string.Empty,
                    OrderDeliveryNotes = l.Order != null ? l.Order.OrderDeliveryNotes : "Miss - No order today",
                    RoundNotes = l.Order != null ? l.Order.Customer.RoundNotes : string.Empty,

                    OrderId = l.OrderId,
                    OrderNumber = l.Order != null ? l.Order.OrderNumber : 0,

                    Products = l.Order != null && l.Order.OrderLines != null ? l.Order.OrderLines.Select(ol => new RouteLineProductModel
                    {
                        ProductCode = ol.Product != null ? ol.Product.ProductCode : string.Empty,
                        Name = ol.Product != null ? ol.Product.Name : string.Empty,
                        QuantityTotal = (int)ol.Quantity,
                        UnitWeight = ol.Product != null ? ol.Product.ProductWeightKilograms : 0,
                    }).OrderBy(o => o.Name).ToList() : new(),

                    Total = l.Order != null ? l.Order.Total : 0,

                    DeliveryStatus = l.DeliveryStatus,
                    PaymentType = l.PaymentType,
                    PaymentStatus = l.PaymentStatus,
                    PaymentAmount = l.PaymentAmount

                }).OrderBy(o => o.StopNumber).ToList(),

                StartAddressId = o.StartAddressId,
                EndAddressId = o.EndAddressId,
                RouteOptimisedStatus = o.RouteOptimisedStatus,

                WillCallCustomers = o.RouteArea.RouteCustomers
                    .Where(w => w.OnStop)
                    .OrderBy(o => o.Name)
                    .Select(s => new RouteWillCallCustomersModel
                    {
                        CustomerId = s.Id,
                        CustomerName = s.Name,
                        RoundNotes = s.RoundNotes,
                        Address = s.CustomerAddressId != null
                                ? new AddressModel(s.CustomerAddress)
                                : new AddressModel(),
                    })
                    .ToList(),
            })
            .FirstOrDefaultAsync();

        // Load all orders for these customers
        var orders = await db.Orders
            .Where(w => routeModel.RouteLines.Select(s => s.CustomerId).Contains(w.CustomerId))
            .Where(w => w.Status != (int)OrderStatus.Cancelled)
            .Where(w => w.RouteLines != null && w.RouteLines.Any())
            .GroupBy(w => w.CustomerId)
            .Select(g => new
            {
                CustomerId = g.Key,
                LastOrder = g.Where(o => o.Route.RouteDate < routeModel.RouteDate.Date)
                             .OrderByDescending(o => o.Route.RouteDate)
                             .Select(s => new RoutePrintOrderModel
                             {
                                 CustomerId = s.CustomerId,
                                 IsMissed = s.RouteLines.First().VisitId != null,
                                 RouteDate = s.Route.RouteDate,
                                 Products = s.OrderLines.Select(ol => $"{ol.Product.Name} x {(int)ol.Quantity}").ToList(),
                             })
                             .FirstOrDefault(),
                NextOrder = g.Where(o => o.Route.RouteDate > routeModel.RouteDate.Date)
                             .OrderBy(o => o.Route.RouteDate)
                             .Select(s => new RoutePrintOrderModel
                             {
                                 CustomerId = s.CustomerId,
                                 IsMissed = s.RouteLines.First().VisitId != null,
                                 RouteDate = s.Route.RouteDate,
                                 Products = s.OrderLines.Select(ol => $"{ol.Product.Name} x {(int)ol.Quantity}").ToList(),
                             })
                             .FirstOrDefault()
            })
            .ToListAsync();

        foreach (var routeLine in routeModel.RouteLines.Where(s => s.CustomerId != null))
        {
            var customerOrders = orders.FirstOrDefault(o => o.CustomerId == routeLine.CustomerId);
            if (customerOrders != null)
            {
                routeLine.PreviousOrder = customerOrders.LastOrder;
                routeLine.NextOrder = customerOrders.NextOrder;
            }
        }

        return routeModel;
    }

    private async Task CheckCashAmounts(Guid routeId)
    {
        var cashAmounts = await db.RouteCashAmounts
                                    .Where(c => c.RouteId == routeId)
                                    .ToListAsync();

        var newCashAmounts = new List<RouteCashAmount>();

        if (cashAmounts.FirstOrDefault(c => c.Type == (int)RouteCashAmountType.Cheque) == null)
        {
            newCashAmounts.Add(new RouteCashAmount { Id = Guid.NewGuid(), RouteId = routeId, TenantId = currentUserService.TenantId, Amount = 0, Display = "Cheques", Type = (int)RouteCashAmountType.Cheque });
        }
        if (cashAmounts.FirstOrDefault(c => c.Type == (int)RouteCashAmountType.FiftyPounds) == null)
        {
            newCashAmounts.Add(new RouteCashAmount { Id = Guid.NewGuid(), RouteId = routeId, TenantId = currentUserService.TenantId, Amount = 0, Display = "£50", Type = (int)RouteCashAmountType.FiftyPounds });
        }
        if (cashAmounts.FirstOrDefault(c => c.Type == (int)RouteCashAmountType.TwentyPounds) == null)
        {
            newCashAmounts.Add(new RouteCashAmount { Id = Guid.NewGuid(), RouteId = routeId, TenantId = currentUserService.TenantId, Amount = 0, Display = "£20", Type = (int)RouteCashAmountType.TwentyPounds });
        }
        if (cashAmounts.FirstOrDefault(c => c.Type == (int)RouteCashAmountType.TenPounds) == null)
        {
            newCashAmounts.Add(new RouteCashAmount { Id = Guid.NewGuid(), RouteId = routeId, TenantId = currentUserService.TenantId, Amount = 0, Display = "£10", Type = (int)RouteCashAmountType.TenPounds });
        }
        if (cashAmounts.FirstOrDefault(c => c.Type == (int)RouteCashAmountType.FivePounds) == null)
        {
            newCashAmounts.Add(new RouteCashAmount { Id = Guid.NewGuid(), RouteId = routeId, TenantId = currentUserService.TenantId, Amount = 0, Display = "£5", Type = (int)RouteCashAmountType.FivePounds });
        }
        if (cashAmounts.FirstOrDefault(c => c.Type == (int)RouteCashAmountType.Coins) == null)
        {
            newCashAmounts.Add(new RouteCashAmount { Id = Guid.NewGuid(), RouteId = routeId, TenantId = currentUserService.TenantId, Amount = 0, Display = "Coins", Type = (int)RouteCashAmountType.Coins });
        }
        if (cashAmounts.FirstOrDefault(c => c.Type == (int)RouteCashAmountType.PettyCashAdjustment) == null)
        {
            newCashAmounts.Add(new RouteCashAmount { Id = Guid.NewGuid(), RouteId = routeId, TenantId = currentUserService.TenantId, Amount = 0, Display = "Petty Cash Adjustment", Type = (int)RouteCashAmountType.PettyCashAdjustment });
        }

        if (newCashAmounts.Any())
        {
            db.RouteCashAmounts.AddRange(newCashAmounts);

            await db.SaveChangesAsync();
        }
    }

    public async Task<bool> UpdateRouteCashAmountAsync(RouteCashAmountModel cashAmountModel)
    {
        var cashAmount = await db.RouteCashAmounts.FirstOrDefaultAsync(c => c.Id == cashAmountModel.Id);

        if (cashAmount == null)
        {
            db.RouteCashAmounts.Add(new RouteCashAmount
            {
                Id = cashAmountModel.Id,
                RouteId = cashAmountModel.RouteId,
                TenantId = currentUserService.TenantId,
                Amount = cashAmountModel.Amount,
                Display = cashAmountModel.Display,
                Type = cashAmountModel.Type,
                Reason = cashAmountModel.Reason,
            });
        }
        else
        {
            cashAmount.Amount = cashAmountModel.Amount;
            cashAmount.Reason = cashAmountModel.Reason;
        }

        await db.SaveChangesAsync();

        return true;
    }

    /// <summary>
    /// Delete a route.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> DeleteRouteAsync(Guid id)
    {
        var route = await db.Routes
                    .Include(i => i.StartAddress)
                    .Include(i => i.EndAddress)
                    .Include(i => i.RouteCashAmounts)
                    .FirstOrDefaultAsync(r => r.Id == id);

        db.Addresses.Remove(route.StartAddress);
        db.Addresses.Remove(route.EndAddress);
        db.RouteCashAmounts.RemoveRange(route.RouteCashAmounts);

        db.Routes.Remove(route);

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// List of routes.
    /// </summary>
    /// <param name="gridParametersModel"></param>
    /// <returns></returns>
    public async Task<GridResultModel<RouteListItemModel>> ListRoutesAsync(GridParametersModel gridParametersModel)
    {
        var q = db.Routes
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Select(o => new RouteListItemModel
                   {
                       Id = o.Id,
                       Status = o.Status,
                       RouteDate = o.RouteDate,
                       Name = o.Name,
                       Stops = o.RouteLines.Count,
                       Vehicle = o.Vehicle.Registration,
                       AreaName = o.RouteArea.Name,
                       VehicleDriver = o.VehicleDriver.FirstName + " " + o.VehicleDriver.LastName,
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    /// <summary>
    /// List of routes.
    /// </summary>
    /// <param name="gridParametersModel"></param>
    /// <returns></returns>
    public async Task<List<RouteListItemModel>> ListTodaysRoutesAsync()
    {
        var routes = await db.Routes
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.RouteDate == DateTime.Today)
                   .Select(o => new RouteListItemModel
                   {
                       Id = o.Id,
                       Status = o.Status,
                       RouteDate = o.RouteDate,
                       Name = o.Name,
                       Stops = o.RouteLines.Count,
                       Vehicle = o.Vehicle.Registration,
                       AreaName = o.RouteArea.Name,
                       VehicleDriver = o.VehicleDriver.FirstName + " " + o.VehicleDriver.LastName,
                   })
                   .ToListAsync();

        return routes;
    }

    /// <summary>
    /// Route calendar.
    /// </summary>
    /// <param name="weekStartDate"></param>
    /// <returns></returns>
    public async Task<RouteCalendarModel> GetRouteCalendarAsync(DateTime weekStartDate)
    {
        var routeCalendarModel = new RouteCalendarModel
        {
            StartDate = weekStartDate,
            Days = RouteCalendarHelper.GetSevenDays(weekStartDate)
        };

        foreach (var day in routeCalendarModel.Days)
        {
            var routes = await db.Routes.Where(w => w.TenantId == currentUserService.TenantId)
                                   .Where(r => r.RouteDate.Date == day.Date)
                                   .Select(s => new RouteCalendarDayItemModel
                                   {
                                       Id = s.Id,
                                       Status = s.Status,
                                       AreaName = s.RouteArea.Name,
                                       AreaCode = s.RouteArea.Code,
                                       Name = s.Name,
                                       Vehicle = s.Vehicle.Registration,
                                       VehicleDriver = s.VehicleDriver.FirstName + " " + s.VehicleDriver.LastName,
                                       Stops = s.RouteLines.Count,
                                   })
                                   .ToListAsync();

            day.Routes = routes;
        }

        return routeCalendarModel;
    }

    /// <summary>
    /// Finds orders that can go on a route.
    /// </summary>
    /// <returns></returns>
    public async Task<List<DropdownListItem>> GetOpenOrdersDropdownAsync()
    {

        var orders = await db.Orders
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.Status == (int)OrderStatus.Open)
            .Where(w => w.RouteId == null)
            .OrderBy(o => o.OrderNumber)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = "#" + s.OrderNumber
                        + " - " + s.OrderDate.ToString("MM/dd/yyyy")
                        + " - " + s.Customer.Name
                        + " - " + s.Customer.CustomerAddress.AddressLine1
                        + " - " + s.Customer.CustomerAddress.AddressLine2
                        + " - " + s.Customer.CustomerAddress.AddressPostcode
            })
            .ToListAsync();

        return orders;
    }

    /// <summary>
    /// Update route details.
    /// </summary>
    /// <param name="routeModel"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> UpdateRouteDetailsAsync(RouteModel routeModel)
    {
        var route = await db.Routes.Where(i => i.Id == routeModel.Id).FirstOrDefaultAsync();
        route.Name = routeModel.Name;
        route.VehicleDriverId = routeModel.VehicleDriverId;
        route.VehicleId = routeModel.VehicleId;
        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Adds a order to route.
    /// </summary>
    /// <param name="routeId"></param>
    /// <param name="orderId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> AddOrderToRouteAsync(Guid routeId, Guid orderId)
    {
        var order = await db.Orders.FirstOrDefaultAsync(o => o.Id == orderId);
        order.RouteId = routeId;
        order.DeliveryStatus = (int)OrderDeliveryStatus.OnDeliveryRoute;

        db.RouteLines.Add(new Database.RouteLine
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,

            RouteId = routeId,
            OrderId = orderId,

            StopNumber = 999,
        });

        var routeData = await db.Routes.Where(w => w.Id == routeId).FirstOrDefaultAsync();
        routeData.RouteOptimisedStatus = (int)RouteOptimisedStatus.None;

        await db.SaveChangesAsync();

        await this.RefreshRouteLineNumbers(routeId);

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Delete order from route.
    /// </summary>
    /// <param name="routeLineId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> DeleteOrderOnRouteAsync(Guid routeLineId)
    {
        var routeline = await db.RouteLines.Include(r => r.Route).FirstOrDefaultAsync(s => s.Id == routeLineId);

        routeline.Route.RouteOptimisedStatus = (int)RouteOptimisedStatus.None;

        var order = await db.Orders.FirstOrDefaultAsync(o => o.Id == routeline.OrderId);
        order.RouteId = null;
        order.DeliveryStatus = (int)OrderDeliveryStatus.NotOnRoute;

        db.RouteLines.Remove(routeline);


        await db.SaveChangesAsync();


        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Delete visit from route.
    /// </summary>
    /// <param name="routeLineId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> DeleteVisitOnRouteAsync(Guid routeLineId)
    {
        var routeLine = await db.RouteLines
                        .Include(i => i.Visit)
                        .Include(r => r.Route)
                        .FirstOrDefaultAsync(s => s.Id == routeLineId);

        routeLine.Route.RouteOptimisedStatus = (int)RouteOptimisedStatus.None;

        db.Visits.Remove(routeLine.Visit);
        db.RouteLines.Remove(routeLine);

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Adds a order to route.
    /// </summary>
    /// <param name="routeId"></param>
    /// <param name="orderId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> AddVisitToRouteAsync(Guid routeId, Guid visitId)
    {
        var visit = await db.Visits.FirstOrDefaultAsync(o => o.Id == visitId);
        visit.RouteId = routeId;

        db.RouteLines.Add(new Database.RouteLine
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,

            RouteId = routeId,
            VisitId = visitId,
            StopNumber = 999,
        });

        var routeData = await db.Routes.Where(w => w.Id == routeId).FirstOrDefaultAsync();
        routeData.RouteOptimisedStatus = (int)RouteOptimisedStatus.None;

        await db.SaveChangesAsync();

        await this.RefreshRouteLineNumbers(routeId);

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Delete order from route.
    /// </summary>
    /// <param name="routeLineId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> DeleteVisitOnRouteAsync(Guid routeId, Guid visitId)
    {
        var routeline = await db.RouteLines
                        .Include(i => i.Route)
                        .Where(w => w.RouteId == routeId)
                        .Where(w => w.VisitId == visitId)
                        .FirstOrDefaultAsync();

        routeline.Route.RouteOptimisedStatus = (int)RouteOptimisedStatus.None;

        var visit = await db.Visits.FirstOrDefaultAsync(o => o.Id == routeline.VisitId);

        db.Visits.Remove(visit);
        db.RouteLines.Remove(routeline);

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Reoragnises the route in optimum order.
    /// </summary>
    /// <param name="routeId"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> OptimiseRoute(Guid routeId)
    {
        try
        {
            var route = await db.Routes.Where(w => w.Id == routeId)
                                  .Select(r => new
                                  {
                                      StartLatitude = r.StartAddress.Latitude,
                                      StartLongitude = r.StartAddress.Longitude,

                                      EndLatitude = r.EndAddress.Latitude,
                                      EndLongitude = r.EndAddress.Longitude,
                                  }).FirstOrDefaultAsync();

            var routeLines = await db.RouteLines
                            .Include(i => i.Order)
                                .ThenInclude(i => i.DeliveryAddress)
                            .Include(i => i.Visit)
                                .ThenInclude(i => i.Customer)
                                    .ThenInclude(i => i.CustomerAddress)
                            .Where(s => s.RouteId == routeId)
                            .ToListAsync();

            var locations = new List<RouteXLLocation>
            {
                new() { address = "start", lat = route.StartLatitude.ToString(), lng = route.StartLongitude.ToString() }
            };
            foreach (var item in routeLines)
            {
                if (item.VisitId != null)
                {
                    locations.Add(new RouteXLLocation
                    {
                        address = item.Id.ToString(),
                        lat = item.Visit.Customer.CustomerAddress.Latitude.ToString(),
                        lng = item.Visit.Customer.CustomerAddress.Longitude.ToString()
                    });
                }
                else
                {
                    locations.Add(new RouteXLLocation
                    {
                        address = item.Id.ToString(),
                        lat = item.Order.DeliveryAddress.Latitude.ToString(),
                        lng = item.Order.DeliveryAddress.Longitude.ToString()
                    });
                }

            }
            locations.Add(new RouteXLLocation { address = "end", lat = route.EndLatitude.ToString(), lng = route.EndLongitude.ToString() });

            var routeOptimised1 = await routeXlService.GetOptimisedRoute(locations);

            foreach (var routeline in routeLines)
            {
                var routeKey = routeOptimised1.FirstOrDefault(s => s.Value.name == routeline.Id.ToString());
                routeline.StopNumber = Convert.ToInt32(routeKey.Key);
            }

            var routeData = await db.Routes.FirstOrDefaultAsync(w => w.Id == routeId);
            routeData.RouteOptimisedStatus = (int)RouteOptimisedStatus.Automatic;

            await db.SaveChangesAsync();
        }
        catch (Exception)
        {

            return new FormResponseModel<bool>(false);
        }



        return new FormResponseModel<bool>();
    }

    /// <summary>
    /// List vehicles for route.
    /// </summary>
    /// <returns></returns>
    public async Task<List<DropdownListItem>> ListVehicleDropdownAsync()
    {
        var q = await db.Vehicles.Where(w => w.TenantId == currentUserService.TenantId)

                                   .Select(s => new DropdownListItem
                                   {
                                       Id = s.Id.ToString(),
                                       Name = s.Registration
                                   })
                                   .OrderBy(o => o.Name)
                                   .ToListAsync();

        return q;
    }

    /// <summary>
    /// Driver returned.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> DriverReturnedAsync(Guid id)
    {
        var route = await db.Routes
                             .Include(i => i.RouteLines)
                             .ThenInclude(i => i.Order)
                             .FirstOrDefaultAsync(r => r.Id == id);

        route.Status = (int)RouteStatus.DriverReturned;

        foreach (var routeLine in route.RouteLines)
        {
            routeLine.DeliveryStatus = (int)RouteLineDeliveryStatus.Delivered;
            routeLine.PaymentStatus = (int)OrderPaymentStatus.Paid;
            routeLine.PaymentAmount = routeLine.Order != null ? routeLine.Order.Total : 0;
        }

        await db.SaveChangesAsync();



        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Reopens the route and pulls it back from driver return.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> ReOpenRouteFromDriverReturnedAsync(Guid id)
    {
        var route = await db.Routes
                             .Include(i => i.RouteLines)
                             .ThenInclude(i => i.Order)
                             .FirstOrDefaultAsync(r => r.Id == id);

        route.Status = (int)RouteStatus.Open;

        foreach (var routeLine in route.RouteLines)
        {
            routeLine.DeliveryStatus = (int)RouteLineDeliveryStatus.NotDelivered;
            routeLine.PaymentStatus = (int)OrderPaymentStatus.NotPaid;
            routeLine.PaymentAmount = routeLine.Order != null ? routeLine.Order.Total : 0;
        }

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Update driver returned line.
    /// </summary>
    /// <param name="routeLineModel"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> UpdateDriverReturnedLineAsync(RouteLineModel routeLineModel)
    {
        var routeLine = await db.RouteLines.Where(i => i.Id == routeLineModel.Id).FirstOrDefaultAsync();
        routeLine.DeliveryStatus = routeLineModel.DeliveryStatus;
        routeLine.PaymentStatus = routeLineModel.PaymentStatus;
        routeLine.PaymentType = routeLineModel.PaymentType;
        routeLine.PaymentAmount = (OrderPaymentType)routeLineModel.PaymentType == OrderPaymentType.NotPaid ? 0 : routeLineModel.PaymentAmount;
        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Complete route.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> CompleteRouteAsync(Guid id)
    {
        var route = await db.Routes
                    .Include(i => i.RouteLines)
                        .ThenInclude(o => o.Order)
                    .Include(i => i.RouteLines)
                        .ThenInclude(o => o.Visit)
                    .FirstOrDefaultAsync(r => r.Id == id);

        route.Status = (int)RouteStatus.Completed;

        foreach (var routeLine in route.RouteLines)
        {
            if (routeLine.VisitId != null)
            {
                routeLine.DeliveryStatus = (int)RouteLineDeliveryStatus.VisitOnly;
            }

            if (routeLine.DeliveryStatus == (int)RouteLineDeliveryStatus.Delivered && routeLine.Order != null)
            {
                routeLine.Order.Status = (int)OrderStatus.Completed;
                routeLine.Order.DeliveryStatus = (int)OrderDeliveryStatus.Delivered;
            }

            if (routeLine.PaymentAmount != 0)
            {
                var payment = new CustomerPayment
                {
                    Id = Guid.NewGuid(),
                    TenantId = currentUserService.TenantId,
                    CustomerId = routeLine.Order != null ? routeLine.Order.CustomerId.Value : routeLine.Visit.CustomerId,
                    Date = DateTime.Now,
                    PaymentType = routeLine.PaymentType,
                    Total = routeLine.PaymentAmount,
                    OrderId = routeLine.Order?.Id
                };
                db.CustomerPayments.Add(payment);

                await db.SaveChangesAsync();
            }
        }

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Route area dropdown.
    /// </summary>
    /// <returns></returns>
    public async Task<List<DropdownListItem>> ListRoundRouteAreaDropdownAsync()
    {
        var q = await db.RouteAreas.Where(w => w.TenantId == currentUserService.TenantId)
                                   .Where(w => w.IsRoundRoute)
                                   .Select(s => new DropdownListItem
                                   {
                                       Id = s.Id.ToString(),
                                       Name = s.Name
                                   })
                                   .OrderBy(o => o.Name)
                                   .ToListAsync();

        return q;
    }

    /// <summary>
    /// Route area dropdown.
    /// </summary>
    /// <returns></returns>
    public async Task<List<DropdownListItem>> ListRouteAreaDropdownAsync()
    {
        var q = await db.RouteAreas.Where(w => w.TenantId == currentUserService.TenantId)

                                   .Select(s => new DropdownListItem
                                   {
                                       Id = s.Id.ToString(),
                                       Name = s.Name
                                   })
                                   .OrderBy(o => o.Name)
                                   .ToListAsync();

        return q;
    }

    /// <summary>
    /// List Vehicle drivers.
    /// </summary>
    /// <returns></returns>
    public async Task<List<DropdownListItem>> ListVehicleDriversDropdown()
    {
        var vehicleDrivers = await db.VehicleDrivers
                              .Where(w => w.TenantId == currentUserService.TenantId)
                              .Select(s => new DropdownListItem
                              {
                                  Id = s.Id.ToString(),
                                  Name = s.FirstName + " " + s.LastName,
                              })
                              .OrderBy(o => o.Name)
                              .ToListAsync();

        return vehicleDrivers;
    }


    /// <summary>
    /// Refresh Products for Route.
    /// </summary>
    /// <returns></returns>
    public async Task RefreshRouteLineNumbers(Guid routeId)
    {
        var routeLines = await db.RouteLines
                        .Where(w => w.RouteId == routeId)
                        .ToListAsync();

        int stopNumber = 1;

        foreach (var routeLine in routeLines.OrderBy(o => o.StopNumber))
        {
            routeLine.StopNumber = stopNumber;
            stopNumber++;
        }

        await db.SaveChangesAsync();
    }

    /// <summary>
    /// Refresh Products for Route.
    /// </summary>
    /// <returns></returns>
    public async Task RefreshRouteProducts(Guid routeId)
    {
        var routeExtra = await db.Routes.Where(w => w.Id == routeId).Select(s => new
        {
            s.TenantId,

            OrderProducts = s.RouteLines.Where(l => l.OrderId != null).SelectMany(o => o.Order.OrderLines).Select(ol => new
            {
                ProductId = ol.Product != null ? ol.Product.Id : Guid.Empty,
                ProductCode = ol.Product != null ? ol.Product.ProductCode : string.Empty,
                Name = ol.Product != null ? ol.Product.Name : string.Empty,
                Quantity = (int)ol.Quantity,
                Weight = ol.Product != null ? ol.Product.ProductWeightKilograms : 0,
            }).ToList(),

            Products = s.RouteProducts.Where(s => s.QuantityFromExtras != 0).Select(ss => new
            {
                ss.ProductId,
                ss.QuantityFromExtras
            }).ToList()

        }).FirstOrDefaultAsync();


        var routeProducts = await db.RouteProducts.Where(s => s.RouteId == routeId).ToListAsync();

        db.RouteProducts.RemoveRange(routeProducts);

        foreach (var item in routeExtra.OrderProducts.GroupBy(s => s.ProductId))
        {
            var productId = item.Key;

            if (productId != Guid.Empty)
            {
                db.RouteProducts.Add(new RouteProduct
                {
                    Id = Guid.NewGuid(),
                    TenantId = routeExtra.TenantId,
                    RouteId = routeId,
                    ProductId = productId,
                    QuantityFromOrders = routeExtra.OrderProducts.Where(s => s.ProductId == productId).Sum(s => s.Quantity),
                    QuantityFromExtras = routeExtra.Products.Where(s => s.ProductId == productId).Sum(s => s.QuantityFromExtras),

                    QuantityTotal = routeExtra.OrderProducts.Where(s => s.ProductId == productId).Sum(s => s.Quantity) + routeExtra.Products.Where(s => s.ProductId == productId).Sum(s => s.QuantityFromExtras)
                });
            }
        }

        await db.SaveChangesAsync();
    }

    /// <summary>
    /// Update extra.
    /// </summary>
    /// <param name="routeId"></param>
    /// <returns></returns>
    public async Task<RouteLineProductModel> UpdateExtraProducts(RouteLineProductModel routeLineProductModel)
    {
        var vehicleDrivers = await db.RouteProducts
                  .Where(w => w.TenantId == currentUserService.TenantId)
                  .Where(w => w.Id == routeLineProductModel.Id)
                  .FirstOrDefaultAsync();

        vehicleDrivers.QuantityFromExtras = routeLineProductModel.QuantityFromExtras;

        vehicleDrivers.QuantityTotal = routeLineProductModel.QuantityFromOrders + routeLineProductModel.QuantityFromExtras;

        await db.SaveChangesAsync();

        routeLineProductModel.QuantityTotal = vehicleDrivers.QuantityTotal;

        return routeLineProductModel;
    }

    public async Task<RouteDatePrintModel> GetRouteOwingsDatePrint(DateTime dateTime)
    {
        var routes = await db.Routes.Where(w => w.TenantId == currentUserService.TenantId)
                               .Where(w => w.RouteDate == dateTime)
                               .Select(w => w.Id)
                               .ToListAsync();

        var routeDatePrintModel = new RouteDatePrintModel { };

        foreach (var routeId in routes)
        {
            var route = await this.GetRouteOwingsPrintAsync(routeId);
            routeDatePrintModel.Routes.Add(route);
        }

        return routeDatePrintModel;
    }

    public async Task<RouteDatePrintModel> GetRouteLoadsDatePrint(DateTime dateTime)
    {
        var routes = await db.Routes.Where(w => w.TenantId == currentUserService.TenantId)
                                           .Where(w => w.RouteDate == dateTime)
                                           .Select(w => w.Id)
                                           .ToListAsync();

        var routeDatePrintModel = new RouteDatePrintModel { };

        foreach (var routeId in routes)
        {
            var route = await this.GetRouteLoadsPrintAsync(routeId);
            routeDatePrintModel.Routes.Add(route);
        }

        return routeDatePrintModel;
    }

    /// <summary>
    /// GetRouteDayPrint
    /// </summary>
    /// <param name="routeId"></param>
    /// <returns></returns>
    public async Task<RouteDatePrintModel> GetRouteDatePrint(DateTime dateTime)
    {
        var routes = await db.Routes.Where(w => w.TenantId == currentUserService.TenantId)
                                           .Where(w => w.RouteDate == dateTime)
                                           .Select(w => w.Id)
                                           .ToListAsync();

        var routeDatePrintModel = new RouteDatePrintModel { };

        foreach (var routeId in routes)
        {
            var route = await this.GetRoutePrintAsync(routeId);
            routeDatePrintModel.Routes.Add(route);
        }

        return routeDatePrintModel;
    }

    public async Task<FormResponseModel<bool>> UpdateRouteLinesOrder(RouteModel routeModel)
    {
        var route = await db.Routes.Where(w => w.TenantId == currentUserService.TenantId)
                                    .Where(w => w.Id == routeModel.Id)
                                    .Include(i => i.RouteLines)
                                    .FirstOrDefaultAsync();

        route.RouteOptimisedStatus = (int)RouteOptimisedStatus.Manual;

        foreach (var routeline in route.RouteLines)
        {
            routeline.StopNumber = routeModel.RouteLines.FirstOrDefault(s => s.Id == routeline.Id).StopNumber;
        }

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>();
    }


    /// <summary>
    /// List Vehicle drivers.
    /// </summary>
    /// <returns></returns>
    public async Task<List<DropdownListItem>> GetRoutesForDate(DateTime date, bool excludeCompleted = false)
    {
        var routesQuery = db.Routes
                        .Where(route => route.TenantId == currentUserService.TenantId)
                        .Where(route => route.RouteDate == date.Date);

        if (excludeCompleted)
        {
            routesQuery = routesQuery.Where(route => route.Status != (int)RouteStatus.Completed);
        }

        var routes = await routesQuery
                    .Select(s => new DropdownListItem
                    {
                        Id = s.Id.ToString(),
                        Name = "Route: " + (s.Name ?? "") + ", " +
                               "Driver: " + (s.VehicleDriver != null ? s.VehicleDriver.FirstName + " " + s.VehicleDriver.LastName : "N/A") + ", " +
                               "Vehicle: " + (s.Vehicle != null ? s.Vehicle.Registration : "N/A")
                    })
                    .OrderBy(o => o.Name)
                    .ToListAsync();

        return routes;
    }

    public async Task<FormResponseModel<RouteMoveModel>> RouteMove(RouteMoveModel routeMoveModel)
    {
        var routeLinesToMove = await db.RouteLines.Include(o => o.Order).Where(rl => routeMoveModel.RouteLinesToMove.Contains(rl.Id)).ToListAsync();

        foreach (var item in routeLinesToMove)
        {
            item.RouteId = routeMoveModel.MoveToRouteId;
            item.Order.RouteId = routeMoveModel.MoveToRouteId;
            item.StopNumber = 999;
        }
        await db.SaveChangesAsync();

        await this.RefreshRouteLineNumbers(routeMoveModel.MoveFromRouteId);
        await this.RefreshRouteLineNumbers(routeMoveModel.MoveToRouteId);

        await this.RefreshRouteProducts(routeMoveModel.MoveFromRouteId);
        await this.RefreshRouteProducts(routeMoveModel.MoveToRouteId);

        var extrasToMove = routeMoveModel.ProductLines.Where(s => s.ExtrasToMove != 0).ToList();

        foreach (var extra in extrasToMove)
        {
            var routeToProducts = await db.RouteProducts.FirstOrDefaultAsync(rp => rp.RouteId == routeMoveModel.MoveToRouteId
                                                                        && rp.ProductId == extra.ProductId);

            if (routeToProducts != null)
            {
                routeToProducts.QuantityFromExtras += extra.ExtrasToMove;
            }

            if (routeToProducts != null)
            {
                routeToProducts.QuantityFromExtras -= extra.ExtrasToMove;
            }

        }

        await this.RefreshRouteProducts(routeMoveModel.MoveFromRouteId);
        await this.RefreshRouteProducts(routeMoveModel.MoveToRouteId);


        return new FormResponseModel<RouteMoveModel>();
    }

    public async Task<FormResponseModel<RouteUpdateAddress>> RouteUpdateAddress(RouteUpdateAddress routeUpdateAddress)
    {
        var route = await db.Routes.Include(i => i.StartAddress).Include(i => i.EndAddress).FirstOrDefaultAsync(r => r.Id == routeUpdateAddress.RouteId);

        if (routeUpdateAddress.StartAddress)
        {
            route.StartAddress.AddressLine1 = routeUpdateAddress.Address.AddressLine1;
            route.StartAddress.AddressLine2 = routeUpdateAddress.Address.AddressLine2;
            route.StartAddress.AddressLine3 = routeUpdateAddress.Address.AddressLine3;
            route.StartAddress.AddressLine4 = routeUpdateAddress.Address.AddressLine4;
            route.StartAddress.AddressPostcode = routeUpdateAddress.Address.AddressPostcode;
            route.StartAddress.Latitude = routeUpdateAddress.Address.Latitude;
            route.StartAddress.Longitude = routeUpdateAddress.Address.Longitude;
        }

        if (!routeUpdateAddress.StartAddress)
        {
            route.EndAddress.AddressLine1 = routeUpdateAddress.Address.AddressLine1;
            route.EndAddress.AddressLine2 = routeUpdateAddress.Address.AddressLine2;
            route.EndAddress.AddressLine3 = routeUpdateAddress.Address.AddressLine3;
            route.EndAddress.AddressLine4 = routeUpdateAddress.Address.AddressLine4;
            route.EndAddress.AddressPostcode = routeUpdateAddress.Address.AddressPostcode;
            route.EndAddress.Latitude = routeUpdateAddress.Address.Latitude;
            route.EndAddress.Longitude = routeUpdateAddress.Address.Longitude;
        }

        await db.SaveChangesAsync();

        return new FormResponseModel<RouteUpdateAddress>(routeUpdateAddress);
    }

    public async Task<FormResponseModel<Guid>> FindRouteAsync(RouteSearchModel searchModel)
    {
        var response = new FormResponseModel<Guid>();

        var customer = await db.Customers
                        .Include(i => i.CustomerAddress)
                        .Include(i => i.RouteArea)
                            .ThenInclude(ti => ti.RouteAreaLinks)
                        .FirstOrDefaultAsync(c => c.Id == searchModel.CustomerId);

        var route = await db.Routes
                .Where(l => l.TenantId == currentUserService.TenantId)
                .Where(l => l.RouteAreaId == searchModel.RouteAreaId)
                .Where(l => l.RouteDate == searchModel.DeliveryDate)
                .FirstOrDefaultAsync();

        // See if we can find a linked route
        if (route == null && customer.RouteArea.RouteAreaLinks != null)
        {
            foreach (var relatedRoute in customer.RouteArea.RouteAreaLinks)
            {
                route = await db.Routes
                        .Where(l => l.TenantId == currentUserService.TenantId)
                        .Where(l => l.RouteAreaId == relatedRoute.LinkedRouteAreaId)
                        .Where(l => l.RouteDate == searchModel.DeliveryDate)
                        .FirstOrDefaultAsync();

                if (route != null)
                {
                    break;
                }
            }
        }

        if (route == null)
        {
            response.AddError($"The route does not exist for {searchModel.DeliveryDate.ToShortDateString()}.");
        }
        else
        {
            response.Data = route.Id;
        }

        return response;
    }

    public async Task<FormResponseModel<bool>> UpdateCustomerRoundNotesAsync(RouteLineModel model)
    {
        var response = new FormResponseModel<bool>();

        var customer = await db.Customers
                            .Where(w => w.TenantId == currentUserService.TenantId)
                            .Where(w => w.Id == model.CustomerId)
                            .FirstOrDefaultAsync();

        customer.RoundNotes = model.RoundNotes;

        await db.SaveChangesAsync();

        response.Data = true;

        return response;
    }

    public async Task<FormResponseModel<int>> AddWillRingsToRouteAsync(Guid routeId, List<Guid> tagIds)
    {
        var response = new FormResponseModel<int>();

        // Get the route to get the route date
        var route = await db.Routes
            .Where(r => r.TenantId == currentUserService.TenantId)
            .Where(r => r.Id == routeId)
            .FirstOrDefaultAsync();

        // Find all customers with the selected tags where OnStop = true
        var customers = await db.Customers
            .Where(c => c.TenantId == currentUserService.TenantId)
            .Where(c => c.OnStop)
            .Where(c => c.Status == (int)GenericStatus.Active)
            .Where(c => c.CustomerTagLinks.Any(a => tagIds.Contains(a.CustomerTagId)))
            .ToListAsync();

        var visitsToAdd = new List<Database.Visit>();

        foreach (var customer in customers)
        {
            // Check if customer is already on this route (via Order or Visit)
            var existingRouteLine = await db.RouteLines
                .Where(rl => rl.TenantId == currentUserService.TenantId)
                .Where(rl => rl.RouteId == routeId)
                .Where(rl => (rl.Order != null && rl.Order.CustomerId == customer.Id) ||
                            (rl.Visit != null && rl.Visit.CustomerId == customer.Id))
                .FirstOrDefaultAsync();

            if (existingRouteLine != null)
            {
                continue; // Skip if customer is already on route
            }

            // Create a new visit for this customer
            var visit = new Database.Visit
            {
                Id = Guid.NewGuid(),
                TenantId = currentUserService.TenantId,
                CustomerId = customer.Id,
                VisitDate = route.RouteDate,
                CreatedDateTime = DateTime.Now,
                OrderDeliveryNotes = "Will Ring - No order today",
            };

            visitsToAdd.Add(visit);
        }

        // Add all visits to the database and save first
        db.Visits.AddRange(visitsToAdd);
        await db.SaveChangesAsync();

        // Add each visit to the route
        foreach (var visit in visitsToAdd)
        {
            await AddVisitToRouteAsync(routeId, visit.Id);
        }

        response.Data = visitsToAdd.Count;
        return response;
    }
}
