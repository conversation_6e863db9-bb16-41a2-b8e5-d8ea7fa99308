﻿using HT.Api.Common.Extensions;
using HT.Database;
using HT.Shared.Constants;
using HT.Shared.Models.Order;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Order;

public class OrderLineService(
    DatabaseContext db,
    CurrentUserService currentUserService,
    ProductPriceService productPriceService,
    PortalEventService portalEventService)
{
    /// <summary>
    /// Creates an order line.
    /// </summary>
    /// <param name="orderId">The ID of the order.</param>
    /// <returns>The created order line.</returns>
    public async Task<OrderLineModel> CreateOrderLine(Guid orderId)
    {
        var orderLine = new OrderLine
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            OrderId = orderId,
            LineNumber = await GetNextOrderLineNumber(orderId)
        };

        db.OrderLines.Add(orderLine);

        await portalEventService.LogAsync(PortalEventType.OrderLineCreated, orderId, $"Order line {orderLine.LineNumber} created");

        await db.SaveChangesAsync();

        var orderLineModel = new OrderLineModel
        {
            Id = orderLine.Id,
            LineNumber = orderLine.LineNumber
        };

        return orderLineModel;
    }

    /// <summary>
    /// Updates a order line.
    /// </summary>
    /// <param name="orderLineModel"></param>
    /// <returns></returns>
    public async Task<OrderLineModel> UpdateOrderLine(OrderLineModel orderLineModel)
    {
        var orderLine = await db.OrderLines.Include(i => i.Order).FirstOrDefaultAsync(o => o.Id == orderLineModel.Id);

        orderLine.Quantity = orderLine.ProductId != null ? orderLineModel.Quantity : 1;
        orderLine.ProductId = orderLineModel.ProductId;

        orderLine.CustomUnitPrice = orderLineModel.CustomUnitPrice;

        orderLine.UnitPrice = 0;
        orderLine.UnitTotal = 0;
        orderLine.VATRate = 0;
        orderLine.VATTotal = 0;

        if (orderLineModel.ProductId != null)
        {
            var price = await productPriceService.GetProductPriceAsync((Guid)orderLineModel.ProductId, (Guid)orderLine.Order.CustomerId);
            decimal unitPrice = price.Price;
            decimal vatRate = price.VATPercent;

            // If the order line has a custom unit price, use the custom unit price and VAT rate.
            orderLine.VATRate = orderLineModel.VATRate;

            if (orderLineModel.CustomUnitPrice)
            {
                orderLine.UnitPrice = orderLineModel.UnitPrice;
            }
            else
            {
                orderLine.UnitPrice = unitPrice;
                orderLine.VATRate = vatRate;
            }
            orderLine.UnitTotal = orderLine.UnitPrice * orderLine.Quantity;
        }

        orderLine.VATTotal = orderLine.VATRate != 0 ? orderLine.UnitTotal * (orderLine.VATRate / 100) : 0;
        orderLine.Total = orderLine.UnitTotal + orderLine.VATTotal;

        string changes = db.GetChangedProperties(orderLine);
        await portalEventService.LogAsync(PortalEventType.OrderLineUpdated, orderLine.OrderId, $"Order line {orderLine.LineNumber} changed: {changes}");

        await db.SaveChangesAsync();

        orderLineModel.Quantity = (int)orderLine.Quantity;
        orderLineModel.UnitPrice = orderLine.UnitPrice;
        orderLineModel.UnitTotal = orderLine.UnitTotal;
        orderLineModel.VATRate = orderLine.VATRate;
        orderLineModel.VATTotal = orderLine.VATTotal;
        orderLineModel.Total = orderLine.Total;

        await this.UpdateOrderTotals(orderLine.OrderId);

        return orderLineModel;
    }

    /// <summary>
    /// Deletes a order line.
    /// </summary>
    /// <param name="orderLineModel"></param>
    /// <returns></returns>
    public async Task<OrderLineModel> DeleteOrderLine(OrderLineModel orderLineModel)
    {
        var orderLine = await db.OrderLines.FirstOrDefaultAsync(o => o.Id == orderLineModel.Id);

        await portalEventService.LogAsync(PortalEventType.OrderLineDeleted, orderLine.OrderId, $"Order line {orderLine.LineNumber} deleted");

        db.OrderLines.Remove(orderLine);

        var remainingOrderLines = db.OrderLines.Where(w => w.OrderId == orderLine.OrderId && w.Id != orderLineModel.Id).OrderBy(o => o.LineNumber);

        int lineNumber = 1;
        foreach (var remainingOrderLine in remainingOrderLines)
        {
            remainingOrderLine.LineNumber = lineNumber;
            lineNumber++;
        }
        await db.SaveChangesAsync();

        await this.UpdateOrderTotals(orderLine.OrderId);

        return orderLineModel;
    }

    /// <summary>
    /// Get the next order line number.
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    private async Task<int> GetNextOrderLineNumber(Guid orderId)
    {
        int? lastOrderLineNumber = await db.OrderLines
            .Where(s => s.OrderId == orderId)
            .MaxAsync(s => (int?)s.LineNumber);

        return (lastOrderLineNumber ?? 0) + 1;
    }

    /// <summary>
    /// Update the order totals.
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    private async Task UpdateOrderTotals(Guid orderId)
    {
        var order = await db.Orders
            .Include(i => i.OrderLines)
            .FirstOrDefaultAsync(o => o.Id == orderId);

        order.UnitTotal = order.OrderLines.Sum(s => s.UnitTotal);
        order.VATTotal = order.OrderLines.Sum(s => s.VATTotal);
        order.Total = order.OrderLines.Sum(s => s.Total);

        await db.SaveChangesAsync();
    }
}
