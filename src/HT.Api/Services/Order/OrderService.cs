﻿using HT.Api.Common.Extensions;
using HT.Api.Services.Common.Address;
using HT.Api.Services.Customer;
using HT.Api.Services.Route;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Constants;
using HT.Shared.Enums;
using HT.Shared.Models.Customer;
using HT.Shared.Models.Order;
using HT.Shared.Models.Product;
using HT.Shared.Models.Route;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Order;

public class OrderService(
    DatabaseContext databaseContext,
    AddressService addressService,
    CurrentUserService currentUserService,
    RouteService routeService,
    CustomerPaymentService customerPaymentService,
    PortalEventService portalEventService)
{
    /// <summary>
    /// Creates an order.
    /// </summary>
    public async Task<OrderCreateModel> CreateOrderAsync(OrderCreateModel orderCreateModel)
    {
        int nextNumber = await databaseContext.Orders
        .Where(s => s.TenantId == currentUserService.TenantId)
        .Select(s => s.OrderNumber)
        .DefaultIfEmpty()
        .MaxAsync();

        var customer = await databaseContext.Customers
            .Where(c => c.Id == orderCreateModel.CustomerId)
            .Select(c => new
            {
                c.CustomerAddressId,
                c.DefaultPaymentType,
            })
            .FirstOrDefaultAsync();

        var customerAddress = await addressService.GetAddressAsync(customer.CustomerAddressId);

        var orderAddressId = await addressService.CreateUpdateAddressAsync(new Shared.Models.Address.AddressModel
        {
            AddressLine1 = customerAddress.AddressLine1,
            AddressLine2 = customerAddress.AddressLine2,
            AddressLine3 = customerAddress.AddressLine3,
            AddressLine4 = customerAddress.AddressLine4,
            AddressPostcode = customerAddress.AddressPostcode,
            Latitude = customerAddress.Latitude,
            Longitude = customerAddress.Longitude,
        });

        var order = new Database.Order
        {
            Id = orderCreateModel.Id,
            TenantId = currentUserService.TenantId,
            CustomerId = orderCreateModel.CustomerId,
            OrderDate = DateTime.Now.Date,
            CreatedDateTime = DateTime.Now,
            Status = (int)OrderStatus.Open,
            OrderNumber = nextNumber + 1,
            DeliveryType = (int)OrderDeliveryType.Route,
            DeliveryStatus = (int)OrderDeliveryStatus.NotOnRoute,
            DeliveryAddressId = orderAddressId,
        };

        databaseContext.Orders.Add(order);

        await portalEventService.LogAsync(PortalEventType.OrderCreated, order.Id);

        await databaseContext.SaveChangesAsync();

        if (orderCreateModel.RouteId != null)
        {
            await routeService.AddOrderToRouteAsync((Guid)orderCreateModel.RouteId, order.Id);
        }

        orderCreateModel.Id = order.Id;

        return orderCreateModel;
    }

    /// <summary>
    /// Lists orders.
    /// </summary>
    public async Task<GridResultModel<OrderListItemModel>> ListOrdersAsync(GridParametersModel gridParametersModel)
    {
        var q = databaseContext.Orders
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Select(o => new OrderListItemModel
                   {
                       Id = o.Id,
                       OrderDate = o.OrderDate,
                       Status = o.Status,
                       OrderNumber = o.OrderNumber,
                       CustomerName = o.Customer.Name,
                       CustomerTags = o.Customer.CustomerTagLinks.Select(t => t.CustomerTag.Name).ToList(),

                       DeliveryType = o.DeliveryType,
                       DeliveryStatus = o.DeliveryStatus,
                       DeliveryDate = o.Route != null ? o.Route.RouteDate : null,

                       Total = o.Total
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    /// <summary>
    /// Get an order.
    /// </summary>
    public async Task<OrderModel> GetOrderAsync(Guid orderId)
    {
        var orderModel = await databaseContext.Orders
                               .Where(i => i.Id == orderId)
                               .Select(o => new OrderModel
                               {
                                   Id = o.Id,
                                   ReadOnly = o.Status == (int)OrderStatus.Completed,
                                   Status = o.Status,
                                   OrderNumber = o.OrderNumber,
                                   OrderDate = o.OrderDate,
                                   RoundOrder = o.RoundOrder,
                                   CustomerId = o.CustomerId,
                                   CustomerName = o.Customer.Name,
                                   CustomerPhoneNumber = o.Customer.PhoneNumber,
                                   CustomerMobileNumber = o.Customer.MobilePhoneNumber,
                                   CustomerEmail = o.Customer.InvoiceEmail,
                                   CustomerDeliveryNotes = o.Customer.DeliveryNotes,
                                   CustomerDefaultRouteAreaId = o.Customer.RouteAreaId,
                                   CustomerTags = o.Customer.CustomerTagLinks.Select(t => t.CustomerTag.Name).ToList(),
                                   PaymentTotal = o.Total,
                                   DeliveryType = o.DeliveryType,
                                   DeliveryStatus = o.DeliveryStatus,
                                   OrderDeliveryNotes = o.OrderDeliveryNotes,
                                   DeliveryRouteId = o.RouteId,
                                   DeliveryOnRoute = o.RouteId != null,
                                   DeliveryRouteDate = o.RouteId != null ? o.Route.RouteDate : DateTime.Now,
                                   DeliveryRouteArea = o.RouteId != null && o.Route.RouteArea != null ? o.Route.RouteArea.Name : "",
                                   DeliveryRouteVehicleName = o.RouteId != null && o.Route.Vehicle != null ? o.Route.Vehicle.Name : "",
                                   DeliveryRouteVehicleRegistration = o.RouteId != null && o.Route.Vehicle != null ? o.Route.Vehicle.Registration : "",
                                   OrderLines = o.OrderLines.Select(ol => new OrderLineModel
                                   {
                                       Id = ol.Id,
                                       LineNumber = ol.LineNumber,
                                       ProductId = ol.ProductId,
                                       ProductName = ol.Product != null ? ol.Product.Name : string.Empty,
                                       Total = ol.Total,
                                       VATTotal = ol.VATTotal,
                                       UnitTotal = ol.UnitTotal,
                                       Quantity = (int)ol.Quantity,
                                       CustomUnitPrice = ol.CustomUnitPrice,
                                       UnitPrice = ol.UnitPrice,
                                       VATRate = ol.VATRate,
                                   }).OrderBy(ol => ol.LineNumber).ToList(),
                                   DeliveryAddressId = o.DeliveryAddressId,
                                   UnitTotal = o.UnitTotal,
                                   VATTotal = o.VATTotal,
                                   Total = o.Total
                               })
                               .FirstOrDefaultAsync();

        orderModel.DeliveryAddress = await addressService.GetAddressAsync(orderModel.DeliveryAddressId);

        return orderModel;
    }

    /// <summary>
    /// Deletes an order.
    /// </summary>
    public async Task<FormResponseModel<bool>> DeleteOrderAsync(Guid orderId)
    {
        var order = await databaseContext.Orders.Include(i => i.OrderLines)
                                    .Include(i => i.RouteLines)
                                    .Where(i => i.Id == orderId)
                                    .FirstOrDefaultAsync();

        databaseContext.OrderLines.RemoveRange(order.OrderLines);
        databaseContext.RouteLines.RemoveRange(order.RouteLines);
        databaseContext.Orders.Remove(order);

        await portalEventService.LogAsync(PortalEventType.OrderDeleted, orderId);

        await databaseContext.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Used to update payment type or date or order. If payment type is changed it updates it on the route.
    /// </summary>
    public async Task<FormResponseModel<bool>> UpdateOrderDetailsAsync(OrderModel orderModel)
    {
        var order = await databaseContext.Orders.Where(i => i.Id == orderModel.Id).FirstOrDefaultAsync();

        order.OrderDate = orderModel.OrderDate.Value;
        order.OrderDeliveryNotes = orderModel.OrderDeliveryNotes;
        order.RoundOrder = orderModel.RoundOrder;

        if (orderModel.DeliveryType is ((int)OrderDeliveryType.None) or ((int)OrderDeliveryType.NotRequired) or ((int)OrderDeliveryType.PickupOnly))
        {
            order.RouteId = null;

            var routeLine = await databaseContext.RouteLines.Where(i => i.OrderId == order.Id && i.RouteId == order.RouteId).FirstOrDefaultAsync();

            if (routeLine != null)
            {
                await routeService.DeleteOrderOnRouteAsync(routeLine.Id);
            }
        }

        if (orderModel.DeliveryType == (int)OrderDeliveryType.Route)
        {
            if (orderModel.DeliveryRouteId == null)
            {
                order.DeliveryStatus = (int)OrderDeliveryStatus.NotOnRoute;
            }

            if (orderModel.DeliveryRouteId != order.RouteId)
            {
                order.DeliveryStatus = (int)OrderDeliveryStatus.OnDeliveryRoute;

                var routeLine = await databaseContext.RouteLines.Where(i => i.OrderId == order.Id && i.RouteId == order.RouteId).FirstOrDefaultAsync();
                if (routeLine != null)
                {
                    await routeService.DeleteOrderOnRouteAsync(routeLine.Id);
                }

                if (orderModel.DeliveryRouteId != null)
                {
                    await routeService.AddOrderToRouteAsync(orderModel.DeliveryRouteId.Value, orderModel.Id);
                }
            }
        }

        string changes = databaseContext.GetChangedProperties(order);
        await portalEventService.LogAsync(PortalEventType.OrderUpdated, order.Id, changes);

        await databaseContext.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Lists customers to make an order for.
    /// </summary>
    public async Task<List<DropdownListItem>> ListCustomersForOrderDropdownAsync()
    {
        var q = await databaseContext.Customers
        .Where(w => w.TenantId == currentUserService.TenantId)
        .Where(w => w.Status == (int)GenericStatus.Active)
        .Select(s => new DropdownListItem
        {
            Id = s.Id.ToString(),
            Name = s.CustomerAddressId == null
                    ? s.Name
                    : s.Name
                        + " - "
                        + s.CustomerAddress.AddressLine1
                        + " - "
                        + s.CustomerAddress.AddressLine2
                        + " - "
                        + s.CustomerAddress.AddressLine3
                        + " - "
                        + s.CustomerAddress.AddressLine4
                        + " - "
                        + s.CustomerAddress.AddressPostcode
        })
        .OrderBy(o => o.Name)
        .ToListAsync();

        return q;
    }

    /// <summary>
    /// Lists products that can be ordered.
    /// </summary>
    public async Task<List<DropdownListItem>> ListProductsDropdownAsync()
    {
        var q = await databaseContext.Products
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.Type == (int)ProductType.Product)
            //.Where(w => w.Status == (int)GenericStatus.Active)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = s.Name
            })
            .OrderBy(o => o.Name)
            .ToListAsync();

        return q;
    }

    /// <summary>
    /// Marks an order as complete meaning it's fully locked now.
    /// </summary>
    public async Task<FormResponseModel<bool>> MarkAsCompletedAsync(Guid orderId)
    {
        var order = await databaseContext.Orders.Include(i => i.OrderLines).Where(i => i.Id == orderId).FirstOrDefaultAsync();
        order.Status = (int)OrderStatus.Completed;

        await portalEventService.LogAsync(PortalEventType.OrderUpdated, order.Id, "Order marked as completed");

        await databaseContext.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    /// <summary>
    /// Finds all available routes for the order.
    /// </summary>
    public async Task<List<OrderRouteItemModel>> GetAvailableRoutesAsync(Guid orderId, Guid? routeAreaId, DateTime? date)
    {
        var query = databaseContext.Routes.Where(w => w.TenantId == currentUserService.TenantId)
                              .Where(w => w.Status == (int)RouteStatus.Open)
                              .Where(w => w.RouteDate.Date == date.Value.Date);

        if (routeAreaId != null)
        {
            query = query.Where(w => w.RouteAreaId == routeAreaId);
        }

        var vehicleDrivers = await query
            .Select(s => new OrderRouteItemModel
            {
                Id = s.Id,
                RouteDate = s.RouteDate,
                VehicleName = s.Vehicle.Name,
                VehicleRegistration = s.Vehicle.Registration,
                RouteArea = s.RouteArea.Name,
                VehicleDriver = s.VehicleDriver.FirstName + " " + s.VehicleDriver.LastName,
            })
            .OrderBy(o => o.RouteDate)
            .ThenBy(o => o.RouteArea)
            .ToListAsync();

        return vehicleDrivers;
    }

    /// <summary>
    /// Get the edit order model for editing a order on the route screen.
    /// </summary>
    public async Task<RouteEditOrderModel> GetRouteEditOrderAsync(Guid routeLineId)
    {
        var routeLine = await databaseContext.RouteLines
                              .Where(w => w.Id == routeLineId)
                              .Select(s => new
                              {
                                  Id = s.Id.ToString(),
                                  s.OrderId
                              })
                              .FirstOrDefaultAsync();

        var routeEditOrderModel = new RouteEditOrderModel
        {
            OrderModel = await this.GetOrderAsync(routeLine.OrderId.Value),
        };

        return routeEditOrderModel;
    }

    /// <summary>
    /// Gets customer order details for dialog.
    /// </summary>
    public async Task<CustomerOrderSnapshot> GetCustomerOrderSnapshotAsync(Guid customerId)
    {
        var customer = await databaseContext.Customers
                    .Include(i => i.CustomerTagLinks)
                    .ThenInclude(t => t.CustomerTag)
                    .Include(i => i.RouteArea)
                    .FirstOrDefaultAsync(c => c.Id == customerId);

        return new CustomerOrderSnapshot
        {
            Name = customer.Name,
            CustomerNumber = customer.CustomerNumber,

            PreferredDeliveryDay = GetDayOfWeekText(customer.PreferredDeliveryDay),

            PhoneNumber = customer.PhoneNumber,
            MobilePhoneNumber = customer.MobilePhoneNumber,

            CustomerLedger = await customerPaymentService.GetCustomerLedger(customerId, DateTime.Now.Date),
            FutureOrdersList = await this.GetCustomerFutureOrdersAsync(customerId),
            PreviousOrdersList = await this.GetCustomerPreviousOrdersAsync(customerId),
            CustomerPayments = await this.GetCustomerPaymentsAsync(customerId),
            RouteArea = customer.RouteArea != null ? customer.RouteArea.Name : string.Empty,
            CustomerTags = customer.CustomerTagLinks.Select(s => s.CustomerTag.Name).ToList()
        };
    }

    public static string GetDayOfWeekText(int dayNumber)
    {
        return dayNumber switch
        {
            1 => "Monday",
            2 => "Tuesday",
            3 => "Wednesday",
            4 => "Thursday",
            5 => "Friday",
            6 => "Saturday",
            7 => "Sunday",
            _ => "",
        };
    }

    public async Task<List<CustomerOrderListModel>> GetCustomerFutureOrdersAsync(Guid customerId)
    {
        var futureOrders = await databaseContext.Orders.Where(w => w.CustomerId == customerId)
                                           .Where(w => w.OrderDate >= DateTime.Now)
                                           .OrderBy(o => o.OrderDate)
                                           .Take(5)
                                           .Select(s => new CustomerOrderListModel
                                           {
                                               Id = s.Id,
                                               OrderNumber = s.OrderNumber.ToString(),
                                               OrderDate = s.OrderDate.ToString("dd/MM/yyyy"),
                                               Summary = string.Join(", ", s.OrderLines.Select(ol => $"{ol.Product.Name} (Qty: {ol.Quantity}, Total: {ol.Total:C})")),
                                               Total = s.Total.ToString("C")
                                           })
                                           .ToListAsync();
        return futureOrders;
    }

    public async Task<List<CustomerOrderListModel>> GetCustomerPreviousOrdersAsync(Guid customerId)
    {
        var previousOrders = await databaseContext.Orders.Where(w => w.CustomerId == customerId)
                                             .Where(w => w.OrderDate < DateTime.Now)
                                             .OrderByDescending(o => o.OrderDate)
                                             .Take(5)
                                             .Select(s => new CustomerOrderListModel
                                             {
                                                 Id = s.Id,
                                                 OrderNumber = s.OrderNumber.ToString(),
                                                 OrderDate = s.OrderDate.ToString("dd/MM/yyyy"),
                                                 Summary = string.Join(", ", s.OrderLines.Select(ol => $"{ol.Product.Name} (Qty: {ol.Quantity}, Total: {ol.Total:C})")),
                                                 Total = s.Total.ToString("C")
                                             })
                                             .ToListAsync();
        return previousOrders;
    }

    public async Task<List<CustomerPaymentListItemModel>> GetCustomerPaymentsAsync(Guid customerId)
    {
        var payments = await databaseContext.CustomerPayments
                                .Where(w => w.TenantId == currentUserService.TenantId)
                                .Where(w => w.CustomerId == customerId)
                                .OrderByDescending(o => o.Date)
                                .Select(o => new CustomerPaymentListItemModel
                                {
                                    Id = o.Id,
                                    Date = o.Date,
                                    PaymentType = o.PaymentType,
                                    Total = o.Total,
                                    CustomerName = o.Customer.Name
                                })
                                .Take(5)
                                .ToListAsync();
        return payments;
    }

    /// <summary>
    /// Used to update order notes.
    /// </summary>
    public async Task<FormResponseModel<bool>> UpdateOrderNotesAsync(OrderModel orderModel)
    {
        var order = await databaseContext.Orders.Where(i => i.Id == orderModel.Id).FirstOrDefaultAsync();

        order.OrderDeliveryNotes = orderModel.OrderDeliveryNotes;

        string changes = databaseContext.GetChangedProperties(order);
        await portalEventService.LogAsync(PortalEventType.OrderUpdated, order.Id, changes);

        await databaseContext.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    public async Task<GridResultModel<CustomerOrderLedgerModel>> ListCustomerLedgerAsync(Guid customerId, GridParametersModel gridParametersModel)
    {
        var listCustomerOrderLedgerModel = new List<CustomerOrderLedgerModel>();

        var payments = await databaseContext.CustomerPayments
                    .Where(w => w.TenantId == currentUserService.TenantId)
                    .Where(w => w.CustomerId == customerId)
                    .Select(o => new CustomerOrderLedgerModel
                    {
                        Id = o.Id,
                        Date = o.Date,
                        PaymentType = o.PaymentType,
                        Type = "Payment",
                        Amount = o.Total,

                    })
                    .ToListAsync();


        listCustomerOrderLedgerModel.AddRange(payments);

        var previousOrders = await databaseContext.Orders.Where(w => w.CustomerId == customerId)
                                 .Where(w => w.OrderDate < DateTime.Now)
                                 .Select(s => new CustomerOrderLedgerModel
                                 {
                                     Id = s.Id,
                                     //Date = s.RouteLines != null && s.RouteLines.Any() ? s.RouteLines.First().Route.RouteDate : null,
                                     Date = s.Route != null ? s.Route.RouteDate : null,
                                     RouteId = s.Route != null ? s.Route.Id : null,
                                     Type = "Order",
                                     //Details = string.Join(", ", s.OrderLines.Select(ol => $"{ol.Product.Name} x {ol.Quantity} £{ol.UnitTotal}").ToList()
                                     Details = string.Concat(s.OrderLines.Select(ol =>
                                        $"<div class=\"ledger-order-line\">" +
                                        $"<div>{ol.Product.Name}</div>" +
                                        $"<div>Qty: {ol.Quantity}</div>" +
                                        $"<div>Total: {ol.Total:C}</div>" +
                                        $"</div>"
                                    )),
                                     Amount = s.Total,
                                 })
                                 .ToListAsync();

        listCustomerOrderLedgerModel.AddRange(previousOrders);

        var results = await GridHelper.CreateGridAsync(listCustomerOrderLedgerModel.AsQueryable(), gridParametersModel);

        decimal balance = 0;

        foreach (var transaction in results.Results.OrderBy(o => o.Date))
        {
            if (transaction.Type == "Order")
            {
                balance -= transaction.Amount;
                transaction.Balance = balance;
            }
            else
            {
                balance += transaction.Amount;
                transaction.Balance = balance;
            }
        }

        return results;
    }

}
