﻿using HT.Database;
using HT.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using HT.Blazor.Models.Form;
using HT.Shared.Models.Customer;
using HT.Shared.Models.Customer.Payment;

namespace HT.Api.Services.Customer;

public class CustomerPaymentService(DatabaseContext db, CurrentUserService currentUserService)
{
    public async Task<CustomerLedgerModel> GetCustomerLedger(Guid customerId, DateTime? ordersBeforeDate = null)
    {
        return new CustomerLedgerModel
        {
            OrdersTodayAndBeforeTotal = ordersBeforeDate != null ? await db.Orders
            .Where(o => o.CustomerId == customerId &&
                        o.Status != (int)OrderStatus.Cancelled &&
                        o.RouteLines != null &&
                        o.RouteLines.FirstOrDefault().Route.RouteDate.Date <= ordersBeforeDate)
            .SumAsync(s => s.Total) : 0.0m,
            PaymentsTodayAndBeforeTotal = await db.CustomerPayments.Where(o => o.CustomerId == customerId && o.Date.Date <= ordersBeforeDate).SumAsync(s => s.Total),

            OrdersTotal = await db.Orders.Where(o => o.CustomerId == customerId).SumAsync(s => s.Total),
            PaymentsTotal = await db.CustomerPayments.Where(o => o.CustomerId == customerId).SumAsync(s => s.Total),
        };
    }

    /// <summary>
    /// Adds a customer payment.
    /// </summary>
    /// <param name="orderModel"></param>
    /// <returns></returns>
    public async Task<FormResponseModel<bool>> AddCustomerPaymentAsync(AddPaymentModel addPaymentModel)
    {
        var payment = new CustomerPayment
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            CustomerId = addPaymentModel.CustomerId,
            Date = DateTime.Now,
            PaymentType = addPaymentModel.PaymentType,
            Total = addPaymentModel.PaymentAmount,
            RouteId = addPaymentModel.RouteId != Guid.Empty ? addPaymentModel.RouteId : null,
        };
        db.CustomerPayments.Add(payment);

        await db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    public async Task<List<CustomerPaymentListItemModel>> ListCustomerPaymentsForRoute(Guid routeId)
    {
        var payments = await db.CustomerPayments
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.RouteId == routeId)
                   .Select(o => new CustomerPaymentListItemModel
                   {
                       Id = o.Id,
                       Date = o.Date,
                       PaymentType = o.PaymentType,
                       Total = o.Total,
                       CustomerName = o.Customer.Name
                   })
                   .ToListAsync();

        return payments;
    }
}
