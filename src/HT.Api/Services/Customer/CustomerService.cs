﻿using HT.Api.Services.Common.Address;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HT.Blazor.Models.Form;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Blazor.Models.Field;

namespace HT.Api.Services.Customer;

public class CustomerService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly AddressService _addressService;
    private readonly CustomerPaymentService _customerLedgerService;

    public CustomerService(DatabaseContext db, CurrentUserService currentUserService, AddressService addressService, CustomerPaymentService customerLedgerService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _addressService = addressService;
        _customerLedgerService = customerLedgerService;
    }

    public async Task<CustomerCreateModel> CreateCustomer(CustomerCreateModel customerCreateModel)
    {
        customerCreateModel.Id = Guid.NewGuid();

        int nextNumber = await _db.Customers
                            .Where(s => s.TenantId == _currentUserService.TenantId)
                            .Select(s => s.CustomerNumber)
                            .DefaultIfEmpty()
                            .MaxAsync();

        _db.Customers.Add(new HT.Database.Customer
        {
            Id = customerCreateModel.Id,
            TenantId = _currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            CustomerNumber = nextNumber + 1,
            Code = customerCreateModel.Code,
            Name = customerCreateModel.Name,
        });

        if (customerCreateModel.CreateHaulier)
        {
            var haulierId = Guid.NewGuid();

            _db.Hauliers.Add(new Database.Haulier
            {
                Id = haulierId,
                TenantId = _currentUserService.TenantId,
                Status = (int)GenericStatus.Active,
                Code = customerCreateModel.Code,
                Name = customerCreateModel.Name,
            });

            _db.HaulierCustomerLinks.Add(new HaulierCustomerLink
            {
                Id = Guid.NewGuid(),
                TenantId = _currentUserService.TenantId,
                HaulierId = haulierId,
                CustomerId = customerCreateModel.Id,
            });
        }

        await _db.SaveChangesAsync();

        return customerCreateModel;
    }

    public async Task<CustomerModel> GetCustomer(Guid id)
    {
        var customerModel = await _db.Customers
                   .Where(i => i.Id == id)
                   .Select(s => new CustomerModel
                   {
                       Id = s.Id,
                       CustomerNumber = s.CustomerNumber,
                       Status = s.Status,
                       Code = s.Code,
                       Name = s.Name,
                       OnStop = s.OnStop,
                       PaymentTerms = s.PaymentTerms,
                       Notes = s.Notes,
                       InvoiceEmail = s.InvoiceEmail,
                       ETicketEmail = s.ETicketEmail,

                       CustomerAddressId = s.CustomerAddressId,
                       PreferredDeliveryDay = s.PreferredDeliveryDay,
                       FirstRoundOrderDate = s.FirstRoundOrderDate,

                       PhoneNumber = s.PhoneNumber,
                       MobilePhoneNumber = s.MobilePhoneNumber,

                       DeliveryNotes = s.DeliveryNotes,
                       RouteAreaId = s.RouteAreaId,
                       DefaultPaymentType = s.DefaultPaymentType,
                       AddressName = s.AddressName,
                       OrderFrequencyDays = s.OrderFrequencyDays,

                       CustomerTags = s.CustomerTagLinks.Select(t => t.CustomerTagId).ToList()
                   })
                   .FirstOrDefaultAsync();

        customerModel.CustomerAddress = await _addressService.GetAddressAsync(customerModel.CustomerAddressId);

        customerModel.CustomerLedger = await _customerLedgerService.GetCustomerLedger(id, DateTime.Now.Date);

        return customerModel;
    }

    public async Task<FormResponseModel<CustomerModel>> UpdateCustomer(CustomerModel customerModel)
    {
        var customer = await _db.Customers.Include(i => i.CustomerTagLinks).FirstOrDefaultAsync(s => s.Id == customerModel.Id);

        customer.Status = customerModel.Status;
        customer.Code = customerModel.Code;
        customer.Name = customerModel.Name;
        customer.OnStop = customerModel.OnStop;
        customer.PaymentTerms = customerModel.PaymentTerms;
        customer.Notes = customerModel.Notes;

        customer.InvoiceEmail = customerModel.InvoiceEmail;
        customer.ETicketEmail = customerModel.ETicketEmail;

        customer.PhoneNumber = customerModel.PhoneNumber;
        customer.MobilePhoneNumber = customerModel.MobilePhoneNumber;

        customer.DeliveryNotes = customerModel.DeliveryNotes;
        customer.RouteAreaId = customerModel.RouteAreaId;
        customer.DefaultPaymentType = customerModel.DefaultPaymentType;
        customer.AddressName = customerModel.AddressName;
        customer.OrderFrequencyDays = customerModel.OrderFrequencyDays;
        customer.FirstRoundOrderDate = customerModel.FirstRoundOrderDate;

        customer.PreferredDeliveryDay = customerModel.PreferredDeliveryDay;
        customer.CustomerAddressId = await _addressService.CreateUpdateAddressAsync(customerModel.CustomerAddress);

        // Update orders
        var orders = await _db.Orders.Include(o => o.DeliveryAddress).Where(o => o.Status == (int)OrderStatus.Open && o.CustomerId == customer.Id).ToListAsync();
        foreach (var order in orders)
        {
            order.DeliveryAddress.AddressLine1 = customerModel.CustomerAddress.AddressLine1;
            order.DeliveryAddress.AddressLine2 = customerModel.CustomerAddress.AddressLine2;
            order.DeliveryAddress.AddressLine3 = customerModel.CustomerAddress.AddressLine3;
            order.DeliveryAddress.AddressLine4 = customerModel.CustomerAddress.AddressLine4;
            order.DeliveryAddress.AddressPostcode = customerModel.CustomerAddress.AddressPostcode;
            order.DeliveryAddress.Latitude = customerModel.CustomerAddress.Latitude;
            order.DeliveryAddress.Longitude = customerModel.CustomerAddress.Longitude;
        }

        _db.CustomerTagLinks.RemoveRange(customer.CustomerTagLinks);
        foreach (var item in customerModel.CustomerTags)
        {
            _db.CustomerTagLinks.Add(new CustomerTagLink
            {
                Id = Guid.NewGuid(),
                TenantId = customer.TenantId,
                CustomerTagId = item,
                CustomerId = customer.Id,
            });
        }

        await _db.SaveChangesAsync();

        return new FormResponseModel<CustomerModel>(customerModel);
    }

    public async Task<FormResponseModel<bool>> DeleteCustomer(Guid id)
    {
        var customer = await _db.Customers.FirstOrDefaultAsync(c => c.Id == id);
        customer.Status = (int)GenericStatus.Deleted;
        await _db.SaveChangesAsync();

        return new FormResponseModel<bool>(true);
    }

    public async Task<GridResultModel<CustomerListItemModel>> ListCustomers([FromBody] GridParametersModel gridParametersModel)
    {
        var q = _db.Customers
        .Where(w => w.TenantId == _currentUserService.TenantId)
        .Where(w => w.Status != (int)GenericStatus.Deleted)
        .Select(s => new CustomerListItemModel
        {
            Id = s.Id,
            Status = s.Status,
            CustomerNumber = s.CustomerNumber,
            CustomerNumberString = s.CustomerNumber.ToString(),
            Code = s.Code,
            Name = s.Name,
            Contracts = s.ContractLinks.Count,
            Visits = s.ETickets.Count,
            OnStop = s.OnStop,
            RouteArea = s.RouteArea.Name,
            CustomerTags = s.CustomerTagLinks.Select(t => t.CustomerTag.Name).ToList(),
            OrdersTotal = s.Orders.Where(o => o.Status != (int)OrderStatus.Cancelled && o.RouteLines.FirstOrDefault().Route.RouteDate.Date <= DateTime.Now.Date).Sum(s => s.Total),
            PaymentsTotal = s.CustomerPayments.Sum(sum => sum.Total),
            AddressLine1 = s.CustomerAddress.AddressLine1,
            Postcode = s.CustomerAddress.AddressPostcode,
            Phone = s.PhoneNumber,
            Mobile = s.MobilePhoneNumber
        })
        .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<List<DropdownListItem>> ListActiveCustomersDropdownAsync()
    {
        return await _db.Customers
            .Where(w => w.TenantId == _currentUserService.TenantId)
            .Where(w => w.Status == (int)GenericStatus.Active)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = s.Name
            })
            .OrderBy(o => o.Name)
            .ToListAsync();
    }
}
