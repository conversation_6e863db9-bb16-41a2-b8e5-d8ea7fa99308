﻿using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Customer;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Customer;

public class CustomerSearchService(DatabaseContext db, CurrentUserService currentUserService)
{
    public async Task<List<CustomerSearchItemModel>> SearchAsync(string searchTerm)
    {
        var q = await db.Customers
                         .Where(w => w.TenantId == currentUserService.TenantId)
                         .Where(w => w.Status == (int)GenericStatus.Active)
                         .Where(w =>
                            w.Name.Contains(searchTerm) ||
                            w.CustomerNumber.ToString().Contains(searchTerm) ||
                            w.PhoneNumber.Contains(searchTerm) ||
                            w.MobilePhoneNumber.Contains(searchTerm) ||
                            w.AddressLine1.Contains(searchTerm) ||
                            w.AddressLine2.Contains(searchTerm) ||
                            w.AddressLine3.Contains(searchTerm) ||
                            w.AddressLine4.Contains(searchTerm) ||
                            w.AddressPostcode.Contains(searchTerm)
                         )
                         .Select(s => new CustomerSearchItemModel
                         {
                             Id = s.Id,
                             Name = s.Name,
                             CustomerNumber = s.CustomerNumber,
                             PhoneNumber = s.PhoneNumber,
                             MobilePhoneNumber = s.MobilePhoneNumber,
                             AddressLine1 = s.CustomerAddress.AddressLine1,
                             AddressLine2 = s.CustomerAddress.AddressLine2,
                             AddressLine3 = s.CustomerAddress.AddressLine3,
                             AddressLine4 = s.CustomerAddress.AddressLine4,
                             AddressPostcode = s.CustomerAddress.AddressPostcode,
                             CustomerTags = s.CustomerTagLinks.Select(s => s.CustomerTag.Name).ToList()
                         })
                         .OrderBy(o => o.Name)
                         .Take(10)
                         .ToListAsync();

        return q;
    }

}
