﻿using DriverAppModels;
using HT.Api.Services.Common.Address;
using HT.Api.Services.Common.RouteXL;
using HT.Api.Services.Haulier;
using HT.Database;
using HT.Shared.Models.HaulierRoute;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.DriverAppService;

public class DriverAppService(
    DatabaseContext db,
    CurrentUserService currentUserService)
{
    public async Task<List<DriverAppRouteListItemModel>> ListDriverAppRoutesAsync()
    {
        var q = db.Routes
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .AsQueryable();

        q = q.Where(w => w.HaulierDriverUserId == currentUserService.UserId);

        var query = await q.Select(o => new DriverAppRouteListItemModel
        {
            Id = o.Id.ToString(),
            Day = o.RouteDate.DayOfWeek.ToString(),
            Date = o.RouteDate,
            StopCount = o.RouteLines.Count,
            Status = o.Status.ToString()
        }).OrderByDescending(o => o.Date)
        .ToListAsync();

        return query;
    }


    public async Task<DriverAppViewRouteModel> ViewRouteAsync(Guid id)
    {
        var route = await db.Routes.Where(w => w.Id == id)
            .Select(s => new DriverAppViewRouteModel
            {
                Id = s.Id.ToString(),
                Day = s.RouteDate.DayOfWeek.ToString(),
                Date = s.RouteDate,
                StopCount = s.RouteLines.Count,
                Status = s.Status.ToString(),

                Stops = s.RouteLines.OrderBy(o => o.StopNumber).Select(l => new DriverAppRouteStopModel
                {
                    Id = l.Id,
                    StopNumber = l.StopNumber,
                    Time = l.HaulierStop.Time,
                    HaulierStopId = l.HaulierStopId,

                    //DeliveryAddressLatitude = l.HaulierStop != null ? l.HaulierStop.Address.Latitude : null,
                    //DeliveryAddressLongitude = l.Visit != null ? l.HaulierStop.Address.Longitude : null,

                    DeliveryAddressLine1 = l.HaulierStop.Address.AddressLine1,
                    DeliveryAddressPostcode = l.HaulierStop.Address.AddressPostcode,
                    DeliveryAddressLine2 = l.HaulierStop.Address.AddressLine2,
                    DeliveryAddressLine3 = l.HaulierStop.Address.AddressLine3,
                    DeliveryAddressLine4 = l.HaulierStop.Address.AddressLine4,

                    PhoneNumber = l.HaulierStop.PhoneNumber,
                    MobileNumber = l.HaulierStop.MobilePhoneNumber,

                    Notes = l.HaulierStop.Notes,
                    PhotoRequired = l.HaulierStop.PhotoRequired,
                    SignatureRequired = l.HaulierStop.SignatureRequired,
                    Type = l.HaulierStop.Type,

                    Photos = l.HaulierStop.HaulierStopFiles.Select(s => new PhotoModel
                    {
                        Url = s.AzureStorageURL
                    }).ToList(),

                }).OrderBy(o => o.StopNumber).ToList(),



            }).FirstOrDefaultAsync();

        //foreach (var stop in route.Stops)
        //{
        //    stop.Photos = new List<PhotoModel>
        //            {
        //                new PhotoModel
        //                {
        //                    Url = "https://localhost:7068/img/Heavy-Tech-Logo.svg",
        //                },
        //                new PhotoModel
        //                {
        //                    Url = "https://via.placeholder.com/150",
        //                }
        //            };
        //}

        return route;
    }



}
