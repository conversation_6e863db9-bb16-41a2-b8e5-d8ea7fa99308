﻿using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Product;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services;

public class ProductPriceService(DatabaseContext db)
{
    public async Task<ProductPriceListItemModel> GetProductPriceAsync(Guid productId, Guid customerId)
    {
        var price = await db.ProductPrices
                 .Where(w => w.ProductId == productId)
                 .Where(w => w.CustomerId == null || w.CustomerId == customerId)
                 .Where(w => w.EffectiveDate <= DateTime.Now)
                 .OrderByDescending(o => o.CustomerId)
                 .ThenByDescending(s => s.EffectiveDate)
                 .Select(s => new ProductPriceListItemModel
                 {
                     Price = s.Price,
                     VATPercent = s.VATPercent,
                 })
                 .FirstOrDefaultAsync();

        price ??= new ProductPriceListItemModel { Price = 0, VATPercent = 20 };

        return price;
    }

    public async Task<ProductPriceListItemModel> GetProductPriceAsync(Guid productId, Guid customerId, ProductPricingMethod productPricingMethod)
    {
        var price = await db.ProductPrices
                 .Where(w => w.ProductId == productId)
                 .Where(w => w.CustomerId == null || w.CustomerId == customerId)
                 .Where(w => w.EffectiveDate <= DateTime.Now)
                 .Where(w => w.PricingMethod == (int)productPricingMethod)
                 .OrderByDescending(o => o.CustomerId)
                 .ThenByDescending(s => s.EffectiveDate)
                 .Select(s => new ProductPriceListItemModel
                 {
                     Price = s.Price,
                     VATPercent = s.VATPercent,
                 })
                 .FirstOrDefaultAsync();

        price ??= new ProductPriceListItemModel { Price = 0, VATPercent = 20 };

        return price;
    }

}
