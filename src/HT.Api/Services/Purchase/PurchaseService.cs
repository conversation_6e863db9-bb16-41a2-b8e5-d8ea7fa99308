﻿using Microsoft.EntityFrameworkCore;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using Microsoft.AspNetCore.Mvc;
using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Grid;
using HT.Blazor.Models.Form;
using HT.Shared.Models.Document;

namespace HT.Api.Services.Purchase;

public class PurchaseService(DatabaseContext db, CurrentUserService currentUserService)
{
    public async Task<GridResultModel<PurchaseListItemModel>> ListPurchases([FromBody] GridParametersModel gridParametersModel, Guid? supplierId)
    {
        var q = db.Purchases
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .AsQueryable();

        if (supplierId != null)
        {
            q = q.Where(w => w.SupplierId == supplierId);
        }

        var query = q.Select(s => new PurchaseListItemModel
        {
            Id = s.Id,
            PurchaseOrderNumber = s.PurchaseOrderNumber,
            SupplierName = s.Supplier.Name,
            PurchaseDate = s.PurchaseDate,
            Total = s.Total,
            Paid = s.Paid,
        })
                   .AsQueryable();

        var purchaseList = await GridHelper.CreateGridAsync(query, gridParametersModel);

        return purchaseList;
    }

    public async Task<PurchaseModel> CreatePurchase([FromBody] PurchaseModel purchaseModel)
    {
        purchaseModel.Id = Guid.NewGuid();

        db.Purchases.Add(new Database.Purchase
        {
            Id = purchaseModel.Id,
            TenantId = currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            PurchaseOrderNumber = purchaseModel.PurchaseOrderNumber,
            PurchaseDate = (DateTime)purchaseModel.PurchaseDate,
            Total = purchaseModel.Total,
            Paid = purchaseModel.Paid,
            SupplierId = purchaseModel.SupplierId,
            PaymentDueDate = purchaseModel.PaymentDueDate,
        });

        await db.SaveChangesAsync();

        return purchaseModel;
    }

    public async Task<PurchaseModel> GetPurchase(Guid purchaseId)
    {
        var purchase = await db.Purchases
                    .Where(p => p.Id == purchaseId)
                    .Select(s => new PurchaseModel
                    {
                        Id = s.Id,
                        Status = s.Status,
                        PurchaseOrderNumber = s.PurchaseOrderNumber,
                        SupplierId = s.SupplierId,
                        PurchaseDate = s.PurchaseDate,
                        Total = s.Total,
                        Paid = s.Paid,
                        PaymentDueDate = s.PaymentDueDate,
                    })
                    .FirstOrDefaultAsync();

        if (purchase != null)
        {
            var document = await db.Documents
                                .Where(d => d.PurchaseId == purchase.Id)
                                .Select(s => new DocumentModel
                                {
                                    Id = s.Id,
                                    AzureStorageURL = s.AzureStorageURL,
                                    Name = s.Name,
                                    UploadedDate = s.UploadedDate,
                                })
                                .FirstOrDefaultAsync();

            if (document != null)
            {
                purchase.Document = document;
            }
        }

        return purchase;
    }

    public async Task<FormResponseModel<PurchaseModel>> SavePurchase(PurchaseModel purchaseModel)
    {
        var purchase = await db.Purchases.FirstOrDefaultAsync(p => p.Id == purchaseModel.Id);

        purchase.Status = purchaseModel.Status;
        purchase.PurchaseOrderNumber = purchaseModel.PurchaseOrderNumber;
        purchase.SupplierId = purchaseModel.SupplierId;
        purchase.PurchaseDate = (DateTime)purchaseModel.PurchaseDate;
        purchase.Total = purchaseModel.Total;
        purchase.Paid = purchaseModel.Paid;
        purchase.PaymentDueDate = purchaseModel.PaymentDueDate;

        await db.SaveChangesAsync();

        return new FormResponseModel<PurchaseModel>(purchaseModel);
    }
}
