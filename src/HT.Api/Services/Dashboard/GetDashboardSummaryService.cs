using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Dashboard;
using HT.Shared.Models.ETicket;
using HT.Shared.Models.User;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Dashboard;

public class GetDashboardSummaryService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;

    public GetDashboardSummaryService(DatabaseContext db, CurrentUserService currentUserService)
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    public async Task<DashboardSummaryModel> GetDashboardSummaryAsync(DashboardFiltersModel dashboardFiltersModel)
    {
        var userSecurity = await _currentUserService.GetUserSecurityAsync();

        var eTicketsQuery = _db.ETickets
                            .Include(i => i.Customer)
                            .Include(i => i.Product)
                            .Where(s => s.TenantId == _currentUserService.TenantId &&
                                s.EntryDateTime >= dashboardFiltersModel.StartDay &&
                                s.EntryDateTime <= dashboardFiltersModel.EndDay &&
                                s.Status != (int)ETicketStatus.Void
                            ).AsQueryable();

        if (userSecurity.UserType is not ((int)UserType.SuperUser) and not ((int)UserType.Admin))
        {
            eTicketsQuery = eTicketsQuery.Where(s => userSecurity.Sites.Contains(s.SiteId));
        }

        var eTickets = await eTicketsQuery.ToListAsync();

        var wasteEticketsList = GetWasteETickets(eTickets);

        var aggregateEticketsList = GetAggregateETickets(eTickets);

        var aggregateEticketsProductsList = GetETicketProducts(eTickets);

        var haulierStops = await _db.HaulierStops
                        .Include(i => i.Route)
                        .Include(i => i.ContractLink)
                            .ThenInclude(i => i.Customer)
                        .Where(s => s.Type != (int)HaulierStopType.TipStop)
                        .Where(s => s.TenantId == _currentUserService.TenantId &&
                            s.Route.RouteDate >= dashboardFiltersModel.StartDay &&
                            s.Route.RouteDate <= dashboardFiltersModel.EndDay
                        )
                        .GroupBy(g => g.ContractLink.CustomerId)
                        .ToListAsync();

        var haulierStopsList = new List<HaulierStopSummaryModel>();

        foreach (var haulierStopGroup in haulierStops)
        {
            var haulierStop = new HaulierStopSummaryModel
            {
                CustomerName = haulierStopGroup.First().ContractLink.Customer.Name,
                TotalStops = haulierStopGroup.Count(),
                TotalWagons = haulierStopGroup.Select(s => s.Route.HaulierVehicleId).Distinct().Count(),
            };

            foreach (var stop in haulierStopGroup)
            {
                decimal price = 0m;

                if (stop.PricePerLoad != 0)
                {
                    price = stop.PricePerLoad.Value;
                }

                if (stop.PricePerTonne != 0)
                {
                    price = stop.PricePerTonne.Value * stop.Tonnes;
                }

                haulierStop.TotalAmount += price;
            }

            haulierStopsList.Add(haulierStop);

        }

        var jobs = await _db.Jobs
                    .Include(i => i.Customer)
                    .Include(i => i.Products)
                    .Where(s => s.TenantId == _currentUserService.TenantId &&
                        s.EntryDateTime >= dashboardFiltersModel.StartDay &&
                        s.EntryDateTime <= dashboardFiltersModel.EndDay &&
                        s.Status != (int)JobStatus.Void &&
                        !s.IsInternalTransfer
                    ).ToListAsync();


        var jobWasteList = GetWasteJobs(jobs);
        var jobProductList = GetAggregateJobs(jobs);

        var dashboardSummaryModel = new DashboardSummaryModel
        {
            CustomerCount = await _db.Customers.Where(s => s.TenantId == _currentUserService.TenantId).CountAsync(),
            ContractCount = await _db.Contracts.Where(s => s.TenantId == _currentUserService.TenantId).CountAsync(),
            ETicketsCount = eTickets.Count + jobs.Count,
            Total = eTickets.Sum(s => s.TotalPrice) + jobs.SelectMany(s => s.Products).Sum(s => s.UnitTotal),

            WasteETickets = wasteEticketsList.OrderByDescending(o => o.TotalETickets).ToList(),
            WasteJobs = jobWasteList.OrderByDescending(o => o.TotalETickets).ToList(),

            AggregateETickets = aggregateEticketsList.OrderByDescending(o => o.TotalETickets).ToList(),
            AggregateJobs = jobProductList.OrderByDescending(o => o.TotalETickets).ToList(),

            TopProducts = aggregateEticketsProductsList.OrderByDescending(o => o.TotalETickets).ToList(),
            HaulierStops = haulierStopsList.OrderByDescending(o => o.TotalStops).ToList(),

            CustomersOnStop = await _db.Customers.Where(s => s.TenantId == _currentUserService.TenantId && s.OnStop).Select(s => s.Name).ToListAsync(),

            TotalWasteETickets = wasteEticketsList.Sum(x => x.TotalETickets) + jobs.Count(s => s.Type == (int)JobType.Waste),

            TotalAggregateETickets = aggregateEticketsList.Sum(x => x.TotalETickets) + jobs.Count(s => s.Type == (int)JobType.Product),

            TotalWasteETicketsAmount = wasteEticketsList.Sum(x => x.TotalAmount) + jobWasteList.Sum(x => x.TotalAmount),
            TotalAggregateETicketsAmount = aggregateEticketsList.Sum(x => x.TotalAmount) + jobProductList.Sum(x => x.TotalAmount),
            TotalHaulierStopsAmount = haulierStopsList.Sum(x => x.TotalAmount)
        };

        return dashboardSummaryModel;
    }

    private List<ETicketProductSummaryModel> GetETicketProducts(List<HT.Database.ETicket> eTickets)
    {
        var aggregateETicketProducts = eTickets
                                .Where(w => w.Type == (int)ETicketType.Product)
                                .GroupBy(g => g.ProductId);

        var aggregateEticketsProductsList = new List<ETicketProductSummaryModel>();

        foreach (var eticketProduct in aggregateETicketProducts)
        {
            aggregateEticketsProductsList.Add(new ETicketProductSummaryModel
            {
                ProductName = eticketProduct.First().Product.Name,
                TotalETickets = eticketProduct.Count(),
                TotalTonnes = eticketProduct.Sum(s => s.Tonnes)
            });
        }

        return aggregateEticketsProductsList;
    }

    private List<ETicketSummaryModel> GetAggregateETickets(List<HT.Database.ETicket> eTickets)
    {
        var aggregateETicketGroups = eTickets
                                .Where(w => w.Type == (int)ETicketType.Product)
                                .GroupBy(g => g.CustomerId);

        var aggregateEticketsList = new List<ETicketSummaryModel>();

        foreach (var eticketGroup in aggregateETicketGroups)
        {
            aggregateEticketsList.Add(new ETicketSummaryModel
            {
                CustomerName = eticketGroup.First().Customer.Name,
                TotalETickets = eticketGroup.Count(),
                TotalAmount = eticketGroup.Sum(s => s.TotalPrice),
                TotalWagons = eticketGroup.Select(s => s.HaulierVehicleId).Distinct().Count()
            });
        }

        return aggregateEticketsList;
    }

    private List<ETicketSummaryModel> GetAggregateJobs(List<Job> eTickets)
    {
        var aggregateETicketGroups = eTickets
                                .Where(w => w.Type == (int)ETicketType.Product)
                                .GroupBy(g => g.CustomerId);

        var aggregateEticketsList = new List<ETicketSummaryModel>();

        foreach (var eticketGroup in aggregateETicketGroups)
        {
            aggregateEticketsList.Add(new ETicketSummaryModel
            {
                CustomerName = eticketGroup.FirstOrDefault()?.Customer?.Name ?? "No Customer",
                TotalETickets = eticketGroup.Count(),
                TotalAmount = eticketGroup.SelectMany(s => s.Products).Sum(s => s.UnitTotal),
                //TotalWagons = eticketGroup.Select(s => s.HaulierVehicleId).Distinct().Count()
            });
        }

        return aggregateEticketsList;
    }

    private List<ETicketSummaryModel> GetWasteETickets(List<HT.Database.ETicket> eTickets)
    {
        var wasteETicketGroups = eTickets
                                .Where(w => w.Type == (int)ETicketType.Waste)
                                .GroupBy(g => g.CustomerId);

        var wasteEticketsList = new List<ETicketSummaryModel>();

        foreach (var eticketGroup in wasteETicketGroups)
        {
            wasteEticketsList.Add(new ETicketSummaryModel
            {
                CustomerName = eticketGroup.First().Customer.Name,
                TotalETickets = eticketGroup.Count(),
                TotalAmount = eticketGroup.Sum(s => s.TotalPrice),
                TotalWagons = eticketGroup.Select(s => s.HaulierVehicleId).Distinct().Count()
            });
        }

        return wasteEticketsList;
    }

    private List<ETicketSummaryModel> GetWasteJobs(List<Job> jobs)
    {
        var wasteETicketGroups = jobs
                                .Where(w => w.Type == (int)JobType.Waste)
                                .GroupBy(g => g.CustomerId);

        var wasteEticketsList = new List<ETicketSummaryModel>();

        foreach (var eticketGroup in wasteETicketGroups)
        {
            wasteEticketsList.Add(new ETicketSummaryModel
            {
                CustomerName = eticketGroup.FirstOrDefault()?.Customer?.Name ?? "No Customer",
                TotalETickets = eticketGroup.Count(),
                TotalAmount = eticketGroup.SelectMany(s => s.Products).Sum(s => s.UnitTotal),
                //TotalWagons = eticketGroup.Select(s => s.HaulierVehicleId).Distinct().Count()
            });
        }

        return wasteEticketsList;
    }
}
