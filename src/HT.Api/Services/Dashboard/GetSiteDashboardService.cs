using HT.Api.Services.Common.ActivityLog;
using HT.Api.Services.Route;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Dashboard;
using HT.Shared.Models.ETicket;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace HT.Api.Services.Dashboard;

public class GetSiteDashboardService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly ActivityLogService _activityLogService;
    private readonly RouteService _routeService;
    private readonly ReportService _reportService;

    public GetSiteDashboardService(DatabaseContext db,
        CurrentUserService currentUserService,
        ActivityLogService activityLogService,
        RouteService routeService,
        ReportService reportService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _activityLogService = activityLogService;
        _routeService = routeService;
        _reportService = reportService;
    }

    public async Task<DashboardSiteModel> GetSiteDashboardAsync(DashboardFiltersModel dashboardFiltersModel)
    {
        var eTickets = await _db.ETickets.Where(s => s.TenantId == _currentUserService.TenantId &&
                                   s.EntryDateTime >= dashboardFiltersModel.StartDay &&
                                   s.EntryDateTime <= dashboardFiltersModel.EndDay &&
                                   s.SiteId == dashboardFiltersModel.SiteId &&
                                   s.Status != (int)ETicketStatus.Void
                             ).Select(s => new
                             {
                                 s.Type,
                                 s.Status,
                                 CustomerName = s.Customer.Name,
                                 ProductName = s.Contract.SitePermitProductLink.Product.Name,
                                 NonContractProductName = s.Product.Name,
                                 NonContractProductTonnes = s.Tonnes,
                                 s.Contract.SitePermitProductLink.Product.EWCCode,
                                 ContractName = s.Contract.Name,
                                 s.NetWeight,
                                 s.TotalPrice,
                                 PermitName = s.Contract.SitePermitProductLink.SitePermit.Name,
                                 s.HaulierVehicleId,
                             }).ToListAsync();

        int voidETickets = await _db.ETickets.CountAsync(s => s.TenantId == _currentUserService.TenantId &&
                                   s.EntryDateTime >= dashboardFiltersModel.StartDay &&
                                   s.EntryDateTime <= dashboardFiltersModel.EndDay &&
                                   s.SiteId == dashboardFiltersModel.SiteId &&
                                   s.Status == (int)ETicketStatus.Void
                             );

        var allJobs = await _db.Jobs
                            .Include(i => i.Customer)
                            .Include(i => i.Products).ThenInclude(p => p.Product)
                            .Include(i => i.InternalTransferFromLocation)
                            .Include(i => i.InternalTransferToLocation)
                            .Where(s => s.TenantId == _currentUserService.TenantId &&
                                s.EntryDateTime >= dashboardFiltersModel.StartDay &&
                                s.EntryDateTime <= dashboardFiltersModel.EndDay &&
                                s.Status != (int)JobStatus.Void &&
                                dashboardFiltersModel.SiteId == new Guid("c2a64fa9-29c2-4c49-9d4f-b3fa1ff656c7")
                            ).ToListAsync();

        var jobs = allJobs.Where(s => !s.IsInternalTransfer).ToList();
        var internalJobs = allJobs.Where(s => s.IsInternalTransfer)
                            .Select(s => new DashboardInternalWasteTransferModel 
                            {
                                ProductName = s.Products != null && s.Products.Any() ? s.Products.FirstOrDefault().Product.Name : string.Empty,
                                From = s.InternalTransferFromLocation != null ? s.InternalTransferFromLocation.Name : string.Empty,
                                To = s.InternalTransferToLocation != null ? s.InternalTransferToLocation.Name : string.Empty,
                                Date = s.EntryDateTime.Value.ToString("dd/MM/yyyy hh:mm"),
                                Tons = s.Products != null && s.Products.Any() ? s.Products.FirstOrDefault().NetWeight : 0,
                            })
                            .ToList();

        int voidJobs = await _db.Jobs
            .CountAsync(s => s.TenantId == _currentUserService.TenantId &&
                s.EntryDateTime >= dashboardFiltersModel.StartDay &&
                s.EntryDateTime <= dashboardFiltersModel.EndDay &&
                s.Status == (int)JobStatus.Void &&
                dashboardFiltersModel.SiteId == new Guid("c2a64fa9-29c2-4c49-9d4f-b3fa1ff656c7") &&
                !s.IsInternalTransfer
            );

        int totalWasteCount = eTickets.Count(w => w.Type == (int)ETicketType.Waste) + jobs.Count(s => s.Type == (int)JobType.Waste);
        int totalProductCount = eTickets.Count(w => w.Type == (int)ETicketType.Product) + jobs.Count(s => s.Type == (int)JobType.Product);

        decimal totalWasteGross = eTickets.Where(w => w.Type == (int)ETicketType.Waste).Sum(s => s.TotalPrice) + jobs.Where(s => s.Type == (int)JobType.Waste).Sum(s => s.Products.Sum(p => p.UnitTotal));
        decimal totaPorductGross = eTickets.Where(w => w.Type == (int)ETicketType.Product).Sum(s => s.TotalPrice) + jobs.Where(s => s.Type == (int)JobType.Product).Sum(s => s.Products.Sum(p => p.UnitTotal));

        var dashboardSiteModel = new DashboardSiteModel
        {
            ETicketsCount = eTickets.Count + jobs.Count,
            Total = totalWasteGross + totaPorductGross,

            SavedETickets = eTickets.Count(s => s.Status == (int)ETicketStatus.Saved) + jobs.Count(s => s.Status == (int)JobStatus.Saved),
            ClosedETickets = eTickets.Count(s => s.Status == (int)ETicketStatus.Closed) + jobs.Count(s => s.Status == (int)JobStatus.Closed),
            VoidETickets = voidETickets + voidJobs,
            InvoicedETickets = eTickets.Count(s => s.Status == (int)ETicketStatus.Invoiced) + jobs.Count(s => s.Status == (int)JobStatus.Invoiced),
            InternalWasteTransfers = internalJobs,

            CustomersOnStop = await _db.Customers.Where(s => s.TenantId == _currentUserService.TenantId && s.OnStop).Select(s => s.Name).ToListAsync(),

            TotalWasteCount = totalWasteCount,
            TotalProductCount = totalProductCount,
            TotalWasteGross = totalWasteGross,
            TotalProductGross = totaPorductGross,
        };


        // Customers
        foreach (var item in eTickets.GroupBy(g => g.CustomerName).ToList())
        {
            dashboardSiteModel.TopCustomers.Add(new DashboardCustomerItemModel
            {
                Name = item.Key,
                Count = item.Count(),
                Total = item.Sum(s => s.TotalPrice),
                Wagons = item.Select(s => s.HaulierVehicleId).Distinct().Count()
            });
        }
        foreach (var item in jobs.GroupBy(g => g.Customer?.Name ?? "No Customer").ToList())
        {
            var existingCustomer = dashboardSiteModel.TopCustomers.FirstOrDefault(c => c.Name == item.Key);
            if (existingCustomer != null)
            {
                existingCustomer.Count += item.Count();
                existingCustomer.Total += item.Sum(s => s.Products.Sum(p => p.UnitTotal));
            }
            else
            {
                dashboardSiteModel.TopCustomers.Add(new DashboardCustomerItemModel
                {
                    Name = item.Key,
                    Count = item.Count(),
                    Total = item.Sum(s => s.Products.Sum(p => p.UnitTotal)),
                    Wagons = 0 // Jobs do not have wagons
                });
            }
        }
        dashboardSiteModel.TopCustomers = dashboardSiteModel.TopCustomers.OrderByDescending(o => o.Count).ToList();

        // Products
        foreach (var item in eTickets.Where(w => !string.IsNullOrEmpty(w.ProductName)).GroupBy(g => g.ProductName).ToList())
        {
            dashboardSiteModel.TopProducts.Add(new DashboardCustomerItemModel
            {
                EWCCode = item.FirstOrDefault().EWCCode,
                Name = item.Key,
                Count = item.Count(),
                Tons = item.Sum(s => s.NetWeight) / 1000
            });
        }
        foreach (var item in jobs.Where(s => s.Type == (int)JobType.Waste).SelectMany(j => j.Products).Where(p => p.Product != null && !string.IsNullOrEmpty(p.Product.Name)).GroupBy(g => g.Product.Name).ToList())
        {
            var existingProduct = dashboardSiteModel.TopProducts.FirstOrDefault(p => p.Name == item.Key);
            if (existingProduct != null)
            {
                existingProduct.Count += item.Count();
                existingProduct.Tons += item.Sum(s => s.NetWeight) / 1000;
            }
            else
            {
                dashboardSiteModel.TopProducts.Add(new DashboardCustomerItemModel
                {
                    EWCCode = item.FirstOrDefault().Product.EWCCode,
                    Name = item.Key,
                    Count = item.Count(),
                    Tons = item.Sum(s => s.NetWeight) / 1000
                });
            }
        }
        dashboardSiteModel.TopProducts = dashboardSiteModel.TopProducts.OrderByDescending(o => o.Count).ToList();

        // Products Out
        foreach (var item in eTickets.Where(s => s.Type == (int)ETicketType.Product).GroupBy(g => g.NonContractProductName).ToList())
        {
            dashboardSiteModel.ProductsOut.Add(new DashboardCustomerItemModel
            {
                Name = item.Key,
                Count = item.Count(),
                Tons = item.Sum(s => s.NonContractProductTonnes)
            });
        }
        foreach (var item in jobs.Where(s => s.Type == (int)JobType.Product).SelectMany(j => j.Products).Where(p => p.Product != null && !string.IsNullOrEmpty(p.Product.Name)).GroupBy(g => g.Product.Name).ToList())
        {
            var existingProductOut = dashboardSiteModel.ProductsOut.FirstOrDefault(p => p.Name == item.Key);
            if (existingProductOut != null)
            {
                existingProductOut.Count += item.Count();
                existingProductOut.Tons += item.Sum(s => s.NetWeight) / 1000;
            }
            else
            {
                dashboardSiteModel.ProductsOut.Add(new DashboardCustomerItemModel
                {
                    Name = item.Key,
                    Count = item.Count(),
                    Tons = item.Sum(s => s.NetWeight) / 1000
                });
            }
        }
        dashboardSiteModel.ProductsOut = dashboardSiteModel.ProductsOut.OrderByDescending(o => o.Tons).ToList();

        // Waste In By Customer
        foreach (var group in eTickets.Where(w => w.Type == (int)ETicketType.Waste).GroupBy(g => g.CustomerName).ToList())
        {
            var productOutByCustomer = new DashboardProductOutByCustomerModel
            {
                CustomerName = group.Key,
            };

            foreach (var productGroup in group.GroupBy(g => g.NonContractProductName))
            {
                productOutByCustomer.Products.Add(new DashboardCustomerItemModel
                {
                    Name = productGroup.FirstOrDefault().ProductName,
                    Count = productGroup.Count(),
                    Tons = productGroup.Sum(s => s.NonContractProductTonnes),
                    Total = productGroup.Sum(s => s.TotalPrice),
                    EWCCode = productGroup.FirstOrDefault().EWCCode
                });
            }

            dashboardSiteModel.WasteInByCustomer.Add(productOutByCustomer);
        }
        foreach (var group in jobs.Where(s => s.Type == (int)JobType.Waste).GroupBy(g => g.Customer?.Name ?? "No Customer").ToList())
        {
            var productOutByCustomer = dashboardSiteModel.WasteInByCustomer.FirstOrDefault(p => p.CustomerName == group.Key);
            if (productOutByCustomer == null)
            {
                productOutByCustomer = new DashboardProductOutByCustomerModel
                {
                    CustomerName = group.Key,
                };
                dashboardSiteModel.WasteInByCustomer.Add(productOutByCustomer);
            }

            foreach (var productGroup in group.SelectMany(g => g.Products).GroupBy(g => g.Product.Name))
            {
                var existingProduct = productOutByCustomer.Products.FirstOrDefault(p => p.Name == productGroup.Key);
                if (existingProduct != null)
                {
                    existingProduct.Count += productGroup.Count();
                    existingProduct.Tons += productGroup.Sum(s => s.NetWeight) / 1000;
                    existingProduct.Total += productGroup.Sum(s => s.UnitTotal);
                }
                else
                {
                    productOutByCustomer.Products.Add(new DashboardCustomerItemModel
                    {
                        EWCCode = productGroup.FirstOrDefault().Product.EWCCode,
                        Name = productGroup.FirstOrDefault().Product.Name,
                        Count = productGroup.Count(),
                        Tons = productGroup.Sum(s => s.NetWeight) / 1000,
                        Total = productGroup.Sum(s => s.UnitTotal)
                    });
                }
            }
        }
        dashboardSiteModel.WasteInByCustomer = dashboardSiteModel.WasteInByCustomer.OrderByDescending(o => o.Products.Sum(s => s.Count)).ToList();

        // Products Out By Customer
        foreach (var group in eTickets.Where(s => s.Type == (int)ETicketType.Product).GroupBy(g => g.CustomerName).ToList())
        {
            var productOutByCustomer = new DashboardProductOutByCustomerModel
            {
                CustomerName = group.Key,
            };

            foreach (var productGroup in group.GroupBy(g => g.NonContractProductName))
            {
                productOutByCustomer.Products.Add(new DashboardCustomerItemModel
                {
                    Name = productGroup.Key,
                    Count = productGroup.Count(),
                    Tons = productGroup.Sum(s => s.NonContractProductTonnes),
                    Total = productGroup.Sum(s => s.TotalPrice),
                    EWCCode = productGroup.FirstOrDefault().EWCCode
                });
            }

            dashboardSiteModel.ProductsOutByCustomer.Add(productOutByCustomer);
        }
        foreach (var group in jobs.Where(s => s.Type == (int)JobType.Product).GroupBy(g => g.Customer?.Name ?? "No Customer").ToList())
        {
            var productOutByCustomer = dashboardSiteModel.ProductsOutByCustomer.FirstOrDefault(p => p.CustomerName == group.Key);
            if (productOutByCustomer == null)
            {
                productOutByCustomer = new DashboardProductOutByCustomerModel
                {
                    CustomerName = group.Key,
                };
                dashboardSiteModel.ProductsOutByCustomer.Add(productOutByCustomer);
            }

            foreach (var productGroup in group.SelectMany(g => g.Products).GroupBy(g => g.Product.Name))
            {
                var existingProduct = productOutByCustomer.Products.FirstOrDefault(p => p.Name == productGroup.Key);
                if (existingProduct != null)
                {
                    existingProduct.Count += productGroup.Count();
                    existingProduct.Tons += productGroup.Sum(s => s.NetWeight) / 1000;
                    existingProduct.Total += productGroup.Sum(s => s.UnitTotal);
                }
                else
                {
                    productOutByCustomer.Products.Add(new DashboardCustomerItemModel
                    {
                        Name = productGroup.Key,
                        Count = productGroup.Count(),
                        Tons = productGroup.Sum(s => s.NetWeight) / 1000,
                        Total = productGroup.Sum(s => s.UnitTotal)
                    });
                }
            }
        }
        dashboardSiteModel.ProductsOutByCustomer = dashboardSiteModel.ProductsOutByCustomer.OrderByDescending(o => o.Products.Sum(s => s.Count)).ToList();

        // Contracts
        foreach (var item in eTickets.GroupBy(g => g.ContractName).ToList())
        {
            dashboardSiteModel.TopContracts.Add(new DashboardCustomerItemModel
            {
                Name = item.Key,
                Count = item.Count(),
                Tons = item.Sum(s => s.NetWeight) / 1000,
                Total = item.Sum(s => s.TotalPrice),
                Wagons = item.Select(s => s.HaulierVehicleId).Distinct().Count()
            });
        }
        dashboardSiteModel.TopContracts = dashboardSiteModel.TopContracts.OrderByDescending(o => o.Count).ToList();

        // Permits
        foreach (var item in eTickets.GroupBy(g => g.PermitName).Distinct().ToList())
        {
            if(item.Key is not null)
            {
                var prodcts = eTickets.Where(w => w.PermitName == item.Key).GroupBy(g => new { g.PermitName, g.EWCCode }).SelectMany(s => s).ToList();

                dashboardSiteModel.Permits.Add(new DashboardPermitModel
                {
                    Products = prodcts.GroupBy(g => new { g.EWCCode }).Select(s => new DashboardPermitItemModel
                    {
                        EWCCode = s.FirstOrDefault().EWCCode,
                        Name = s.FirstOrDefault().ProductName,
                        Tons = s.Sum(s => s.NetWeight) / 1000
                    }).OrderBy(o => o.Name).ToList(),
                    Name = item.Key,
                });
            }
        }

        // Contracts Near Limit
        dashboardSiteModel.ContractsNraringLimit = await _db.Contracts
            .Where(s => s.TenantId == _currentUserService.TenantId)
            .Where(s => s.SitePermit.SiteId == dashboardFiltersModel.SiteId)
            .Where(s => s.Status == (int)GenericStatus.Active)
            .Where(s => s.RemainingAllowance < 200)
        .OrderBy(o => o.RemainingAllowance)
        .Select(s => new DashboardCustomerItemModel
        {
            Name = s.Name,
            Tons = s.RemainingAllowance
        }).ToListAsync();

        await _activityLogService.Log(tenantId: _currentUserService.TenantId,
                          userId: _currentUserService.UserId,
                          activityLogType: ActivityLogType.DashboardSite,
                          message: JsonSerializer.Serialize(dashboardFiltersModel),
                          primaryObjectId: null);

        return dashboardSiteModel;
    }
}
