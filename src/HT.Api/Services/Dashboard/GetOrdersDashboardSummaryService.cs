using HT.Api.Services.Route;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using HT.Shared.Models.Dashboard;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Dashboard;

public class GetOrdersDashboardSummaryService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly RouteService _routeService;
    private readonly ReportService _reportService;

    public GetOrdersDashboardSummaryService(DatabaseContext db, CurrentUserService currentUserService, RouteService routeService, ReportService reportService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _routeService = routeService;
        _reportService = reportService;
    }

    public async Task<DashboardOrdersSummaryModel> GetOrdersDashboardSummaryAsync(DashboardFiltersModel dashboardFiltersModel)
    {
        var ordersQuery = _db.Orders.Where(s => s.TenantId == _currentUserService.TenantId &&
                                               s.OrderDate >= dashboardFiltersModel.StartDay &&
                                               s.OrderDate <= dashboardFiltersModel.EndDay //&&
                                                                                           //s.Status != (int)ETicketStatus.Void
                                         ).AsQueryable();

        var routesQuery = _db.Routes.Where(s => s.TenantId == _currentUserService.TenantId &&
                                   s.RouteDate >= dashboardFiltersModel.StartDay &&
                                   s.RouteDate <= dashboardFiltersModel.EndDay
                             ).AsQueryable();

        var customersQuery = _db.Customers
                   .Where(w => w.TenantId == _currentUserService.TenantId)
                   .Select(s => new CustomerListItemModel
                   {
                       Name = s.Name,
                       RouteArea = s.RouteArea.Name,
                       OrdersTotal = s.Orders.Where(o => o.Status != (int)OrderStatus.Cancelled).Sum(s => s.Total),
                       PaymentsTotal = s.CustomerPayments.Sum(sum => sum.Total),
                       LastPaymentDate = s.CustomerPayments.Any() ? s.CustomerPayments.OrderByDescending(o => o.Date).First().Date : null,
                   })
                   .AsQueryable();

        var eTickets = await ordersQuery.ToListAsync();
        var routes = await routesQuery.ToListAsync();
        var todaysRoutes = await _routeService.ListTodaysRoutesAsync();
        var customers = await customersQuery.ToListAsync();

        decimal totalOwedToSuppliers = await _db.Purchases
                                    .Where(w => w.TenantId == _currentUserService.TenantId)
                                    .Where(w => w.Status == (int)GenericStatus.Active)
                                    .Where(w => !w.Paid)
                                    .SumAsync(s => s.Total);

        var reportFilter = new ReportFiltersModel { StartDay = dashboardFiltersModel.StartDay, EndDay = dashboardFiltersModel.EndDay };
        var totalTonnage = await _reportService.ListTotalTonnageAsync(reportFilter);

        var dashboardSummaryModel = new DashboardOrdersSummaryModel
        {
            OrdersTotal = eTickets.Sum(s => s.Total),
            OrdersCount = eTickets.Count,
            RoutesCount = routes.Count,
            TotalOwedToSuppliers = totalOwedToSuppliers,
            TodaysRoutes = todaysRoutes,
            TotalOwedFromCustomers = customers.Sum(s => s.AccountBalance),
            HighestBalances = customers
                                .Where(w => w.AccountBalance > 0)
                                .OrderByDescending(o => o.AccountBalance)
                                .Take(20).ToList(),
            TotalTonnageModel = totalTonnage
        };

        return dashboardSummaryModel;
    }
}
