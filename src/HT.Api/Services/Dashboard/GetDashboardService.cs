using HT.Api.Services.Common.ActivityLog;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Dashboard;
using HT.Shared.Models.User;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Dashboard;

public class GetDashboardService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly ActivityLogService _activityLogService;

    public GetDashboardService(DatabaseContext db, CurrentUserService currentUserService, ActivityLogService activityLogService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _activityLogService = activityLogService;
    }

    public async Task<DashboardModel> GetDashboardAsync()
    {
        var userSecurity = await _currentUserService.GetUserSecurityAsync();

        var sitesQuery = _db.Sites
                       .Where(s => s.TenantId == _currentUserService.TenantId)
                       .Select(s => new DashboardSiteItemListModel
                       {
                           Id = s.Id,
                           Name = s.Name,
                       })
                       .OrderBy(o => o.Name)
                       .AsQueryable();

        if (userSecurity.UserType is not ((int)UserType.SuperUser) and not ((int)UserType.Admin))
        {
            sitesQuery = sitesQuery.Where(s => userSecurity.Sites.Contains(s.Id));
        }

        var dashboardModel = new DashboardModel
        {
            Sites = await sitesQuery.ToListAsync(),
        };

        await _activityLogService.Log(tenantId: _currentUserService.TenantId,
              userId: _currentUserService.UserId,
              activityLogType: ActivityLogType.DashboardLoad,
              message: null,
              primaryObjectId: null);

        return dashboardModel;
    }
}
