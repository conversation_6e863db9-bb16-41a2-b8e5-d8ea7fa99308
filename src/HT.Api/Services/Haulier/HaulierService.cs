﻿using HT.Blazor.Controls.Grid.Helpers;
using HT.Blazor.Models.Field;
using HT.Blazor.Models.Form;
using HT.Blazor.Models.Grid;
using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models.Haulier;
using HT.Shared.Models.HaulierRoute;
using HT.Shared.Models.HaulierVehicle;
using HT.Shared.Models.User;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Haulier;

public class HaulierService(
    DatabaseContext db,
    CurrentUserService currentUserService)
{
    public async Task<HaulierCreateModel> CreateHaulierAsync(HaulierCreateModel HaulierCreateModel)
    {
        HaulierCreateModel.Id = Guid.NewGuid();

        db.Hauliers.Add(new Database.Haulier
        {
            Id = HaulierCreateModel.Id,
            TenantId = currentUserService.TenantId,
            Status = (int)GenericStatus.Active,
            Code = HaulierCreateModel.Code,
            Name = HaulierCreateModel.Name,
            CarrierLicence = HaulierCreateModel.CarrierLicence
        });
        await db.SaveChangesAsync();

        return HaulierCreateModel;
    }

    public async Task<HaulierModel> GetHaulierAsync(Guid id)
    {
        var HaulierModel = await db.Hauliers
                               .Where(i => i.Id == id)
                               .Select(s => new HaulierModel
                               {
                                   Id = s.Id,
                                   Status = s.Status,
                                   Code = s.Code,
                                   Name = s.Name,
                                   CarrierLicence = s.CarrierLicence,
                                   CarrierLicenceExpiryDate = s.CarrierLicenceExpiryDate
                               })
                               .FirstOrDefaultAsync();

        return HaulierModel;
    }

    public async Task<FormResponseModel<HaulierModel>> UpdateHaulierAsync(HaulierModel HaulierModel)
    {
        var Haulier = await db.Hauliers.FirstOrDefaultAsync(s => s.Id == HaulierModel.Id);

        Haulier.Status = HaulierModel.Status;
        Haulier.Code = HaulierModel.Code;
        Haulier.Name = HaulierModel.Name;
        Haulier.CarrierLicence = HaulierModel.CarrierLicence;

        if (HaulierModel.CarrierLicenceExpiryDate != null)
        {
            Haulier.CarrierLicenceExpiryDate = HaulierModel.CarrierLicenceExpiryDate.Value.DateTime;
        }

        await db.SaveChangesAsync();

        return new FormResponseModel<HaulierModel>(HaulierModel);
    }

    public async Task<GridResultModel<HaulierListItemModel>> ListHauliersAsync([FromBody] GridParametersModel gridParametersModel)
    {
        var q = db.Hauliers
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Select(s => new HaulierListItemModel
                   {
                       Id = s.Id,
                       Status = s.Status,
                       Code = s.Code,
                       Name = s.Name,
                       CarrierLicence = s.CarrierLicence,
                       HaulierVehicles = s.HaulierVehicles.Count,
                       CarrierLicenceExpiryDate = s.CarrierLicenceExpiryDate
                   })
                   .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    public async Task<List<DropdownListItem>> ListHauliersDropdownAsync()
    {
        var hauliers = await db.Hauliers
                .Where(w => w.TenantId == currentUserService.TenantId)
                .OrderBy(o => o.Name)
                .Select(s => new DropdownListItem
                {
                    Id = s.Id.ToString(),
                    Name = s.Name,
                })
                .ToListAsync();

        return hauliers;
    }

    public async Task<List<DropdownListItem>> ListHaulierVehicleDriverDropdownAsync()
    {
        var hauliers = await db.Users
            .Where(w => w.TenantId == currentUserService.TenantId)
            .Where(w => w.UserType == (int)UserType.Driver)
            .Where(w => w.HaulierVehicle != null)
            .OrderBy(o => o.FirstName)
            .Select(s => new DropdownListItem
            {
                Id = s.Id.ToString(),
                Name = $"{s.FirstName} {s.Surname} / {s.HaulierVehicle.Haulier.Name} / {s.HaulierVehicle.Registration}",
            })
            .ToListAsync();

        return hauliers;
    }

    public async Task<List<DropdownListItem>> ListHaulierVehicleDropdownAsync()
    {
        var hauliers = await db.HaulierVehicles
                   .Where(w => w.TenantId == currentUserService.TenantId)
                   .Where(w => w.DriverUserId != null)
                   .Select(s => new DropdownListItem
                   {
                       Id = s.Id.ToString(),
                       Name = $"{s.DriverUser.FirstName} {s.DriverUser.Surname} / {s.Haulier.Name} / {s.Registration}",
                   })
                   .ToListAsync();

        return hauliers;
    }

    public async Task<GridResultModel<HaulierListDriverModel>> GetHauliersGridAsync([FromBody] GridParametersModel gridParametersModel)
    {
        var q = db.Users
                .Where(w => w.TenantId == currentUserService.TenantId)
                .Where(w => w.UserType == (int)UserType.Driver)
                .Select(s => new HaulierListDriverModel
                {
                    DriverUserId = s.Id,
                    FirstName = s.FirstName,
                    Surname = s.Surname,
                    Registration = s.HaulierVehicle != null ? s.HaulierVehicle.Registration : string.Empty,
                    HaulierName = s.HaulierVehicle != null ? s.HaulierVehicle.Haulier.Name : string.Empty,
                    Active = s.Status == (int)GenericStatus.Active,
                })
                .AsQueryable();

        return await GridHelper.CreateGridAsync(q, gridParametersModel);
    }

    /// <summary>
    /// Gets the driver, haulier and vehicle details
    /// </summary>
    /// <param name="driverUserId"></param>
    /// <returns></returns>
    public async Task<HaulierDriverModel> GetDriverDetailsAsync(Guid driverUserId)
    {
        return await db.Users
                        .Where(w => w.Id == driverUserId)
                        .Select(s => new HaulierDriverModel
                        {
                            HaulierVehicleId = s.HaulierVehicle.Id,
                            HaulierVehicleRegistration = s.HaulierVehicle.Registration,
                            HaulierId = s.HaulierVehicle.HaulierId,
                            HaulierName = s.HaulierVehicle.Haulier.Name,
                            FirstName = s.FirstName,
                            LastName = s.Surname,
                            DriverUserId = s.Id,
                        })
                        .FirstAsync();
    }
}
