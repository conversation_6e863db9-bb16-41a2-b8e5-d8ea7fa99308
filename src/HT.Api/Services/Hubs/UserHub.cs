﻿using HT.Database;
using Microsoft.AspNetCore.SignalR;

namespace HT.Api.Services.Hubs;

public class UserHub(DatabaseContext db) : Hub
{
    public const string HubUrl = "/UserHub";

    public async Task ChangeUrl(Guid userId, string url)
    {
        db.UserLogs.Add(new UserLog
        {
            Id = Guid.NewGuid(),
            Type = 3,
            ConnectionId = Context.ConnectionId,
            DateTime = DateTime.Now,
            UserId = userId,
            Url = url
        });
        await db.SaveChangesAsync();
    }

    public override async Task OnConnectedAsync()
    {
        db.UserLogs.Add(new UserLog
        {
            Id = Guid.NewGuid(),
            Type = 1,
            ConnectionId = Context.ConnectionId,
            DateTime = DateTime.Now,
        });
        await db.SaveChangesAsync();

        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception exception)
    {
        db.UserLogs.Add(new UserLog
        {
            Id = Guid.NewGuid(),
            Type = 2,
            ConnectionId = Context.ConnectionId,
            DateTime = DateTime.Now,
        });
        await db.SaveChangesAsync();

        await base.OnDisconnectedAsync(exception);
    }
}
