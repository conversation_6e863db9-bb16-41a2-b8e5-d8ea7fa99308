﻿using HT.Database;
using HT.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using HT.Shared.Models.Order;
using HT.Api.Services.Route;
using HT.Blazor.Models.Form;
using HT.Shared.Constants;

namespace HT.Api.Services.Round;

public class RoundOrderService(
    DatabaseContext db,
    CurrentUserService currentUserService,
    ProductPriceService productPriceService,
    RouteService routeService,
    PortalEventService portalEventService)
{
    public async Task<FormResponseModel<Guid>> CreateRoundOrderAsync(RoundOrderCreateModel createModel)
    {
        var response = new FormResponseModel<Guid>();

        var customer = await db.Customers
                        .Include(i => i.CustomerAddress)
                        .Include(i => i.RouteArea)
                            .ThenInclude(t => t.RouteAreaLinks)
                        .FirstOrDefaultAsync(c => c.Id == createModel.CustomerId);

        int lastOrderNumber = await db.Orders
        .Where(w => w.TenantId == currentUserService.TenantId)
        .CountAsync();

        var order = new Database.Order
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            CustomerId = createModel.CustomerId,
            OrderDate = DateTime.Now,
            CreatedDateTime = DateTime.Now,
            Status = (int)OrderStatus.Open,
            DeliveryType = (int)OrderDeliveryType.Route,
            DeliveryStatus = (int)OrderDeliveryStatus.NotOnRoute,
            OrderNumber = lastOrderNumber + 1,
            RoundOrder = true,
        };

        db.Orders.Add(order);

        await portalEventService.LogAsync(PortalEventType.OrderCreated, order.Id);

        var deliveryAddress = new Database.Tables.Address
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            AddressLine1 = customer.CustomerAddress.AddressLine1,
            AddressLine2 = customer.CustomerAddress.AddressLine2,
            AddressLine3 = customer.CustomerAddress.AddressLine3,
            AddressLine4 = customer.CustomerAddress.AddressLine4,
            AddressPostcode = customer.CustomerAddress.AddressPostcode,
            Longitude = customer.CustomerAddress.Longitude,
            Latitude = customer.CustomerAddress.Latitude,
        };

        db.Addresses.Add(deliveryAddress);

        order.DeliveryAddressId = deliveryAddress.Id;

        if (createModel.LastOrderId != null)
        {
            var lastOrder = await db.Orders
                                .Include(i => i.OrderLines)
                                    .ThenInclude(o => o.Product)
                                .Include(i => i.DeliveryAddress)
                                .FirstOrDefaultAsync(o => o.Id == createModel.LastOrderId);

            foreach (var lastOrderLine in lastOrder.OrderLines.Where(l => l.ProductId != null && l.Product.Status == (int)GenericStatus.Active))
            {
                var price = await productPriceService.GetProductPriceAsync((Guid)lastOrderLine.ProductId, createModel.CustomerId);

                decimal unitTotal = price.Price * lastOrderLine.Quantity;
                decimal VATTotal = price.VATPercent != 0 ? lastOrderLine.UnitTotal * (price.VATPercent / 100) : 0;
                decimal total = unitTotal + VATTotal;

                db.OrderLines.Add(new OrderLine
                {
                    Id = Guid.NewGuid(),
                    TenantId = currentUserService.TenantId,
                    LineNumber = lastOrderLine.LineNumber,
                    OrderId = order.Id,
                    ProductId = lastOrderLine.ProductId,
                    Quantity = lastOrderLine.Quantity,
                    CustomUnitPrice = false,

                    UnitPrice = price.Price,
                    UnitTotal = unitTotal,
                    VATRate = price.VATPercent,
                    VATTotal = VATTotal,
                    Total = total,
                });
            }

            if (order.OrderLines != null)
            {
                order.UnitTotal = order.OrderLines.Sum(s => s.UnitTotal);
                order.VATTotal = order.OrderLines.Sum(s => s.VATTotal);
                order.Total = order.OrderLines.Sum(s => s.Total);
            }
            else
            {
                order.UnitTotal = 0;
                order.VATTotal = 0;
                order.Total = 0;
            }
        }

        if (createModel.NextDeliveryDate != null)
        {
            var route = await db.Routes
                    .Where(l => l.TenantId == currentUserService.TenantId)
                    .Where(l => l.RouteAreaId == createModel.RouteAreaId)
                    .Where(l => l.RouteDate == createModel.NextDeliveryDate)
                    .FirstOrDefaultAsync();

            // See if we can find a linked route
            if (route == null && customer.RouteArea.RouteAreaLinks != null)
            {
                foreach (var relatedRoute in customer.RouteArea.RouteAreaLinks)
                {
                    route = await db.Routes
                            .Where(l => l.TenantId == currentUserService.TenantId)
                            .Where(l => l.RouteAreaId == relatedRoute.LinkedRouteAreaId)
                            .Where(l => l.RouteDate == createModel.NextDeliveryDate)
                            .FirstOrDefaultAsync();

                    if (route != null)
                    {
                        break;
                    }
                }
            }

            if (route == null)
            {
                if (createModel.RouteMustExist)
                {
                    response.AddError($"Could not create orders for {customer.RouteArea.Name} route. The route does not exist for {((DateTime)createModel.NextDeliveryDate).ToShortDateString()}.");
                }
            }
            else
            {
                if (route.Status == (int)RouteStatus.Completed)
                {
                    response.AddError($"Could not create orders for {customer.RouteArea.Name} route. The route has been completed.");
                }
                else
                {
                    // Delete any visits on this route

                    var visit = await db.Visits
                                    .Where(w => w.TenantId == currentUserService.TenantId)
                                    .Where(w => w.RouteId == route.Id)
                                    .Where(w => w.VisitDate == createModel.NextDeliveryDate)
                                    .Where(w => w.CustomerId == createModel.CustomerId)
                                    .FirstOrDefaultAsync();

                    if (visit != null)
                    {
                        await routeService.DeleteVisitOnRouteAsync(route.Id, visit.Id);
                    }

                    await db.SaveChangesAsync();
                    await routeService.AddOrderToRouteAsync(route.Id, order.Id);
                }
            }
        }

        if (response.Success)
        {
            await db.SaveChangesAsync();
            response.Data = order.Id;
        }

        return response;
    }
}
