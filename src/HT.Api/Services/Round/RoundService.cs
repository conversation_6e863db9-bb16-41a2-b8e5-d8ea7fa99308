﻿using HT.Database;
using HT.Shared.Enums;
using HT.Shared.Models;
using Microsoft.EntityFrameworkCore;
using HT.Shared.Models.Order;
using HT.Api.Services.Route;
using HT.Blazor.Models.Form;
using HT.Api.Services.Order;
using HT.Shared.Models.Route;

namespace HT.Api.Services.Round;

public class RoundService
{
    private readonly DatabaseContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly RouteService _routeService;
    private readonly OrderService _orderService;

    private List<DateIdItemModel> RouteItems = [];

    public RoundService(
        DatabaseContext db,
        CurrentUserService currentUserService,
        RouteService routeService,
        OrderService orderervice)
    {
        _db = db;
        _currentUserService = currentUserService;
        _routeService = routeService;
        _orderService = orderervice;
    }

    public async Task<List<CustomerRoundListItemModel>> ListRoundCustomersAsync(CustomerRoundListGetModel model)
    {
        // Get the routes

        this.RouteItems = await this._db.Routes
                            .Where(w => w.TenantId == this._currentUserService.TenantId)
                            .Where(w => w.RouteDate >= DateTime.Now.Date)
                            .Select(s => new DateIdItemModel { Date = s.RouteDate, Id = (Guid)s.RouteAreaId })
                            .ToListAsync();

        // Get the round customer data
        var rounds = await _db.Customers
            .AsNoTracking()
            .Include(i => i.Orders)
                .ThenInclude(i => i.OrderLines)
                    .ThenInclude(i => i.Product)
            .Include(i => i.Orders)
                .ThenInclude(i => i.Route)
            .Where(w => w.RouteAreaId != null)
            .Where(w => w.TenantId == _currentUserService.TenantId)
            .Where(w => w.Status == (int)GenericStatus.Active)
            .Where(w => w.CustomerTagLinks.Any(a => a.CustomerTag.Name == "Rounds"))
            .Where(w => model.RouteAreaId != null && w.RouteAreaId == model.RouteAreaId
                        || !string.IsNullOrWhiteSpace(model.NameSearch) && w.Name.ToLower().Contains(model.NameSearch.ToLower())
                  )
            .Where(w => !model.OnStopOnly || w.OnStop)
            .Select(s => new CustomerRoundCustomerModel
            {
                CustomerId = s.Id,
                CustomerName = s.Name,
                OnStop = s.OnStop,
                RouteArea = s.RouteArea.Name,
                RouteAreaId = (Guid)s.RouteAreaId,
                RoundOrdersStartDate = s.FirstRoundOrderDate,
                RoundNotes = s.RoundNotes != null ? s.RoundNotes : string.Empty,
                LastOrder = s.Orders
                                .Where(o => o.Status != (int)OrderStatus.Cancelled)
                                .Where(o => o.RouteId != null)
                                .Where(o => o.Route.RouteDate <= DateTime.Now)
                                .OrderByDescending(o => o.Route.RouteDate)
                                .FirstOrDefault(),
                LastRoundOrder = s.Orders
                                .Where(o => o.Status != (int)OrderStatus.Cancelled)
                                .Where(o => o.RouteId != null)
                                .Where(o => o.RoundOrder)
                                .Where(o => o.Route.RouteDate <= DateTime.Now)
                                .OrderByDescending(o => o.Route.RouteDate)
                                .FirstOrDefault(),
                PreferredDeliveryDay = s.PreferredDeliveryDay,
                OrderFrequencyDays = s.OrderFrequencyDays,
                CustomerTags = s.CustomerTagLinks
                                .Select(t => t.CustomerTag.Name)
                                .OrderBy(o => o)
                                .ToList(),

                FutureOrders = s.Orders.Where(o => o.Status != (int)OrderStatus.Cancelled)
                                        .Where(w => w.Route != null)
                                        .Where(w => w.Route.RouteDate >= DateTime.Now.Date)
                                        .Select(s => new DateIdItemModel { Date = s.Route.RouteDate, Id = s.Id })
                                        .ToList(),
                FutureVisits = s.Visits.Where(w => w.VisitDate >= DateTime.Now.Date)
                                       .Select(s => new DateIdItemModel { Date = s.Route.RouteDate, Id = s.Id })
                                       .ToList(),
            })
            .ToListAsync();

        // Get the orders
        var result = GetRoundOrders(rounds);

        return result;
    }

    public async Task<bool> ToggleOnStopAsync(Guid customerId)
    {
        var customer = await this._db.Customers
                            .Where(w => w.TenantId == this._currentUserService.TenantId)
                            .Where(w => w.Id == customerId)
                            .FirstOrDefaultAsync();

        customer.OnStop = !customer.OnStop;

        await this._db.SaveChangesAsync();

        return customer.OnStop;
    }

    public async Task<FormResponseModel<Guid>> CreateMissedRoundOrderAsync(MissOrderCreateModel createModel)
    {
        var response = new FormResponseModel<Guid>();

        Database.Route route = null;
        Database.RouteArea routeArea = null;

        if (createModel.RouteId != null)
        {
            route = await _db.Routes
                        .Where(l => l.TenantId == this._currentUserService.TenantId)
                        .Where(l => l.Id == createModel.RouteId)
                        .FirstOrDefaultAsync();

            routeArea = await _db.RouteAreas
                            .Include(i => i.RouteAreaLinks)
                            .Where(l => l.TenantId == this._currentUserService.TenantId)
                            .Where(l => l.Id == route.RouteAreaId)
                            .FirstOrDefaultAsync();
        }
        else
        {
            routeArea = await _db.RouteAreas
                            .Include(i => i.RouteAreaLinks)
                            .Where(l => l.TenantId == this._currentUserService.TenantId)
                            .Where(l => l.Id == createModel.RouteAreaId)
                            .FirstOrDefaultAsync();

            route = await _db.Routes
                        .Where(l => l.TenantId == this._currentUserService.TenantId)
                        .Where(l => l.RouteAreaId == createModel.RouteAreaId)
                        .Where(l => l.RouteDate == createModel.DeliveryDate)
                        .FirstOrDefaultAsync();

        }

        // See if we can find a linked route
        if (route == null && routeArea.RouteAreaLinks != null)
        {
            foreach (var relatedRoute in routeArea.RouteAreaLinks)
            {
                route = await this._db.Routes
                        .Where(l => l.TenantId == this._currentUserService.TenantId)
                        .Where(l => l.RouteAreaId == relatedRoute.LinkedRouteAreaId)
                        .Where(l => l.RouteDate == createModel.DeliveryDate)
                        .FirstOrDefaultAsync();

                if (route != null)
                {
                    break;
                }
            }
        }

        if (route == null)
        {
            response.AddError($"Could not create missed order for {routeArea.Name} route. The route does not exist for {createModel.DeliveryDate.ToShortDateString()}.");
        }
        else
        {
            // delete the order for that exists
            if (createModel.OrderId != null)
            {
                var order = await this._db.Orders.FirstOrDefaultAsync(o => o.Id == createModel.OrderId);
                if (order != null)
                {
                    await this._orderService.DeleteOrderAsync((Guid)createModel.OrderId);
                }
            }

            // Create the visit
            var visit = new Visit
            {
                Id = Guid.NewGuid(),
                TenantId = this._currentUserService.TenantId,
                CustomerId = createModel.CustomerId,
                VisitDate = route.RouteDate,
                CreatedDateTime = DateTime.Now,
                OrderDeliveryNotes = "Miss - No order",
            };

            this._db.Visits.Add(visit);

            await this._db.SaveChangesAsync();

            await this._routeService.AddVisitToRouteAsync(route.Id, visit.Id);

            response.Data = visit.Id;
        }

        return response;
    }

    private List<CustomerRoundListItemModel> GetRoundOrders(List<CustomerRoundCustomerModel> rounds)
    {
        var roundOrders = rounds.Select(s => new CustomerRoundListItemModel
        {
            OrderId = s.LastRoundOrder != null ? s.LastRoundOrder.Id : s.LastOrder?.Id,
            CustomerName = s.CustomerName,
            CustomerId = s.CustomerId,
            HasOrders = s.LastOrder != null || s.LastRoundOrder != null,
            OnStop = s.OnStop,
            RoundNotes = s.RoundNotes,
            RouteArea = s.RouteArea,
            RouteAreaId = s.RouteAreaId,
            RouteId = s.LastRoundOrder != null ? s.LastRoundOrder.RouteId : s.LastOrder?.RouteId,
            IsRoundOrder = s.LastRoundOrder != null,
            OrderDate = s.LastRoundOrder != null ? s.LastRoundOrder.OrderDate : s.LastOrder?.OrderDate,
            Products = GetProducts(s.LastRoundOrder, s.LastOrder),
            LastScheduledDeliveryDate = GetLastScheduledDeliveryDate(s.LastRoundOrder, s.LastOrder),
            PreferredDeliveryDay = s.PreferredDeliveryDay,
            OrderFrequencyDays = s.OrderFrequencyDays,
            CustomerTags = s.CustomerTags,
            FutureOrders = s.FutureOrders,
            FutureVisits = s.FutureVisits,
            RoundOrdersStartDate = s.RoundOrdersStartDate,
        })
        .OrderBy(o => o.PreferredDeliveryDay)
        .ThenByDescending(o => o.LastScheduledDeliveryDate != null)
        .ToList();

        foreach (var roundOrder in roundOrders.Where(o => o.OrderFrequencyDays != 0))
        {
            // Get the date to start adding the rounds to.

            var dateToAdd = roundOrder.RoundOrdersStartDate;

            if (dateToAdd == null)
            {
                continue;
            }

            ProcessRoundOrder(roundOrder, dateToAdd);
        }

        return roundOrders;
    }

    private void ProcessRoundOrder(CustomerRoundListItemModel roundOrder, DateTime? dateToAdd)
    {
        int dateCount = 1;

        var routeDates = new List<DateTime>();

        // Loop and add the frequency to the dateToAdd until it is the nearest past date to today.

        while (dateToAdd <= DateTime.Now.Date)
        {
            dateToAdd = dateToAdd.Value.AddDays(roundOrder.OrderFrequencyDays);
        }

        // Get the next five round dates.

        for (int i = 0; i < 5; i++)
        {
            routeDates.Add(dateToAdd.Value.Date);
            dateToAdd = dateToAdd.Value.AddDays(roundOrder.OrderFrequencyDays);
        }

        foreach (var routeDate in routeDates)
        {
            var order = roundOrder.FutureOrders.FirstOrDefault(o => o.Date == routeDate);
            var visit = roundOrder.FutureVisits.FirstOrDefault(o => o.Date == routeDate);

            var futureRoundItemModel = new RouteFutureRoundItemModel
            {
                Date = routeDate,
                CustomerId = roundOrder.CustomerId,
                RouteOrderId = roundOrder.OrderId,
                FutureOrderId = order?.Id,
                FutureVisitId = visit?.Id,
                LastOrderId = roundOrder.OrderId,

                AdditionalOrders = roundOrder.FutureOrders
                                    .Where(o => o.Date > routeDate)
                                    .Where(o => o.Date < routeDate.AddDays(roundOrder.OrderFrequencyDays))
                                    .Select(s => new AdditionalOrderModel { Date = s.Date, OrderId = s.Id })
                                    .ToList(),
            };

            if (futureRoundItemModel.AdditionalOrders.Any() && futureRoundItemModel.FutureOrderId != null)
            {
                futureRoundItemModel.AdditionalOrders.Insert(0, new AdditionalOrderModel
                {
                    OrderId = futureRoundItemModel.FutureOrderId.Value,
                    Date = routeDate
                });
            }

            roundOrder.FutureRounds.Add(futureRoundItemModel);

            if (dateCount == 1 && (order != null || visit != null))
            {
                roundOrder.NextDeliveryIsBooked = true;
            }

            dateCount++;
        }
    }

    private static DateTime? GetLastScheduledDeliveryDate(Database.Order lastRoundOrder, Database.Order lastOrder)
    {
        if (lastRoundOrder?.Route != null)
        {
            return lastRoundOrder.Route.RouteDate;
        }

        if (lastOrder?.RouteId != null)
        {
            return lastOrder.Route.RouteDate;
        }

        return null;
    }

    private static List<string> GetProducts(Database.Order lastRoundOrder, Database.Order lastOrder)
    {
        var orderLines = lastRoundOrder?.OrderLines ?? lastOrder?.OrderLines;

        return orderLines?.Where(w => w.Product != null)
                          .Select(ol => $"{ol.Product.Name} x{ol.Quantity} £{ol.UnitTotal}")
                          .ToList() ?? [];
    }

    public async Task<FormResponseModel<bool>> UpdateRoundsStartDateAsync(CustomerRoundListItemModel model)
    {
        var response = new FormResponseModel<bool>();

        var customer = await this._db.Customers
                            .Where(w => w.TenantId == this._currentUserService.TenantId)
                            .Where(w => w.Id == model.CustomerId)
                            .FirstOrDefaultAsync();

        if (model.RoundOrdersStartDate != null)
        {
            customer.FirstRoundOrderDate = model.RoundOrdersStartDate;

            await this._db.SaveChangesAsync();

            response.Data = true;
        }
        else
        {
            response.AddError("The round start date cannot be empty.");
        }

        return response;
    }

    public async Task<FormResponseModel<bool>> UpdateCustomerRoundNotesAsync(CustomerRoundListItemModel model)
    {
        var response = new FormResponseModel<bool>();

        var customer = await this._db.Customers
                            .Where(w => w.TenantId == this._currentUserService.TenantId)
                            .Where(w => w.Id == model.CustomerId)
                            .FirstOrDefaultAsync();

        customer.RoundNotes = model.RoundNotes;

        await this._db.SaveChangesAsync();

        response.Data = true;

        return response;
    }
}
