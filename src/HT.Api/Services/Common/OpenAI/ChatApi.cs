﻿using OpenAI_API.Chat;

namespace HT.Api.Services.Common.OpenAI;

public class ChatApi
{
    private readonly OpenAI_API.OpenAIAPI _api;
    private OpenAI_API.Chat.Conversation _chat;

    public ChatApi()
    {
        _api = new OpenAI_API.OpenAIAPI("***************************************************");
        _chat = _api.Chat.CreateConversation(new ChatRequest
        {
            Model = OpenAI_API.Models.Model.GPT4
        });
    }

    public async Task<string> GetResponseFromChatbotAsync(string userInput)
    {
        _chat.AppendUserInput(userInput);
        return await _chat.GetResponseFromChatbotAsync();
    }

    public void CreateConversation()
    {
        _chat = _api.Chat.CreateConversation();
    }

    public void AppendSystemMessage(string message)
    {
        _chat.AppendSystemMessage(message);
    }

    public async Task<string> GetImageResponseFromChatbotAsync(string userInput)
    {
        _ = await _api.ImageGenerations.CreateImageAsync(userInput);
        return "";
    }

}
