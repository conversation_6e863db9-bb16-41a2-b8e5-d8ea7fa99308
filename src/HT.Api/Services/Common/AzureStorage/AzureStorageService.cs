﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;

namespace HT.Api.Services.Common.AzureStorage;

public class AzureStorageService
{
    private readonly BlobServiceClient _blobServiceClient;

    public AzureStorageService()
    {
        // Hardcoded connection string
        const string connectionString = "DefaultEndpointsProtocol=https;AccountName=stheavydutycdn;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
        _blobServiceClient = new BlobServiceClient(connectionString);
    }

    public async Task<string> UploadFileToBlobAsync(string containerName, string blobName, Stream fileStream, string contentType = null)
    {
        if (fileStream == null)
        {
            throw new ArgumentNullException(nameof(fileStream), "File stream cannot be null.");
        }

        var blobContainerClient = _blobServiceClient.GetBlobContainerClient(containerName);
        await blobContainerClient.CreateIfNotExistsAsync();

        var blobClient = blobContainerClient.GetBlobClient(blobName);
        await blobClient.UploadAsync(fileStream, true);

        if (!string.IsNullOrEmpty(contentType))
        {
            await blobClient.SetHttpHeadersAsync(new BlobHttpHeaders { ContentType = contentType });
        }

        return blobClient.Uri.ToString();
    }

    public async Task<bool> DeleteBlobAsync(string containerName, string blobName)
    {
        var blobContainerClient = _blobServiceClient.GetBlobContainerClient(containerName);
        var blobClient = blobContainerClient.GetBlobClient(blobName);

        var response = await blobClient.DeleteIfExistsAsync();

        return response.Value;
    }
}
