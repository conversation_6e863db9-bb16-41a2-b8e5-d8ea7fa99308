﻿using HT.Shared.Models.Google.Maps;
using Newtonsoft.Json;

public class GoogleMapsAutocompleteService
{
    private readonly string _apiKey = "AIzaSyCz8-Hg-y1_Xg6WPvJvwYBAwjF3NHC713k";

    public GoogleMapsAutocompleteService()
    {
    }

    public async Task<GoogleAutocompleteResponse> GetGeocodeLocationAsync(string input)
    {
        using (var httpClient = new HttpClient())
        {
            string baseUrl = "https://maps.googleapis.com/maps/api/place/autocomplete/json";
            string url = $"{baseUrl}?input={Uri.EscapeDataString(input)}&key={_apiKey}";


            var response = await httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                string jsonResult = await response.Content.ReadAsStringAsync();
                var geocodeResponse = JsonConvert.DeserializeObject<GoogleAutocompleteResponse>(jsonResult);
                return geocodeResponse;
            }
        }

        return new GoogleAutocompleteResponse { };
    }
}
