﻿using HT.Shared.Models.Google.Maps;
using Newtonsoft.Json;

namespace HT.Api.Services.Common.Google.Maps;

public class GoogleRoutesService
{
    private const string ApiKey = "AIzaSyCz8-Hg-y1_Xg6WPvJvwYBAwjF3NHC713k";

    // lat - long
    public async Task<List<int>> GetOptimisedRoute(string startGeocode, string endGeocode, List<string> stopGeocodes)
    {
        try
        {
            using var httpClient = new HttpClient();
            string waypoints = string.Join("|", stopGeocodes);

            string requestUrl = $"https://maps.googleapis.com/maps/api/directions/json?origin={startGeocode}&destination={endGeocode}&waypoints=optimize:true|{waypoints}&key={ApiKey}";

            var response = await httpClient.GetAsync(requestUrl);
            response.EnsureSuccessStatusCode();
            string responseBody = await response.Content.ReadAsStringAsync();

            var routeData = JsonConvert.DeserializeObject<GoogleMapsDirectionsResponse>(responseBody);

            if (routeData.status == "OK")
            {
                return routeData.routes.FirstOrDefault().waypoint_order;
            }
            else
            {
                return null;
            }
        }
        catch (HttpRequestException)
        {
            return null;
        }
    }




}
