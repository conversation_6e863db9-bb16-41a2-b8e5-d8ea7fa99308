﻿using HT.Shared.Models.Google.Maps;
using Newtonsoft.Json;

namespace HT.Api.Services.Common.Google.Maps;

public class GoogleGeocodeService
{
    private const string _apiKey = "AIzaSyCz8-Hg-y1_Xg6WPvJvwYBAwjF3NHC713k";

    public GoogleGeocodeService()
    {
    }

    public async Task<GeocodeLocation> GetGeocodeLocationAsync(string address)
    {
        using var httpClient = new HttpClient();
        string geocodeUrl = $"https://maps.googleapis.com/maps/api/geocode/json?address={Uri.EscapeDataString(address)}&key={_apiKey}";

        var response = await httpClient.GetAsync(geocodeUrl);
        if (!response.IsSuccessStatusCode)
        {
            return null;
        }

        string jsonResult = await response.Content.ReadAsStringAsync();
        var geocodeResponse = JsonConvert.DeserializeObject<GeocodeResponse>(jsonResult);

        if (geocodeResponse.Status != "OK" || geocodeResponse.Results.Length <= 0)
        {
            return null;
        }

        var location = geocodeResponse.Results[0].Geometry.Location;
        return location;

    }
}
