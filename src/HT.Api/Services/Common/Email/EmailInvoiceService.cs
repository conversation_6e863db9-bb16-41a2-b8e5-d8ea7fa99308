﻿using HT.Database;
using HT.Shared.Common;
using HT.Shared.Enums;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Common.Email;

public class EmailInvoiceService(
    DatabaseContext databaseContext,
    CurrentUserService currentUserService,
    EmailService emailService)
{
    public async Task SendInvoiceEmail(Guid invoiceId)
    {
        var invoiceData = await databaseContext.Invoices.Where(w => w.Id == invoiceId)
                                            .Select(s => new
                                            {
                                                s.TenantId,
                                                s.InvoiceCompanyNumber,
                                                DueDate = s.DateTime.AddDays(s.Customer.PaymentTerms),
                                                CompanyName = s.InvoiceLines.Any(a => a.JobId == null) ? s.Contract.SitePermit.Site.Company.Name : "Astley Sand & Aggregates Ltd",
                                                s.Customer.InvoiceEmail,
                                                PostmarkClientServerKey = s.InvoiceLines.Any(a => a.JobId == null) ? s.Contract.SitePermit.Site.Company.PostmarkClientServerKey : "************************************",
                                                InvoiceEmailFromAddress = s.InvoiceLines.Any(a => a.JobId == null) ? s.Contract.SitePermit.Site.Company.InvoiceEmailFromAddress : "<EMAIL>",
                                                InvoiceCCEmailAddress = s.InvoiceLines.Any(a => a.JobId == null) ? s.Contract.SitePermit.Site.Company.InvoiceEmailCCAddress : "<EMAIL>",
                                                CustomerCode = s.Customer.Code,

                                                AnyETickets = s.InvoiceLines.Any(a=> a.ETicketId != null),
                                                AnyJobs = s.InvoiceLines.Any(a => a.JobId != null),
                                                AnyHaulierStopIds = s.InvoiceLines.Any(a => a.HaulierStopId != null)
                                            })
                                            .FirstOrDefaultAsync();

        var invoiceDataReplacements = new Dictionary<string, string>
        {
            { "InvoiceNumber", invoiceData.InvoiceCompanyNumber?.ToString() },
            { "DueDate", invoiceData.DueDate.ToString("dd/MM/yyyy") },
            { "CompanyName", invoiceData.CompanyName?.ToString() },
            { "InvoiceEmail", invoiceData.InvoiceEmail?.ToString() },
            { "InvoiceCCEmailAddress", invoiceData.InvoiceCCEmailAddress?.ToString() }
        };

        var invoiceTemplate = await databaseContext.EmailTemplates.Where(w => w.TenantId == invoiceData.TenantId)
                                                      .Where(w => w.Type == (int)EmailType.Invoice)
                                                      .FirstOrDefaultAsync();

        string subject = invoiceTemplate.Subject.ReplaceWithDictionaryKeys(invoiceDataReplacements);
        string textBody = invoiceTemplate.TextTemplate.ReplaceWithDictionaryKeys(invoiceDataReplacements);
        string htmlBody = invoiceTemplate.HtmlTemplate.ReplaceWithDictionaryKeys(invoiceDataReplacements);

        var email = new HT.Database.Email
        {
            Id = Guid.NewGuid(),
            TenantId = currentUserService.TenantId,
            Status = (int)EmailStatus.Queued,
            CreatedDate = DateTime.Now,
            From = invoiceData.InvoiceEmailFromAddress,
            To = invoiceData.InvoiceEmail,
            CC = invoiceData.InvoiceCCEmailAddress,
            HtmlTemplate = htmlBody,
            TextTemplate = textBody,
            Subject = subject,
            InvoiceId = invoiceId,
            PostmarkClientServerKey = invoiceData.PostmarkClientServerKey,
        };
        databaseContext.Emails.Add(email);

        var emailAttachment = new HT.Database.EmailAttachment
        {
            Id = Guid.NewGuid(),
            EmailId = email.Id,
            FileContentType = "application/pdf",
            FileName = $"{invoiceData.CustomerCode}-Invoice-{invoiceData.InvoiceCompanyNumber}.pdf",
            FileUrl = $"https://app-weigh-technology-api.azurewebsites.net/invoice/printpdf?id={invoiceId}"
        };
        databaseContext.EmailAttachments.Add(emailAttachment);

        if (invoiceData.AnyETickets)
        {
            var emailAttachmentTicket = new HT.Database.EmailAttachment
            {
                Id = Guid.NewGuid(),
                EmailId = email.Id,
                FileContentType = "application/pdf",
                FileName = $"{invoiceData.CustomerCode}-ETickets-{invoiceData.InvoiceCompanyNumber}.pdf",
                FileUrl = $"https://app-weigh-technology-api.azurewebsites.net/invoice/PrintETicketsPdf?id={invoiceId}"
            };
            databaseContext.EmailAttachments.Add(emailAttachmentTicket);
        }

        if (invoiceData.AnyJobs)
        {
            var emailAttachmentTicket = new HT.Database.EmailAttachment
            {
                Id = Guid.NewGuid(),
                EmailId = email.Id,
                FileContentType = "application/pdf",
                FileName = $"{invoiceData.CustomerCode}-Jobs-{invoiceData.InvoiceCompanyNumber}.pdf",
                FileUrl = $"https://app-weigh-technology-api.azurewebsites.net/invoice/PrintJobsPdf?id={invoiceId}"
            };
            databaseContext.EmailAttachments.Add(emailAttachmentTicket);
        }

        if (invoiceData.AnyHaulierStopIds)
        {
            var emailAttachmentTicket = new HT.Database.EmailAttachment
            {
                Id = Guid.NewGuid(),
                EmailId = email.Id,
                FileContentType = "application/pdf",
                FileName = $"{invoiceData.CustomerCode}-POD-{invoiceData.InvoiceCompanyNumber}.pdf",
                FileUrl = $"https://app-weigh-technology-api.azurewebsites.net/invoice/PrintPODsPdf?id={invoiceId}"
            };
            databaseContext.EmailAttachments.Add(emailAttachmentTicket);
        }

        await databaseContext.SaveChangesAsync();

        await emailService.SendEmail(email.Id);
    }

}
