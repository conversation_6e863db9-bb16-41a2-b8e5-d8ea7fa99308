﻿using HT.Database;
using HT.Shared.Enums;
using Microsoft.EntityFrameworkCore;
using PostmarkDotNet;

namespace HT.Api.Services.Common.Email;

/// <summary>
/// Sends the emails out from the table via postmark.
/// </summary>
public class EmailService(DatabaseContext db)
{
    /// <summary>
    /// Sends a email via postmark, all the settings come from the email record.
    /// </summary>
    /// <param name="emailId"></param>
    /// <returns></returns>
    public async Task SendEmail(Guid emailId)
    {
        var email = await db.Emails
                             .Include(i => i.EmailAttachments)
                             .FirstOrDefaultAsync(f => f.Id == emailId);

        try
        {
            var message = new PostmarkMessage()
            {
                To = email.To,
                From = email.From,
                Cc = email.CC,
                TrackOpens = true,
                Subject = email.Subject,
                TextBody = email.TextTemplate,
                HtmlBody = email.HtmlTemplate,
                MessageStream = "outbound",
                Tag = "Invoice",
            };

            foreach (var attachment in email.EmailAttachments)
            {
                await AddAttachmentAsync(message, attachment);
            }

            var client = new PostmarkClient(email.PostmarkClientServerKey);
            var sendResult = await client.SendMessageAsync(message);

            if (sendResult.Status != PostmarkStatus.Success)
            {
                throw new Exception("Error Sending Email:" + sendResult.Message);
            }

            email.Status = (int)EmailStatus.Sent;
            email.SentDate = DateTime.Now;
        }
        catch (Exception ex)
        {
            email.Status = (int)EmailStatus.Error;
            email.ErrorMessage = ex.Message;
            await EmergencyEmail.SendEmailAsync($"Email Failed: {emailId} " + ex.Message);
        }

        await db.SaveChangesAsync();
    }

    /// <summary>
    /// This goes to the url and downloads the bytes and adds it to the email as a attachment.
    /// </summary>
    /// <param name="message"></param>
    /// <param name="attachment"></param>
    /// <returns></returns>
    private static async Task AddAttachmentAsync(PostmarkMessage message, EmailAttachment attachment)
    {
        using var httpClient = new HttpClient();
        httpClient.Timeout = new TimeSpan(0, 0, 15);
        var response = await httpClient.GetStreamAsync(attachment.FileUrl);
        message.AddAttachment(response, attachment.FileName, attachment.FileContentType);
    }

}
