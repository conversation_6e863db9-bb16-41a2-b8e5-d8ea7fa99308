﻿using HT.Database;
using HT.Shared.Common;
using HT.Shared.Constants;
using HT.Shared.Enums;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Common.Email;

public class ForgotPasswordService(DatabaseContext db, EmailService emailService)
{
    public async Task SendForgotPasswordEmail(Guid userId)
    {
        var user = await db.Users.FirstOrDefaultAsync(u => u.Id == userId);

        var replacements = new Dictionary<string, string>
        {
            { "Token", user.PasswordResetToken },
        };

        var template = await db.EmailTemplates.FirstOrDefaultAsync(w => w.Type == (int)EmailType.ForgotPassword);

        string subject = template.Subject.ReplaceWithDictionaryKeys(replacements);
        string textBody = template.TextTemplate.ReplaceWithDictionaryKeys(replacements);
        string htmlBody = template.HtmlTemplate.ReplaceWithDictionaryKeys(replacements);

        var email = new HT.Database.Email
        {
            Id = Guid.NewGuid(),
            TenantId = user.TenantId,
            Status = (int)EmailStatus.Queued,
            CreatedDate = DateTime.Now,
            From = "<EMAIL>",
            To = user.Email,
            CC = string.Empty,
            HtmlTemplate = htmlBody,
            TextTemplate = textBody,
            Subject = subject,
            PostmarkClientServerKey = Constants.PostmarkClientServerKey,
        };
        db.Emails.Add(email);

        await db.SaveChangesAsync();

        await emailService.SendEmail(email.Id);
    }

}
