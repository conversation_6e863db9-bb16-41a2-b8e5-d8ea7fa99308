﻿using PostmarkDotNet;

namespace HT.Api.Services.Common.Email;

public static class EmergencyEmail
{
    public static async Task SendEmailAsync(string message)
    {
        var email = new PostmarkMessage()
        {
            To = "<EMAIL>, <EMAIL>",
            From = "<EMAIL>",
            Subject = "Heavy Tech - Emergency",
            TextBody = message,
            HtmlBody = message,
            MessageStream = "outbound",
            Tag = "Emergency",
        };

        var client = new PostmarkClient("************************************");
        await client.SendMessageAsync(email);
    }
}
