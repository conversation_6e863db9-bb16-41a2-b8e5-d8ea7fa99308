﻿using DinkToPdf;
using IPdfConverter = DinkToPdf.Contracts.IConverter;

namespace HT.Api.Services.Common.PDF;

public class UrlToPdf(IPdfConverter pdfConverter)
{
    public IPdfConverter PdfConverter { get; } = pdfConverter;

    public async Task<byte[]> BuildPdf(string url)
    {
        string html = await FetchHtml(url);

        return PdfConverter.Convert(new HtmlToPdfDocument()
        {
            Objects =
            {
                new ObjectSettings
                {
                    HtmlContent = html,
                    FooterSettings = { }
                }
            }
        });
    }

    public static async Task<string> FetchHtml(string url)
    {
        using var httpClient = new HttpClient();

        var response = await httpClient.GetAsync(url);
        if (!response.IsSuccessStatusCode)
        {
            throw new InvalidOperationException($"FetchHtml failed {response.StatusCode} : {response.ReasonPhrase}");
        }
        return await response.Content.ReadAsStringAsync();
    }

}
