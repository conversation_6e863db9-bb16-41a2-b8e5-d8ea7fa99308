﻿using HT.Database;
using HT.Shared.Models.Address;
using Microsoft.EntityFrameworkCore;

namespace HT.Api.Services.Common.Address;

public class AddressService(DatabaseContext databaseContext, CurrentUserService currentUserService)
{
    public async Task<AddressModel> GetAddressAsync(Guid? id)
    {
        if (!id.HasValue || id.Value == Guid.Empty)
        {
            return new AddressModel();
        }

        var addressEntity = await databaseContext.Addresses.FirstOrDefaultAsync(a => a.Id == id);

        var addressModel = new AddressModel
        {
            Id = addressEntity.Id,
            AddressLine1 = addressEntity.AddressLine1,
            AddressLine2 = addressEntity.AddressLine2,
            AddressLine3 = addressEntity.AddressLine3,
            AddressLine4 = addressEntity.AddressLine4,
            AddressPostcode = addressEntity.AddressPostcode,
            Latitude = addressEntity.Latitude ?? 0,
            Longitude = addressEntity.Longitude ?? 0
        };

        return addressModel;
    }

    public async Task<Guid> CreateUpdateAddressAsync(AddressModel addressModel)
    {
        HT.Database.Tables.Address existingAddress = null;

        if (addressModel.Id.HasValue && addressModel.Id != Guid.Empty)
        {
            existingAddress = await databaseContext.Addresses.FirstOrDefaultAsync(a => a.Id == addressModel.Id);
        }

        if (existingAddress == null)
        {
            existingAddress = new HT.Database.Tables.Address
            {
                Id = Guid.NewGuid()
            };
            databaseContext.Addresses.Add(existingAddress);
        }

        existingAddress.TenantId = currentUserService.TenantId;
        existingAddress.AddressLine1 = addressModel.AddressLine1;
        existingAddress.AddressLine2 = addressModel.AddressLine2;
        existingAddress.AddressLine3 = addressModel.AddressLine3;
        existingAddress.AddressLine4 = addressModel.AddressLine4;
        existingAddress.AddressPostcode = addressModel.AddressPostcode;
        existingAddress.Latitude = addressModel.Latitude;
        existingAddress.Longitude = addressModel.Longitude;

        return existingAddress.Id;
    }

}
