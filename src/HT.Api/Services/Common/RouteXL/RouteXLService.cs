﻿using Newtonsoft.Json;
using System.Text;
namespace HT.Api.Services.Common.RouteXL;

public class RouteXlService
{
    private const string Username = "AustinRoute";
    private const string Password = "HelloRoute1!";

    // Define the request URL
    private const string RequestUrl = "https://api.routexl.com/tour";

    // lat - long
    public async Task<Dictionary<string, RouteStop>> GetOptimisedRoute(List<RouteXLLocation> locations)
    {
        try
        {

            using var client = new HttpClient();
            string json = JsonConvert.SerializeObject(locations);
            string requestData = $"locations={json}";
            string credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{Username}:{Password}"));

            using var request = new HttpRequestMessage(HttpMethod.Post, RequestUrl);
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", credentials);
            request.Content = new StringContent(requestData, Encoding.UTF8, "application/x-www-form-urlencoded");

            var response = await client.SendAsync(request);


            if (response.IsSuccessStatusCode)
            {
                string responseBody = await response.Content.ReadAsStringAsync();

                var routeData = System.Text.Json.JsonSerializer.Deserialize<RouteData>(responseBody);

                return routeData.route;

            }
            else
            {
                return null;
            }
        }
        catch (Exception)
        {
            return null;
        }
    }

}

public class RouteXLLocation
{
    public string address { get; set; }
    public string lat { get; set; }
    public string lng { get; set; }
}


public class RouteData
{
    public string id { get; set; }
    public int count { get; set; }
    public bool feasible { get; set; }
    public Dictionary<string, RouteStop> route { get; set; }
}

public class RouteStop
{
    public string name { get; set; }
    public int arrival { get; set; }
    public double distance { get; set; }
}
