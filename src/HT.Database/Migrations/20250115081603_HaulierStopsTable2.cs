﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class HaulierStopsTable2 : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "HaulierStopId",
            table: "RouteLines",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "MobilePhoneNumber",
            table: "HaulierStops",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "PhoneNumber",
            table: "HaulierStops",
            type: "nvarchar(max)",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_RouteLines_HaulierStopId",
            table: "RouteLines",
            column: "HaulierStopId");

        migrationBuilder.AddForeignKey(
            name: "FK_RouteLines_HaulierStops_HaulierStopId",
            table: "RouteLines",
            column: "HaulierStopId",
            principalTable: "HaulierStops",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_RouteLines_HaulierStops_HaulierStopId",
            table: "RouteLines");

        migrationBuilder.DropIndex(
            name: "IX_RouteLines_HaulierStopId",
            table: "RouteLines");

        migrationBuilder.DropColumn(
            name: "HaulierStopId",
            table: "RouteLines");

        migrationBuilder.DropColumn(
            name: "MobilePhoneNumber",
            table: "HaulierStops");

        migrationBuilder.DropColumn(
            name: "PhoneNumber",
            table: "HaulierStops");
    }
}
