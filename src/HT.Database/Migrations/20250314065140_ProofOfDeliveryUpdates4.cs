﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class ProofOfDeliveryUpdates4 : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        string sql = @"DECLARE @newPromptId uniqueidentifier = '0604640d-feaa-4e75-88ea-e213a668ea58';
DECLARE @tenantId uniqueidentifier = '0a9b8eab-06b4-4743-b5bb-4b966997cefc';

INSERT INTO EmailTemplates(
	Id,
	TenantId,
	[Type],
	[Subject],
	[HtmlTemplate],
	[TextTemplate])
VALUES(
	@newPromptId,
	@tenantId,
	4,
	'Proof of Delivery for Order No. {{OrderNumber}}',
	'<!DOCTYPE html><html><head><meta charset=""utf-8""></head><body><p>Hi,</p><br /><br /><p>Please find attached the proof of delivery for order number {{OrderNumber}}.<br /><br /><p>Kind Regards,</p><br /><br /><p>{{CompanyName}}</p><br /><br /></body></html>',
	'Hi,

Please find attached the proof of delivery for order number { { OrderNumber} }.

Kind Regards,
{ { CompanyName} }'
);";

        migrationBuilder.Sql(sql);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {

    }
}
