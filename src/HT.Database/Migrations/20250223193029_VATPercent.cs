﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class VATPercent : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "VATPercent",
                table: "ETickets",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            // Update all existing records to be dfault 20 VAT
            migrationBuilder.Sql("UPDATE ETickets SET VATPercent = 20");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "VATPercent",
                table: "ETickets");
        }
    }
}
