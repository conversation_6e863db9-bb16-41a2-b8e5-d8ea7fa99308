﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class HaulierStopFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "HaulierPrice",
                table: "HaulierStops",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Price",
                table: "HaulierStops",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "PriceIsTonnes",
                table: "HaulierStops",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "Tonnes",
                table: "HaulierStops",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HaulierPrice",
                table: "HaulierStops");

            migrationBuilder.DropColumn(
                name: "Price",
                table: "HaulierStops");

            migrationBuilder.DropColumn(
                name: "PriceIsTonnes",
                table: "HaulierStops");

            migrationBuilder.DropColumn(
                name: "Tonnes",
                table: "HaulierStops");
        }
    }
}
