﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class Files : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "HaulierStopFiles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    HaulierStopId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    AzureStoragePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    AzureStorageURL = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    UploadedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UploadedUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HaulierStopFiles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HaulierStopFiles_HaulierStops_HaulierStopId",
                        column: x => x.HaulierStopId,
                        principalTable: "HaulierStops",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_HaulierStopFiles_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_HaulierStopFiles_Users_UploadedUserId",
                        column: x => x.UploadedUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_HaulierStopFiles_HaulierStopId",
                table: "HaulierStopFiles",
                column: "HaulierStopId");

            migrationBuilder.CreateIndex(
                name: "IX_HaulierStopFiles_TenantId",
                table: "HaulierStopFiles",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_HaulierStopFiles_UploadedUserId",
                table: "HaulierStopFiles",
                column: "UploadedUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HaulierStopFiles");
        }
    }
}
