﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class HaulierContractProducts : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsHaulierContract",
                table: "Contracts",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "TipAddressId",
                table: "Contracts",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "HaulierContractProductLinks",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    HaulierContractId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HaulierContractProductLinks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HaulierContractProductLinks_Contracts_HaulierContractId",
                        column: x => x.HaulierContractId,
                        principalTable: "Contracts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_HaulierContractProductLinks_Products_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_HaulierContractProductLinks_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_TipAddressId",
                table: "Contracts",
                column: "TipAddressId");

            migrationBuilder.CreateIndex(
                name: "IX_HaulierContractProductLinks_HaulierContractId",
                table: "HaulierContractProductLinks",
                column: "HaulierContractId");

            migrationBuilder.CreateIndex(
                name: "IX_HaulierContractProductLinks_ProductId",
                table: "HaulierContractProductLinks",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_HaulierContractProductLinks_TenantId",
                table: "HaulierContractProductLinks",
                column: "TenantId");

            migrationBuilder.AddForeignKey(
                name: "FK_Contracts_Addresses_TipAddressId",
                table: "Contracts",
                column: "TipAddressId",
                principalTable: "Addresses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Contracts_Addresses_TipAddressId",
                table: "Contracts");

            migrationBuilder.DropTable(
                name: "HaulierContractProductLinks");

            migrationBuilder.DropIndex(
                name: "IX_Contracts_TipAddressId",
                table: "Contracts");

            migrationBuilder.DropColumn(
                name: "IsHaulierContract",
                table: "Contracts");

            migrationBuilder.DropColumn(
                name: "TipAddressId",
                table: "Contracts");
        }
    }
}
