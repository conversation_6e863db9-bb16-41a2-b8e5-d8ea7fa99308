﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class JobId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "JobId",
                table: "InvoiceLines",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceLines_JobId",
                table: "InvoiceLines",
                column: "JobId");

            migrationBuilder.AddForeignKey(
                name: "FK_InvoiceLines_Jobs_JobId",
                table: "InvoiceLines",
                column: "JobId",
                principalTable: "Jobs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_InvoiceLines_Jobs_JobId",
                table: "InvoiceLines");

            migrationBuilder.DropIndex(
                name: "IX_InvoiceLines_JobId",
                table: "InvoiceLines");

            migrationBuilder.DropColumn(
                name: "JobId",
                table: "InvoiceLines");
        }
    }
}
