﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class MoveHaulierContractProduct : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "HaulierCustomerProductLinks");

        migrationBuilder.AddColumn<Guid>(
            name: "ProductId",
            table: "ContractLinks",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_ContractLinks_ProductId",
            table: "ContractLinks",
            column: "ProductId");

        migrationBuilder.AddForeignKey(
            name: "FK_ContractLinks_Products_ProductId",
            table: "ContractLinks",
            column: "ProductId",
            principalTable: "Products",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_ContractLinks_Products_ProductId",
            table: "ContractLinks");

        migrationBuilder.DropIndex(
            name: "IX_ContractLinks_ProductId",
            table: "ContractLinks");

        migrationBuilder.DropColumn(
            name: "ProductId",
            table: "ContractLinks");

        migrationBuilder.CreateTable(
            name: "HaulierCustomerProductLinks",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                HaulierCustomerLinkId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_HaulierCustomerProductLinks", x => x.Id);
                table.ForeignKey(
                    name: "FK_HaulierCustomerProductLinks_HaulierCustomerLinks_HaulierCustomerLinkId",
                    column: x => x.HaulierCustomerLinkId,
                    principalTable: "HaulierCustomerLinks",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_HaulierCustomerProductLinks_Products_ProductId",
                    column: x => x.ProductId,
                    principalTable: "Products",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_HaulierCustomerProductLinks_Tenants_TenantId",
                    column: x => x.TenantId,
                    principalTable: "Tenants",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateIndex(
            name: "IX_HaulierCustomerProductLinks_HaulierCustomerLinkId",
            table: "HaulierCustomerProductLinks",
            column: "HaulierCustomerLinkId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierCustomerProductLinks_ProductId",
            table: "HaulierCustomerProductLinks",
            column: "ProductId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierCustomerProductLinks_TenantId",
            table: "HaulierCustomerProductLinks",
            column: "TenantId");
    }
}

