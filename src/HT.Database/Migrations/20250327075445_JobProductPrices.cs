﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class JobProductPrices : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<decimal>(
            name: "Total",
            table: "JobProducts",
            type: "decimal(18,2)",
            nullable: false,
            defaultValue: 0m);

        migrationBuilder.AddColumn<decimal>(
            name: "UnitPrice",
            table: "JobProducts",
            type: "decimal(18,2)",
            nullable: false,
            defaultValue: 0m);

        migrationBuilder.AddColumn<decimal>(
            name: "UnitTotal",
            table: "JobProducts",
            type: "decimal(18,2)",
            nullable: false,
            defaultValue: 0m);

        migrationBuilder.AddColumn<decimal>(
            name: "VATRate",
            table: "JobProducts",
            type: "decimal(18,2)",
            nullable: false,
            defaultValue: 0m);

        migrationBuilder.AddColumn<decimal>(
            name: "VATTotal",
            table: "JobProducts",
            type: "decimal(18,2)",
            nullable: false,
            defaultValue: 0m);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "Total",
            table: "JobProducts");

        migrationBuilder.DropColumn(
            name: "UnitPrice",
            table: "JobProducts");

        migrationBuilder.DropColumn(
            name: "UnitTotal",
            table: "JobProducts");

        migrationBuilder.DropColumn(
            name: "VATRate",
            table: "JobProducts");

        migrationBuilder.DropColumn(
            name: "VATTotal",
            table: "JobProducts");
    }
}
