﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class StopTipAddress : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "ContractLinkId",
            table: "HaulierStops",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_HaulierStops_ContractLinkId",
            table: "HaulierStops",
            column: "ContractLinkId");

        migrationBuilder.AddForeignKey(
            name: "FK_HaulierStops_ContractLinks_ContractLinkId",
            table: "HaulierStops",
            column: "ContractLinkId",
            principalTable: "ContractLinks",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropFore<PERSON>Key(
            name: "FK_HaulierStops_ContractLinks_ContractLinkId",
            table: "HaulierStops");

        migrationBuilder.DropIndex(
            name: "IX_HaulierStops_ContractLinkId",
            table: "HaulierStops");

        migrationBuilder.DropColumn(
            name: "ContractLinkId",
            table: "HaulierStops");
    }
}
