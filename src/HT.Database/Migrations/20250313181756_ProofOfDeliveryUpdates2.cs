﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class ProofOfDeliveryUpdates2 : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_HaulierCustomerLinks_ProofOfDeliveries_ProofOfDeliveryId",
            table: "HaulierCustomerLinks");

        migrationBuilder.DropForeignKey(
            name: "FK_HaulierVehicles_ProofOfDeliveries_ProofOfDeliveryId",
            table: "HaulierVehicles");

        migrationBuilder.DropIndex(
            name: "IX_HaulierVehicles_ProofOfDeliveryId",
            table: "HaulierVehicles");

        migrationBuilder.DropIndex(
            name: "IX_HaulierCustomerLinks_ProofOfDeliveryId",
            table: "HaulierCustomerLinks");

        migrationBuilder.DropColumn(
            name: "CarrierLicence",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "CarrierLicenceExpiryDate",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "Code",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "Name",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "Status",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "ProofOfDeliveryId",
            table: "HaulierVehicles");

        migrationBuilder.DropColumn(
            name: "ProofOfDeliveryId",
            table: "HaulierCustomerLinks");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<string>(
            name: "CarrierLicence",
            table: "ProofOfDeliveries",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);

        migrationBuilder.AddColumn<DateTime>(
            name: "CarrierLicenceExpiryDate",
            table: "ProofOfDeliveries",
            type: "datetime2",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "Code",
            table: "ProofOfDeliveries",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "Name",
            table: "ProofOfDeliveries",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);

        migrationBuilder.AddColumn<int>(
            name: "Status",
            table: "ProofOfDeliveries",
            type: "int",
            nullable: false,
            defaultValue: 0);

        migrationBuilder.AddColumn<Guid>(
            name: "ProofOfDeliveryId",
            table: "HaulierVehicles",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<Guid>(
            name: "ProofOfDeliveryId",
            table: "HaulierCustomerLinks",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_HaulierVehicles_ProofOfDeliveryId",
            table: "HaulierVehicles",
            column: "ProofOfDeliveryId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierCustomerLinks_ProofOfDeliveryId",
            table: "HaulierCustomerLinks",
            column: "ProofOfDeliveryId");

        migrationBuilder.AddForeignKey(
            name: "FK_HaulierCustomerLinks_ProofOfDeliveries_ProofOfDeliveryId",
            table: "HaulierCustomerLinks",
            column: "ProofOfDeliveryId",
            principalTable: "ProofOfDeliveries",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            name: "FK_HaulierVehicles_ProofOfDeliveries_ProofOfDeliveryId",
            table: "HaulierVehicles",
            column: "ProofOfDeliveryId",
            principalTable: "ProofOfDeliveries",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }
}
