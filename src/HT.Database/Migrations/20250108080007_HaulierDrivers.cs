﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class HaulierDrivers : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "DriverUserId",
            table: "Vehicles",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "FirstName",
            table: "Users",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "Surname",
            table: "Users",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_Vehicles_DriverUserId",
            table: "Vehicles",
            column: "DriverUserId");

        migrationBuilder.AddForeignKey(
            name: "FK_Vehicles_Users_DriverUserId",
            table: "Vehicles",
            column: "DriverUserId",
            principalTable: "Users",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_Vehicles_Users_DriverUserId",
            table: "Vehicles");

        migrationBuilder.DropIndex(
            name: "IX_Vehicles_DriverUserId",
            table: "Vehicles");

        migrationBuilder.DropColumn(
            name: "DriverUserId",
            table: "Vehicles");

        migrationBuilder.DropColumn(
            name: "FirstName",
            table: "Users");

        migrationBuilder.DropColumn(
            name: "Surname",
            table: "Users");
    }
}
