﻿// <auto-generated />
using System;
using HT.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace HT.Database.Migrations
{
    [DbContext(typeof(DatabaseContext))]
    [Migration("20250314065140_ProofOfDeliveryUpdates4")]
    partial class ProofOfDeliveryUpdates4
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("HT.Database.Company", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AddressLine1")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine2")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine3")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine4")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressPostcode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("BankAccountNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("BankName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("BankSort")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CompanyNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("InvoiceEmailCCAddress")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("InvoiceEmailFromAddress")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Phone")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PostmarkClientServerKey")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RegisterdOfficeAddressLine1")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RegisterdOfficeAddressLine2")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RegisterdOfficeAddressLine3")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RegisterdOfficeAddressLine4")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RegisterdOfficeAddressPostcode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("VATNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Website")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Companies");
                });

            modelBuilder.Entity("HT.Database.Contract", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("AddressLatitude")
                        .HasColumnType("decimal(10, 6)");

                    b.Property<string>("AddressLine1")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine2")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine3")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine4")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal?>("AddressLongitude")
                        .HasColumnType("decimal(10, 6)");

                    b.Property<string>("AddressPostcode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AnalysisEnquiryNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Borough")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("ContractNumber")
                        .HasMaxLength(100)
                        .HasColumnType("int");

                    b.Property<bool>("IsHaulierContract")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OrderNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("OverallTotalAllowance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("RemainingAllowance")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SiteEmail")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("SitePermitId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SitePermitProductLinkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TipAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SitePermitId");

                    b.HasIndex("SitePermitProductLinkId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TipAddressId");

                    b.ToTable("Contracts");
                });

            modelBuilder.Entity("HT.Database.ContractAllowance", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("AllowanceAmountTonnes")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Message")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("ContractAllowances");
                });

            modelBuilder.Entity("HT.Database.ContractLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AnalysisEnquiryNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OrderNumber")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PricePerLoad")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PricePerTonne")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ContractLinks");
                });

            modelBuilder.Entity("HT.Database.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AddressLine1")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine2")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine3")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine4")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressPostcode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("CustomerAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("CustomerNumber")
                        .HasColumnType("int");

                    b.Property<Guid?>("CustomerSiteLinkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("DefaultPaymentType")
                        .HasColumnType("int");

                    b.Property<string>("DeliveryNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ETicketEmail")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("FirstRoundOrderDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("InvoiceEmail")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MobilePhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("OnStop")
                        .HasColumnType("bit");

                    b.Property<int>("OrderFrequencyDays")
                        .HasColumnType("int");

                    b.Property<int>("PaymentTerms")
                        .HasColumnType("int");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("PreferredDeliveryDay")
                        .HasColumnType("int");

                    b.Property<Guid?>("RouteAreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CustomerAddressId");

                    b.HasIndex("CustomerSiteLinkId");

                    b.HasIndex("RouteAreaId");

                    b.HasIndex("TenantId");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("HT.Database.CustomerPayment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("PaymentType")
                        .HasColumnType("int");

                    b.Property<Guid?>("RouteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("OrderId");

                    b.HasIndex("RouteId");

                    b.HasIndex("TenantId");

                    b.ToTable("CustomerPayments");
                });

            modelBuilder.Entity("HT.Database.CustomerSiteLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("SiteId");

                    b.HasIndex("TenantId");

                    b.ToTable("CustomerSiteLinks");
                });

            modelBuilder.Entity("HT.Database.CustomerTag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CustomerTags");
                });

            modelBuilder.Entity("HT.Database.CustomerTagLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CustomerTagId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerTagId");

                    b.HasIndex("TenantId");

                    b.ToTable("CustomerTagLinks");
                });

            modelBuilder.Entity("HT.Database.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AzureStoragePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("AzureStorageURL")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FileExtention")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FileName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("HaulierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("PurchaseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UploadedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UploadedUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("HaulierId");

                    b.HasIndex("PurchaseId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UploadedUserId");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("HT.Database.ETicket", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DriverName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("EntryDateTime")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("EntryWeight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("ExitDateTime")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("ExitWeight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("HasSiteSurcharge")
                        .HasColumnType("bit");

                    b.Property<Guid>("HaulierVehicleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("HazardousRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("NetWeight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrderNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal?>("PricePerTonne")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("PricedByKilogram")
                        .HasColumnType("bit");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("ProductPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Remarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiteETicketNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SiteSurchargeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("SurchargePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SurchargeReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TicketNumber")
                        .HasColumnType("int");

                    b.Property<decimal>("Tonnes")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TransferNote")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<bool>("UseTareWeight")
                        .HasColumnType("bit");

                    b.Property<decimal>("VATPercent")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("VATTotal")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("HaulierVehicleId");

                    b.HasIndex("ProductId");

                    b.HasIndex("SiteId");

                    b.HasIndex("SiteSurchargeId");

                    b.HasIndex("TenantId");

                    b.ToTable("ETickets");
                });

            modelBuilder.Entity("HT.Database.Email", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CC")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("From")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HtmlTemplate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("InvoiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("PostmarkClientServerKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("SentDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TextTemplate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("To")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("TenantId");

                    b.ToTable("Emails");
                });

            modelBuilder.Entity("HT.Database.EmailAttachment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("EmailId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FileContentType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FileName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FileUrl")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("EmailId");

                    b.ToTable("EmailAttachments");
                });

            modelBuilder.Entity("HT.Database.EmailTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("HtmlTemplate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TextTemplate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("EmailTemplates");
                });

            modelBuilder.Entity("HT.Database.Export", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AzureStoragePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("AzureStorageURL")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("LineCount")
                        .HasColumnType("int");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Exports");
                });

            modelBuilder.Entity("HT.Database.FavouriteAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("TenantId");

                    b.ToTable("FavouriteAddresses");
                });

            modelBuilder.Entity("HT.Database.Haulier", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CarrierLicence")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("CarrierLicenceExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Code")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Hauliers");
                });

            modelBuilder.Entity("HT.Database.HaulierCustomerLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("HaulierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("HaulierId");

                    b.HasIndex("TenantId");

                    b.ToTable("HaulierCustomerLinks");
                });

            modelBuilder.Entity("HT.Database.HaulierStop", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CompletedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ContractLinkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("GrossWeight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("HaulierPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MobilePhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("PhotoRequired")
                        .HasColumnType("bit");

                    b.Property<decimal?>("PricePerLoad")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PricePerTonne")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("ProofOfDeliveryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("RouteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("SignatureRequired")
                        .HasColumnType("bit");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal?>("TareWeight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Time")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("TipStopId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Tonnes")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime>("VisitDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("ContractId");

                    b.HasIndex("ContractLinkId");

                    b.HasIndex("ProofOfDeliveryId");

                    b.HasIndex("RouteId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TipStopId");

                    b.ToTable("HaulierStops");
                });

            modelBuilder.Entity("HT.Database.HaulierStopFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AzureStoragePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("AzureStorageURL")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<Guid>("HaulierStopId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsSignature")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UploadedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UploadedUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("HaulierStopId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UploadedUserId");

                    b.ToTable("HaulierStopFiles");
                });

            modelBuilder.Entity("HT.Database.HaulierVehicle", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("DriverUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("HaulierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("HaulierVehicleTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LastDriverName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Registration")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("TareWeight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("DriverUserId")
                        .IsUnique()
                        .HasFilter("[DriverUserId] IS NOT NULL");

                    b.HasIndex("HaulierId");

                    b.HasIndex("HaulierVehicleTypeId");

                    b.HasIndex("TenantId");

                    b.ToTable("HaulierVehicles");
                });

            modelBuilder.Entity("HT.Database.HaulierVehicleType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HaulierVehicleTypes");
                });

            modelBuilder.Entity("HT.Database.Invoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Exported")
                        .HasColumnType("bit");

                    b.Property<string>("InvoiceCompanyNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("InvoiceNumber")
                        .HasColumnType("int");

                    b.Property<string>("OrderNumbers")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("UnitTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("VATTotal")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("TenantId");

                    b.ToTable("Invoices");
                });

            modelBuilder.Entity("HT.Database.InvoiceLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ETicketId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("HaulierStopId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("LineNumber")
                        .HasColumnType("int");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("UnitTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("VATRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("VATTotal")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("ETicketId");

                    b.HasIndex("HaulierStopId");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("TenantId");

                    b.ToTable("InvoiceLines");
                });

            modelBuilder.Entity("HT.Database.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("DeliveryAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("DeliveryStatus")
                        .HasColumnType("int");

                    b.Property<int>("DeliveryType")
                        .HasColumnType("int");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OrderDeliveryNotes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.Property<bool>("RoundOrder")
                        .HasColumnType("bit");

                    b.Property<Guid?>("RouteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("UnitTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("VATTotal")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("DeliveryAddressId");

                    b.HasIndex("RouteId");

                    b.HasIndex("TenantId");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("HT.Database.OrderEvent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("OrderEvents");
                });

            modelBuilder.Entity("HT.Database.OrderLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("CustomUnitPrice")
                        .HasColumnType("bit");

                    b.Property<int>("LineNumber")
                        .HasColumnType("int");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ProductPriceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("UnitTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("VATRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("VATTotal")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductPriceId");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderLines");
                });

            modelBuilder.Entity("HT.Database.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("DefaultPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("EWCCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("OperatingProfit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PricePerKilogram")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PricePerLoad")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProductCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ProductGroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("ProductWeightKilograms")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductGroupId");

                    b.HasIndex("TenantId");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("HT.Database.ProductGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductGroups");
                });

            modelBuilder.Entity("HT.Database.ProductPrice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<decimal>("VATPercent")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductPrices");
                });

            modelBuilder.Entity("HT.Database.ProofOfDelivery", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ContractName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CustomerName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("DriverName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EWCCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal?>("GrossWeight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("PhotoFileId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ProductName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("SignatureFileId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SignedByName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("StopAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("StopCompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("TareWeight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TipStopAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("TipStopCompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VehicleRegistration")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("CustomerId");

                    b.HasIndex("PhotoFileId");

                    b.HasIndex("SignatureFileId");

                    b.HasIndex("StopAddressId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TipStopAddressId");

                    b.ToTable("ProofOfDeliveries");
                });

            modelBuilder.Entity("HT.Database.Purchase", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Paid")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("PaymentDueDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("PurchaseDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PurchaseOrderNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.HasIndex("TenantId");

                    b.ToTable("Purchases");
                });

            modelBuilder.Entity("HT.Database.Recycling", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("TonnageRecycled")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalTonnage")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Recycling");
                });

            modelBuilder.Entity("HT.Database.Route", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("EndAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("HaulierDriverUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("HaulierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("HaulierVehicleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("RouteAreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("RouteDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("RouteNumber")
                        .HasColumnType("int");

                    b.Property<int>("RouteOptimisedStatus")
                        .HasColumnType("int");

                    b.Property<Guid?>("StartAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VehicleDriverId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VehicleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("EndAddressId");

                    b.HasIndex("HaulierDriverUserId");

                    b.HasIndex("HaulierId");

                    b.HasIndex("HaulierVehicleId");

                    b.HasIndex("RouteAreaId");

                    b.HasIndex("StartAddressId");

                    b.HasIndex("TenantId");

                    b.HasIndex("VehicleDriverId");

                    b.HasIndex("VehicleId");

                    b.ToTable("Routes");
                });

            modelBuilder.Entity("HT.Database.RouteArea", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("EndAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsRoundRoute")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("StartAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("EndAddressId");

                    b.HasIndex("StartAddressId");

                    b.HasIndex("TenantId");

                    b.ToTable("RouteAreas");
                });

            modelBuilder.Entity("HT.Database.RouteAreaLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("LinkedRouteAreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RouteAreaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("RouteAreaId");

                    b.HasIndex("TenantId");

                    b.ToTable("RouteAreaLinks");
                });

            modelBuilder.Entity("HT.Database.RouteCashAmount", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Display")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Reason")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<Guid>("RouteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RouteId");

                    b.HasIndex("TenantId");

                    b.ToTable("RouteCashAmounts");
                });

            modelBuilder.Entity("HT.Database.RouteLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("DeliveryStatus")
                        .HasColumnType("int");

                    b.Property<Guid?>("HaulierStopId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("int");

                    b.Property<int>("PaymentType")
                        .HasColumnType("int");

                    b.Property<Guid>("RouteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("StopNumber")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("VisitId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("HaulierStopId");

                    b.HasIndex("OrderId");

                    b.HasIndex("RouteId");

                    b.HasIndex("TenantId");

                    b.HasIndex("VisitId");

                    b.ToTable("RouteLines");
                });

            modelBuilder.Entity("HT.Database.RouteProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("QuantityFromExtras")
                        .HasColumnType("int");

                    b.Property<int>("QuantityFromOrders")
                        .HasColumnType("int");

                    b.Property<int>("QuantityTotal")
                        .HasColumnType("int");

                    b.Property<Guid>("RouteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("RouteId");

                    b.HasIndex("TenantId");

                    b.ToTable("RouteProducts");
                });

            modelBuilder.Entity("HT.Database.Site", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AddressLine1")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine2")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine3")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine4")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressPostcode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CustomerSiteLinkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("HasRecycling")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CustomerSiteLinkId");

                    b.HasIndex("TenantId");

                    b.ToTable("Sites");
                });

            modelBuilder.Entity("HT.Database.SitePermit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LicenceNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("TonsPerYear")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.HasIndex("TenantId");

                    b.ToTable("SitePermits");
                });

            modelBuilder.Entity("HT.Database.SitePermitProductLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SitePermitId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("SitePermitId");

                    b.HasIndex("TenantId");

                    b.ToTable("SitePermitProductLinks");
                });

            modelBuilder.Entity("HT.Database.SiteSurcharge", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.HasIndex("TenantId");

                    b.ToTable("SiteSurcharges");
                });

            modelBuilder.Entity("HT.Database.Supplier", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MobilePhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("SupplierAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SupplierAddressId");

                    b.HasIndex("TenantId");

                    b.ToTable("Suppliers");
                });

            modelBuilder.Entity("HT.Database.Tables.ActivityLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateTimeUtc")
                        .HasColumnType("datetime2");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("PrimaryObjectId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("ActivityLogs");
                });

            modelBuilder.Entity("HT.Database.Tables.Address", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AddressLine1")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine2")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine3")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressLine4")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("AddressPostcode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("GeocodeFailed")
                        .HasColumnType("bit");

                    b.Property<decimal?>("Latitude")
                        .HasColumnType("decimal(10, 6)");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("decimal(10, 6)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Addresses");
                });

            modelBuilder.Entity("HT.Database.Tables.PortalEvent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("PortalEventTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("RelatedEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("PortalEvents");
                });

            modelBuilder.Entity("HT.Database.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("FleetModule")
                        .HasColumnType("bit");

                    b.Property<bool>("HaulierModule")
                        .HasColumnType("bit");

                    b.Property<string>("LogoUrl")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("OrderModule")
                        .HasColumnType("bit");

                    b.Property<string>("PrimaryColour")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("Production")
                        .HasColumnType("bit");

                    b.Property<bool>("PurchaseLedgerModule")
                        .HasColumnType("bit");

                    b.Property<bool>("RouteModule")
                        .HasColumnType("bit");

                    b.Property<string>("SecondaryColour")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("WebsiteModule")
                        .HasColumnType("bit");

                    b.Property<bool>("WeighbridgeModule")
                        .HasColumnType("bit");

                    b.Property<bool>("WorkforceModule")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.ToTable("Tenants");
                });

            modelBuilder.Entity("HT.Database.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Password")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("PasswordResetExpiry")
                        .HasColumnType("datetime2");

                    b.Property<string>("PasswordResetToken")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Surname")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("UserType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("HT.Database.UserLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConnectionId")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("Url")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("UserLogs");
                });

            modelBuilder.Entity("HT.Database.UserSiteLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SiteId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("UserSiteLinks");
                });

            modelBuilder.Entity("HT.Database.Vehicle", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Capacity")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Make")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Model")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Registration")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Vehicles");
                });

            modelBuilder.Entity("HT.Database.VehicleDriver", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FirstName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LastName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Nickname")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("VehicleDrivers");
                });

            modelBuilder.Entity("HT.Database.Visit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OrderDeliveryNotes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("RouteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("VisitDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("RouteId");

                    b.HasIndex("TenantId");

                    b.ToTable("Visits");
                });

            modelBuilder.Entity("HT.Database.Website", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CustomerAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EmailAddress")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FacebookUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("FooterHtml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GoogleAnalyticsHtml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HeadHtml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HeaderHtml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InstagramUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("LogoUrl")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PinterestUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("PrimaryColour")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("SecondaryColour")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Slogan")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TikTokUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("TopHeaderHtml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TwitterUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("YouTubeUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerAddressId");

                    b.HasIndex("TenantId");

                    b.ToTable("Websites");
                });

            modelBuilder.Entity("HT.Database.WebsiteActivity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("IP")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Referer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RequestData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("Url")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("UserAgent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("WebsiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("WebsiteMediaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("WebsitePageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("WebsiteActivities");
                });

            modelBuilder.Entity("HT.Database.WebsiteAuthor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ImageWebsiteMediaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("LastModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<Guid>("WebsiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ImageWebsiteMediaId");

                    b.HasIndex("WebsiteId");

                    b.ToTable("WebsiteAuthor");
                });

            modelBuilder.Entity("HT.Database.WebsiteDomain", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Default")
                        .HasColumnType("bit");

                    b.Property<string>("Domain")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("WebsiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("WebsiteId");

                    b.ToTable("WebsiteDomains");
                });

            modelBuilder.Entity("HT.Database.WebsiteMedia", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AzureStoragePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("AzureStorageURL")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ContentType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("FileExtention")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FileName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("LastModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Slug")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid>("WebsiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("WebsiteMediaFolderId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("WebsiteId");

                    b.HasIndex("WebsiteMediaFolderId");

                    b.ToTable("WebsiteMedia");
                });

            modelBuilder.Entity("HT.Database.WebsiteMediaFolder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ParentFolderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ParentWebsiteMediaFolderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("WebsiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ParentWebsiteMediaFolderId");

                    b.HasIndex("WebsiteId");

                    b.ToTable("WebsiteMediaFolders");
                });

            modelBuilder.Entity("HT.Database.WebsiteNewsletter", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("WebsiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("WebsiteId");

                    b.ToTable("WebsiteNewsletters");
                });

            modelBuilder.Entity("HT.Database.WebsitePage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ContentHtml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContentMarkdown")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("MetaDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("PageTemplateType")
                        .HasColumnType("int");

                    b.Property<string>("RedirectUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Slug")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("ThumbnailWebsiteMediaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Title")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid?>("WebsiteAuthorId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("WebsiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("WebsitePageFolderId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ThumbnailWebsiteMediaId");

                    b.HasIndex("WebsiteAuthorId");

                    b.HasIndex("WebsiteId");

                    b.HasIndex("WebsitePageFolderId");

                    b.ToTable("WebsitePages");
                });

            modelBuilder.Entity("HT.Database.WebsitePageBlock", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ContentHtml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContentMarkdown")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid?>("WebsiteMediaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("WebsitePageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("WebsiteMediaId");

                    b.HasIndex("WebsitePageId");

                    b.ToTable("WebsitePageBlocks");
                });

            modelBuilder.Entity("HT.Database.WebsitePageBlockLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid?>("WebsiteMediaFolderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("WebsiteMediaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("WebsitePageBlockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("WebsitePageFolderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("WebsitePageId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("WebsiteMediaFolderId");

                    b.HasIndex("WebsiteMediaId");

                    b.HasIndex("WebsitePageBlockId");

                    b.HasIndex("WebsitePageFolderId");

                    b.HasIndex("WebsitePageId");

                    b.ToTable("WebsitePageBlockLinks");
                });

            modelBuilder.Entity("HT.Database.WebsitePageFolder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ParentFolderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ParentWebsitePageFolderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("WebsiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ParentWebsitePageFolderId");

                    b.HasIndex("WebsiteId");

                    b.ToTable("WebsitePageFolders");
                });

            modelBuilder.Entity("HT.Database.WebsiteQuote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("WebsiteId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("WebsiteId");

                    b.ToTable("WebsiteQuotes");
                });

            modelBuilder.Entity("HT.Database.Company", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Contract", b =>
                {
                    b.HasOne("HT.Database.SitePermit", "SitePermit")
                        .WithMany()
                        .HasForeignKey("SitePermitId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.SitePermitProductLink", "SitePermitProductLink")
                        .WithMany()
                        .HasForeignKey("SitePermitProductLinkId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tables.Address", "TipAddress")
                        .WithMany()
                        .HasForeignKey("TipAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("SitePermit");

                    b.Navigation("SitePermitProductLink");

                    b.Navigation("Tenant");

                    b.Navigation("TipAddress");
                });

            modelBuilder.Entity("HT.Database.ContractAllowance", b =>
                {
                    b.HasOne("HT.Database.Contract", "Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Contract");

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("HT.Database.ContractLink", b =>
                {
                    b.HasOne("HT.Database.Contract", "Contract")
                        .WithMany("ContractLinks")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany("ContractLinks")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Contract");

                    b.Navigation("Customer");

                    b.Navigation("Product");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Customer", b =>
                {
                    b.HasOne("HT.Database.Tables.Address", "CustomerAddress")
                        .WithMany()
                        .HasForeignKey("CustomerAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.CustomerSiteLink", null)
                        .WithMany("Customers")
                        .HasForeignKey("CustomerSiteLinkId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.RouteArea", "RouteArea")
                        .WithMany("RouteCustomers")
                        .HasForeignKey("RouteAreaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CustomerAddress");

                    b.Navigation("RouteArea");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.CustomerPayment", b =>
                {
                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany("CustomerPayments")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Order", "Order")
                        .WithMany("Payments")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Route", "Route")
                        .WithMany()
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Order");

                    b.Navigation("Route");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.CustomerSiteLink", b =>
                {
                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Site");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.CustomerTag", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.CustomerTagLink", b =>
                {
                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany("CustomerTagLinks")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.CustomerTag", "CustomerTag")
                        .WithMany("CustomerTagLinks")
                        .HasForeignKey("CustomerTagId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("CustomerTag");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Document", b =>
                {
                    b.HasOne("HT.Database.Contract", "Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Haulier", "Haulier")
                        .WithMany()
                        .HasForeignKey("HaulierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Purchase", "Purchase")
                        .WithMany()
                        .HasForeignKey("PurchaseId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.User", "UploadedUser")
                        .WithMany()
                        .HasForeignKey("UploadedUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Contract");

                    b.Navigation("Customer");

                    b.Navigation("Haulier");

                    b.Navigation("Purchase");

                    b.Navigation("Tenant");

                    b.Navigation("UploadedUser");
                });

            modelBuilder.Entity("HT.Database.ETicket", b =>
                {
                    b.HasOne("HT.Database.Contract", "Contract")
                        .WithMany("ETickets")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany("ETickets")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.HaulierVehicle", "HaulierVehicle")
                        .WithMany()
                        .HasForeignKey("HaulierVehicleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.SiteSurcharge", "SiteSurcharge")
                        .WithMany()
                        .HasForeignKey("SiteSurchargeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Contract");

                    b.Navigation("Customer");

                    b.Navigation("HaulierVehicle");

                    b.Navigation("Product");

                    b.Navigation("Site");

                    b.Navigation("SiteSurcharge");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Email", b =>
                {
                    b.HasOne("HT.Database.Invoice", "Invoice")
                        .WithMany()
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Invoice");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.EmailAttachment", b =>
                {
                    b.HasOne("HT.Database.Email", "Email")
                        .WithMany("EmailAttachments")
                        .HasForeignKey("EmailId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Email");
                });

            modelBuilder.Entity("HT.Database.EmailTemplate", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Export", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.FavouriteAddress", b =>
                {
                    b.HasOne("HT.Database.Tables.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Address");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Haulier", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.HaulierCustomerLink", b =>
                {
                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Haulier", "Haulier")
                        .WithMany("HaulierCustomerLinks")
                        .HasForeignKey("HaulierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Haulier");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.HaulierStop", b =>
                {
                    b.HasOne("HT.Database.Tables.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Contract", "Contract")
                        .WithMany("HaulierStops")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.ContractLink", "ContractLink")
                        .WithMany()
                        .HasForeignKey("ContractLinkId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.ProofOfDelivery", "ProofOfDelivery")
                        .WithMany()
                        .HasForeignKey("ProofOfDeliveryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Route", "Route")
                        .WithMany("HaulierStops")
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.HaulierStop", "TipStop")
                        .WithMany()
                        .HasForeignKey("TipStopId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Address");

                    b.Navigation("Contract");

                    b.Navigation("ContractLink");

                    b.Navigation("ProofOfDelivery");

                    b.Navigation("Route");

                    b.Navigation("Tenant");

                    b.Navigation("TipStop");
                });

            modelBuilder.Entity("HT.Database.HaulierStopFile", b =>
                {
                    b.HasOne("HT.Database.HaulierStop", "HaulierStop")
                        .WithMany("HaulierStopFiles")
                        .HasForeignKey("HaulierStopId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.User", "UploadedUser")
                        .WithMany()
                        .HasForeignKey("UploadedUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("HaulierStop");

                    b.Navigation("Tenant");

                    b.Navigation("UploadedUser");
                });

            modelBuilder.Entity("HT.Database.HaulierVehicle", b =>
                {
                    b.HasOne("HT.Database.User", "DriverUser")
                        .WithOne("HaulierVehicle")
                        .HasForeignKey("HT.Database.HaulierVehicle", "DriverUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Haulier", "Haulier")
                        .WithMany("HaulierVehicles")
                        .HasForeignKey("HaulierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.HaulierVehicleType", "HaulierVehicleType")
                        .WithMany()
                        .HasForeignKey("HaulierVehicleTypeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("DriverUser");

                    b.Navigation("Haulier");

                    b.Navigation("HaulierVehicleType");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.HaulierVehicleType", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Invoice", b =>
                {
                    b.HasOne("HT.Database.Contract", "Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Contract");

                    b.Navigation("Customer");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.InvoiceLine", b =>
                {
                    b.HasOne("HT.Database.ETicket", "ETicket")
                        .WithMany()
                        .HasForeignKey("ETicketId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.HaulierStop", "HaulierStop")
                        .WithMany()
                        .HasForeignKey("HaulierStopId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Invoice", "Invoice")
                        .WithMany("InvoiceLines")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ETicket");

                    b.Navigation("HaulierStop");

                    b.Navigation("Invoice");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Order", b =>
                {
                    b.HasOne("HT.Database.User", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany("Orders")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tables.Address", "DeliveryAddress")
                        .WithMany()
                        .HasForeignKey("DeliveryAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Route", "Route")
                        .WithMany()
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatedByUser");

                    b.Navigation("Customer");

                    b.Navigation("DeliveryAddress");

                    b.Navigation("Route");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.OrderEvent", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("HT.Database.OrderLine", b =>
                {
                    b.HasOne("HT.Database.Order", "Order")
                        .WithMany("OrderLines")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.ProductPrice", "ProductPrice")
                        .WithMany()
                        .HasForeignKey("ProductPriceId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Order");

                    b.Navigation("Product");

                    b.Navigation("ProductPrice");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Product", b =>
                {
                    b.HasOne("HT.Database.ProductGroup", "ProductGroup")
                        .WithMany()
                        .HasForeignKey("ProductGroupId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ProductGroup");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.ProductGroup", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.ProductPrice", b =>
                {
                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Product", "Product")
                        .WithMany("ProductPrices")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Product");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.ProofOfDelivery", b =>
                {
                    b.HasOne("HT.Database.Contract", "Contract")
                        .WithMany()
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.HaulierStopFile", "PhotoFile")
                        .WithMany()
                        .HasForeignKey("PhotoFileId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.HaulierStopFile", "SignatureFile")
                        .WithMany()
                        .HasForeignKey("SignatureFileId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tables.Address", "StopAddress")
                        .WithMany()
                        .HasForeignKey("StopAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tables.Address", "TipStopAddress")
                        .WithMany()
                        .HasForeignKey("TipStopAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Contract");

                    b.Navigation("Customer");

                    b.Navigation("PhotoFile");

                    b.Navigation("SignatureFile");

                    b.Navigation("StopAddress");

                    b.Navigation("Tenant");

                    b.Navigation("TipStopAddress");
                });

            modelBuilder.Entity("HT.Database.Purchase", b =>
                {
                    b.HasOne("HT.Database.Supplier", "Supplier")
                        .WithMany("Purchases")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Supplier");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Recycling", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Route", b =>
                {
                    b.HasOne("HT.Database.Tables.Address", "EndAddress")
                        .WithMany()
                        .HasForeignKey("EndAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.User", "HaulierDriverUser")
                        .WithMany()
                        .HasForeignKey("HaulierDriverUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Haulier", "Haulier")
                        .WithMany()
                        .HasForeignKey("HaulierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.HaulierVehicle", "HaulierVehicle")
                        .WithMany()
                        .HasForeignKey("HaulierVehicleId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.RouteArea", "RouteArea")
                        .WithMany("Routes")
                        .HasForeignKey("RouteAreaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tables.Address", "StartAddress")
                        .WithMany()
                        .HasForeignKey("StartAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.VehicleDriver", "VehicleDriver")
                        .WithMany()
                        .HasForeignKey("VehicleDriverId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("EndAddress");

                    b.Navigation("Haulier");

                    b.Navigation("HaulierDriverUser");

                    b.Navigation("HaulierVehicle");

                    b.Navigation("RouteArea");

                    b.Navigation("StartAddress");

                    b.Navigation("Tenant");

                    b.Navigation("Vehicle");

                    b.Navigation("VehicleDriver");
                });

            modelBuilder.Entity("HT.Database.RouteArea", b =>
                {
                    b.HasOne("HT.Database.Tables.Address", "EndAddress")
                        .WithMany()
                        .HasForeignKey("EndAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tables.Address", "StartAddress")
                        .WithMany()
                        .HasForeignKey("StartAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("EndAddress");

                    b.Navigation("StartAddress");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.RouteAreaLink", b =>
                {
                    b.HasOne("HT.Database.RouteArea", "RouteArea")
                        .WithMany("RouteAreaLinks")
                        .HasForeignKey("RouteAreaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("RouteArea");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.RouteCashAmount", b =>
                {
                    b.HasOne("HT.Database.Route", "Route")
                        .WithMany("RouteCashAmounts")
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Route");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.RouteLine", b =>
                {
                    b.HasOne("HT.Database.HaulierStop", "HaulierStop")
                        .WithMany()
                        .HasForeignKey("HaulierStopId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Order", "Order")
                        .WithMany("RouteLines")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Route", "Route")
                        .WithMany("RouteLines")
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Visit", "Visit")
                        .WithMany()
                        .HasForeignKey("VisitId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("HaulierStop");

                    b.Navigation("Order");

                    b.Navigation("Route");

                    b.Navigation("Tenant");

                    b.Navigation("Visit");
                });

            modelBuilder.Entity("HT.Database.RouteProduct", b =>
                {
                    b.HasOne("HT.Database.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Route", "Route")
                        .WithMany("RouteProducts")
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Route");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Site", b =>
                {
                    b.HasOne("HT.Database.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.CustomerSiteLink", null)
                        .WithMany("Sites")
                        .HasForeignKey("CustomerSiteLinkId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Company");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.SitePermit", b =>
                {
                    b.HasOne("HT.Database.Site", "Site")
                        .WithMany("SitePermits")
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Site");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.SitePermitProductLink", b =>
                {
                    b.HasOne("HT.Database.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.SitePermit", "SitePermit")
                        .WithMany("SitePermitProductLinks")
                        .HasForeignKey("SitePermitId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("SitePermit");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.SiteSurcharge", b =>
                {
                    b.HasOne("HT.Database.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Site");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Supplier", b =>
                {
                    b.HasOne("HT.Database.Tables.Address", "SupplierAddress")
                        .WithMany()
                        .HasForeignKey("SupplierAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("SupplierAddress");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Tables.Address", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Tables.PortalEvent", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("HT.Database.User", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.UserLog", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("HT.Database.UserSiteLink", b =>
                {
                    b.HasOne("HT.Database.Site", "Site")
                        .WithMany()
                        .HasForeignKey("SiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.User", "User")
                        .WithMany("UserSiteLinks")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Site");

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("HT.Database.Vehicle", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.VehicleDriver", b =>
                {
                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Visit", b =>
                {
                    b.HasOne("HT.Database.Customer", "Customer")
                        .WithMany("Visits")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.Route", "Route")
                        .WithMany()
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Route");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.Website", b =>
                {
                    b.HasOne("HT.Database.Tables.Address", "CustomerAddress")
                        .WithMany()
                        .HasForeignKey("CustomerAddressId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CustomerAddress");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("HT.Database.WebsiteAuthor", b =>
                {
                    b.HasOne("HT.Database.WebsiteMedia", "ImageWebsiteMedia")
                        .WithMany()
                        .HasForeignKey("ImageWebsiteMediaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Website", "Website")
                        .WithMany()
                        .HasForeignKey("WebsiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ImageWebsiteMedia");

                    b.Navigation("Website");
                });

            modelBuilder.Entity("HT.Database.WebsiteDomain", b =>
                {
                    b.HasOne("HT.Database.Website", "Website")
                        .WithMany()
                        .HasForeignKey("WebsiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Website");
                });

            modelBuilder.Entity("HT.Database.WebsiteMedia", b =>
                {
                    b.HasOne("HT.Database.Website", "Website")
                        .WithMany()
                        .HasForeignKey("WebsiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.WebsiteMediaFolder", "WebsiteMediaFolder")
                        .WithMany()
                        .HasForeignKey("WebsiteMediaFolderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Website");

                    b.Navigation("WebsiteMediaFolder");
                });

            modelBuilder.Entity("HT.Database.WebsiteMediaFolder", b =>
                {
                    b.HasOne("HT.Database.WebsiteMediaFolder", "ParentWebsiteMediaFolder")
                        .WithMany()
                        .HasForeignKey("ParentWebsiteMediaFolderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Website", "Website")
                        .WithMany()
                        .HasForeignKey("WebsiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ParentWebsiteMediaFolder");

                    b.Navigation("Website");
                });

            modelBuilder.Entity("HT.Database.WebsiteNewsletter", b =>
                {
                    b.HasOne("HT.Database.Website", "Website")
                        .WithMany()
                        .HasForeignKey("WebsiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Website");
                });

            modelBuilder.Entity("HT.Database.WebsitePage", b =>
                {
                    b.HasOne("HT.Database.WebsiteMedia", "ThumbnailWebsiteMedia")
                        .WithMany()
                        .HasForeignKey("ThumbnailWebsiteMediaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.WebsiteAuthor", "WebsiteAuthor")
                        .WithMany()
                        .HasForeignKey("WebsiteAuthorId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Website", "Website")
                        .WithMany()
                        .HasForeignKey("WebsiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.WebsitePageFolder", "WebsitePageFolder")
                        .WithMany()
                        .HasForeignKey("WebsitePageFolderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ThumbnailWebsiteMedia");

                    b.Navigation("Website");

                    b.Navigation("WebsiteAuthor");

                    b.Navigation("WebsitePageFolder");
                });

            modelBuilder.Entity("HT.Database.WebsitePageBlock", b =>
                {
                    b.HasOne("HT.Database.WebsiteMedia", "WebsiteMedia")
                        .WithMany()
                        .HasForeignKey("WebsiteMediaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.WebsitePage", "WebsitePage")
                        .WithMany("WebsitePageBlocks")
                        .HasForeignKey("WebsitePageId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("WebsiteMedia");

                    b.Navigation("WebsitePage");
                });

            modelBuilder.Entity("HT.Database.WebsitePageBlockLink", b =>
                {
                    b.HasOne("HT.Database.WebsiteMediaFolder", "WebsiteMediaFolder")
                        .WithMany()
                        .HasForeignKey("WebsiteMediaFolderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.WebsiteMedia", "WebsiteMedia")
                        .WithMany("WebsitePageBlockLinks")
                        .HasForeignKey("WebsiteMediaId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.WebsitePageBlock", "WebsitePageBlock")
                        .WithMany("WebsitePageBlockLinks")
                        .HasForeignKey("WebsitePageBlockId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HT.Database.WebsitePageFolder", "WebsitePageFolder")
                        .WithMany()
                        .HasForeignKey("WebsitePageFolderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.WebsitePage", "WebsitePage")
                        .WithMany()
                        .HasForeignKey("WebsitePageId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("WebsiteMedia");

                    b.Navigation("WebsiteMediaFolder");

                    b.Navigation("WebsitePage");

                    b.Navigation("WebsitePageBlock");

                    b.Navigation("WebsitePageFolder");
                });

            modelBuilder.Entity("HT.Database.WebsitePageFolder", b =>
                {
                    b.HasOne("HT.Database.WebsitePageFolder", "ParentWebsitePageFolder")
                        .WithMany()
                        .HasForeignKey("ParentWebsitePageFolderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HT.Database.Website", "Website")
                        .WithMany()
                        .HasForeignKey("WebsiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ParentWebsitePageFolder");

                    b.Navigation("Website");
                });

            modelBuilder.Entity("HT.Database.WebsiteQuote", b =>
                {
                    b.HasOne("HT.Database.Website", "Website")
                        .WithMany()
                        .HasForeignKey("WebsiteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Website");
                });

            modelBuilder.Entity("HT.Database.Contract", b =>
                {
                    b.Navigation("ContractLinks");

                    b.Navigation("ETickets");

                    b.Navigation("HaulierStops");
                });

            modelBuilder.Entity("HT.Database.Customer", b =>
                {
                    b.Navigation("ContractLinks");

                    b.Navigation("CustomerPayments");

                    b.Navigation("CustomerTagLinks");

                    b.Navigation("ETickets");

                    b.Navigation("Orders");

                    b.Navigation("Visits");
                });

            modelBuilder.Entity("HT.Database.CustomerSiteLink", b =>
                {
                    b.Navigation("Customers");

                    b.Navigation("Sites");
                });

            modelBuilder.Entity("HT.Database.CustomerTag", b =>
                {
                    b.Navigation("CustomerTagLinks");
                });

            modelBuilder.Entity("HT.Database.Email", b =>
                {
                    b.Navigation("EmailAttachments");
                });

            modelBuilder.Entity("HT.Database.Haulier", b =>
                {
                    b.Navigation("HaulierCustomerLinks");

                    b.Navigation("HaulierVehicles");
                });

            modelBuilder.Entity("HT.Database.HaulierStop", b =>
                {
                    b.Navigation("HaulierStopFiles");
                });

            modelBuilder.Entity("HT.Database.Invoice", b =>
                {
                    b.Navigation("InvoiceLines");
                });

            modelBuilder.Entity("HT.Database.Order", b =>
                {
                    b.Navigation("OrderLines");

                    b.Navigation("Payments");

                    b.Navigation("RouteLines");
                });

            modelBuilder.Entity("HT.Database.Product", b =>
                {
                    b.Navigation("ProductPrices");
                });

            modelBuilder.Entity("HT.Database.Route", b =>
                {
                    b.Navigation("HaulierStops");

                    b.Navigation("RouteCashAmounts");

                    b.Navigation("RouteLines");

                    b.Navigation("RouteProducts");
                });

            modelBuilder.Entity("HT.Database.RouteArea", b =>
                {
                    b.Navigation("RouteAreaLinks");

                    b.Navigation("RouteCustomers");

                    b.Navigation("Routes");
                });

            modelBuilder.Entity("HT.Database.Site", b =>
                {
                    b.Navigation("SitePermits");
                });

            modelBuilder.Entity("HT.Database.SitePermit", b =>
                {
                    b.Navigation("SitePermitProductLinks");
                });

            modelBuilder.Entity("HT.Database.Supplier", b =>
                {
                    b.Navigation("Purchases");
                });

            modelBuilder.Entity("HT.Database.User", b =>
                {
                    b.Navigation("HaulierVehicle");

                    b.Navigation("UserSiteLinks");
                });

            modelBuilder.Entity("HT.Database.WebsiteMedia", b =>
                {
                    b.Navigation("WebsitePageBlockLinks");
                });

            modelBuilder.Entity("HT.Database.WebsitePage", b =>
                {
                    b.Navigation("WebsitePageBlocks");
                });

            modelBuilder.Entity("HT.Database.WebsitePageBlock", b =>
                {
                    b.Navigation("WebsitePageBlockLinks");
                });
#pragma warning restore 612, 618
        }
    }
}
