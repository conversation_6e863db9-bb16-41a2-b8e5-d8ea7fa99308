﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class HaulierStopsTable : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "HaulierStops",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ContractId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                RouteId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                VisitDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                Notes = table.Column<string>(type: "nvarchar(max)", nullable: true),
                CreatedDateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                AddressId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Time = table.Column<DateTime>(type: "datetime2", nullable: false),
                PhotoRequired = table.Column<bool>(type: "bit", nullable: false),
                SignatureRequired = table.Column<bool>(type: "bit", nullable: false),
                Type = table.Column<int>(type: "int", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_HaulierStops", x => x.Id);
                table.ForeignKey(
                    name: "FK_HaulierStops_Addresses_AddressId",
                    column: x => x.AddressId,
                    principalTable: "Addresses",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_HaulierStops_Contracts_ContractId",
                    column: x => x.ContractId,
                    principalTable: "Contracts",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_HaulierStops_Routes_RouteId",
                    column: x => x.RouteId,
                    principalTable: "Routes",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_HaulierStops_Tenants_TenantId",
                    column: x => x.TenantId,
                    principalTable: "Tenants",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateIndex(
            name: "IX_HaulierStops_AddressId",
            table: "HaulierStops",
            column: "AddressId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierStops_ContractId",
            table: "HaulierStops",
            column: "ContractId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierStops_RouteId",
            table: "HaulierStops",
            column: "RouteId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierStops_TenantId",
            table: "HaulierStops",
            column: "TenantId");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "HaulierStops");
    }
}
