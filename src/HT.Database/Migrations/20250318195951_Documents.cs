﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class Documents : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ETicketId",
                table: "Documents",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Documents_ETicketId",
                table: "Documents",
                column: "ETicketId");

            migrationBuilder.AddForeignKey(
                name: "FK_Documents_ETickets_ETicketId",
                table: "Documents",
                column: "ETicketId",
                principalTable: "ETickets",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Documents_ETickets_ETicketId",
                table: "Documents");

            migrationBuilder.DropIndex(
                name: "IX_Documents_ETicketId",
                table: "Documents");

            migrationBuilder.DropColumn(
                name: "ETicketId",
                table: "Documents");
        }
    }
}
