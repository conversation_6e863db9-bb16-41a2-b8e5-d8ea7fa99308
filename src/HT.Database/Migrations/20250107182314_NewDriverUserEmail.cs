﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class NewDriverUserEmail : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        string sql = @"INSERT INTO dbo.EmailTemplates (Id, [Type], [Subject], [HtmlTemplate], [TextTemplate])
                        SELECT NEWID(), 
                            3,
                            'Heavy Tech - New User Account',
                            '<!DOCTYPE html><html><head><meta charset=""utf-8""><title>Heavy Tech - New User</title></head><body>
                            <p>Hi,</p><br />
                            <p>Your user account has been created with the password  {{Password}}</p><br /><br />
                            <a href=""https://app.heavytech.co.uk"">Please click here to log in.</a></body></html>', 
                            'Hi, Your user account has been created with the password {{Password}}. Please copy this link into your browser to log in. https://app.heavytech.co.uk';";

        migrationBuilder.Sql(sql);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {

    }
}
