﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class HaulierDrivers3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_HaulierVehicles_DriverUserId",
                table: "HaulierVehicles");

            migrationBuilder.CreateIndex(
                name: "IX_HaulierVehicles_DriverUserId",
                table: "HaulierVehicles",
                column: "DriverUserId",
                unique: true,
                filter: "[DriverUserId] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_HaulierVehicles_DriverUserId",
                table: "HaulierVehicles");

            migrationBuilder.CreateIndex(
                name: "IX_HaulierVehicles_DriverUserId",
                table: "HaulierVehicles",
                column: "DriverUserId");
        }
    }
}
