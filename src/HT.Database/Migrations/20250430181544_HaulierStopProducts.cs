﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class HaulierStopProducts : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "HaulierStopProductLines",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                HaulierStopId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                LineNumber = table.Column<int>(type: "int", nullable: false),
                ProductName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                Quantity = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                UnitPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                UnitTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                VATRate = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                VATTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                Total = table.Column<decimal>(type: "decimal(18,2)", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_HaulierStopProductLines", x => x.Id);
                table.ForeignKey(
                    name: "FK_HaulierStopProductLines_HaulierStops_HaulierStopId",
                    column: x => x.HaulierStopId,
                    principalTable: "HaulierStops",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_HaulierStopProductLines_Tenants_TenantId",
                    column: x => x.TenantId,
                    principalTable: "Tenants",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateIndex(
            name: "IX_HaulierStopProductLines_HaulierStopId",
            table: "HaulierStopProductLines",
            column: "HaulierStopId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierStopProductLines_TenantId",
            table: "HaulierStopProductLines",
            column: "TenantId");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "HaulierStopProductLines");
    }
}
