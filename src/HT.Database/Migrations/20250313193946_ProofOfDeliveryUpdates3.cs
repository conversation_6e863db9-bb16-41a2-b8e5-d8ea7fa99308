﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class ProofOfDeliveryUpdates3 : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<string>(
            name: "EWCCode",
            table: "ProofOfDeliveries",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "ProductName",
            table: "ProofOfDeliveries",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "SignedByName",
            table: "ProofOfDeliveries",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);

        migrationBuilder.AddColumn<string>(
            name: "SiteEmail",
            table: "Contracts",
            type: "nvarchar(100)",
            maxLength: 100,
            nullable: true);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "EWCCode",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "ProductName",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "SignedByName",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "SiteEmail",
            table: "Contracts");
    }
}
