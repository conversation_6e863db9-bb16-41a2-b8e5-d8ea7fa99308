﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class Tonnes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Tonnes",
                table: "ETickets",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "ETickets",
                type: "int",
                nullable: false,
                defaultValue: 0);

            // Update all existing records to have Type = 1
            migrationBuilder.Sql("UPDATE ETickets SET Type = 1");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Tonnes",
                table: "ETickets");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "ETickets");
        }
    }
}
