﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class HaulierDrivers2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Vehicles_Users_DriverUserId",
                table: "Vehicles");

            migrationBuilder.DropIndex(
                name: "IX_Vehicles_DriverUserId",
                table: "Vehicles");

            migrationBuilder.DropColumn(
                name: "DriverUserId",
                table: "Vehicles");

            migrationBuilder.AddColumn<Guid>(
                name: "DriverUserId",
                table: "HaulierVehicles",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_HaulierVehicles_DriverUserId",
                table: "HaulierVehicles",
                column: "DriverUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_HaulierVehicles_Users_DriverUserId",
                table: "HaulierVehicles",
                column: "DriverUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_HaulierVehicles_Users_DriverUserId",
                table: "HaulierVehicles");

            migrationBuilder.DropIndex(
                name: "IX_HaulierVehicles_DriverUserId",
                table: "HaulierVehicles");

            migrationBuilder.DropColumn(
                name: "DriverUserId",
                table: "HaulierVehicles");

            migrationBuilder.AddColumn<Guid>(
                name: "DriverUserId",
                table: "Vehicles",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Vehicles_DriverUserId",
                table: "Vehicles",
                column: "DriverUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_Vehicles_Users_DriverUserId",
                table: "Vehicles",
                column: "DriverUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
