﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class TipStopId : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "TipStopId",
            table: "HaulierStops",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_HaulierStops_TipStopId",
            table: "HaulierStops",
            column: "TipStopId");

        migrationBuilder.AddForeignKey(
            name: "FK_HaulierStops_HaulierStops_TipStopId",
            table: "HaulierStops",
            column: "TipStopId",
            principalTable: "HaulierStops",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_HaulierStops_HaulierStops_TipStopId",
            table: "HaulierStops");

        migrationBuilder.DropIndex(
            name: "IX_HaulierStops_TipStopId",
            table: "HaulierStops");

        migrationBuilder.DropColumn(
            name: "TipStopId",
            table: "HaulierStops");
    }
}
