﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class TareAndGrossWeight : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<decimal>(
            name: "GrossWeight",
            table: "HaulierStops",
            type: "decimal(18,2)",
            nullable: true);

        migrationBuilder.AddColumn<decimal>(
            name: "TareWeight",
            table: "HaulierStops",
            type: "decimal(18,2)",
            nullable: true);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "GrossWeight",
            table: "HaulierStops");

        migrationBuilder.DropColumn(
            name: "<PERSON>reWei<PERSON>",
            table: "HaulierStops");
    }
}
