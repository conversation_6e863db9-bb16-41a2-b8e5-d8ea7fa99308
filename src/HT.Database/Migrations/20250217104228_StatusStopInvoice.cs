﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class StatusStopInvoice : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<Guid>(
                name: "ETicketId",
                table: "InvoiceLines",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<Guid>(
                name: "HaulierStopId",
                table: "InvoiceLines",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceLines_HaulierStopId",
                table: "InvoiceLines",
                column: "HaulierStopId");

            migrationBuilder.AddForeignKey(
                name: "FK_InvoiceLines_HaulierStops_HaulierStopId",
                table: "InvoiceLines",
                column: "HaulierStopId",
                principalTable: "HaulierStops",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_InvoiceLines_HaulierStops_HaulierStopId",
                table: "InvoiceLines");

            migrationBuilder.DropIndex(
                name: "IX_InvoiceLines_HaulierStopId",
                table: "InvoiceLines");

            migrationBuilder.DropColumn(
                name: "HaulierStopId",
                table: "InvoiceLines");

            migrationBuilder.AlterColumn<Guid>(
                name: "ETicketId",
                table: "InvoiceLines",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);
        }
    }
}
