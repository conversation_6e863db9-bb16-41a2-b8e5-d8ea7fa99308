﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class ProofOfDeliveryTable2 : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "ContractId",
            table: "ProofOfDeliveries",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<Guid>(
            name: "CustomerId",
            table: "ProofOfDeliveries",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_ContractId",
            table: "ProofOfDeliveries",
            column: "ContractId");

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_CustomerId",
            table: "ProofOfDeliveries",
            column: "CustomerId");

        migrationBuilder.AddForeignKey(
            name: "FK_ProofOfDeliveries_Contracts_ContractId",
            table: "ProofOfDeliveries",
            column: "ContractId",
            principalTable: "Contracts",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            name: "FK_ProofOfDeliveries_Customers_CustomerId",
            table: "ProofOfDeliveries",
            column: "CustomerId",
            principalTable: "Customers",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_ProofOfDeliveries_Contracts_ContractId",
            table: "ProofOfDeliveries");

        migrationBuilder.DropForeignKey(
            name: "FK_ProofOfDeliveries_Customers_CustomerId",
            table: "ProofOfDeliveries");

        migrationBuilder.DropIndex(
            name: "IX_ProofOfDeliveries_ContractId",
            table: "ProofOfDeliveries");

        migrationBuilder.DropIndex(
            name: "IX_ProofOfDeliveries_CustomerId",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "ContractId",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "CustomerId",
            table: "ProofOfDeliveries");
    }
}
