﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class RouteHaulierDriver : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "HaulierDriverUserId",
            table: "Routes",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<Guid>(
            name: "HaulierId",
            table: "Routes",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<Guid>(
            name: "HaulierVehicleId",
            table: "Routes",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_Routes_HaulierDriverUserId",
            table: "Routes",
            column: "HaulierDriverUserId");

        migrationBuilder.CreateIndex(
            name: "IX_Routes_HaulierId",
            table: "Routes",
            column: "HaulierId");

        migrationBuilder.CreateIndex(
            name: "IX_Routes_HaulierVehicleId",
            table: "Routes",
            column: "HaulierVehicleId");

        migrationBuilder.AddForeignKey(
            name: "FK_Routes_HaulierVehicles_HaulierVehicleId",
            table: "Routes",
            column: "HaulierVehicleId",
            principalTable: "HaulierVehicles",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            name: "FK_Routes_Hauliers_HaulierId",
            table: "Routes",
            column: "HaulierId",
            principalTable: "Hauliers",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            name: "FK_Routes_Users_HaulierDriverUserId",
            table: "Routes",
            column: "HaulierDriverUserId",
            principalTable: "Users",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_Routes_HaulierVehicles_HaulierVehicleId",
            table: "Routes");

        migrationBuilder.DropForeignKey(
            name: "FK_Routes_Hauliers_HaulierId",
            table: "Routes");

        migrationBuilder.DropForeignKey(
            name: "FK_Routes_Users_HaulierDriverUserId",
            table: "Routes");

        migrationBuilder.DropIndex(
            name: "IX_Routes_HaulierDriverUserId",
            table: "Routes");

        migrationBuilder.DropIndex(
            name: "IX_Routes_HaulierId",
            table: "Routes");

        migrationBuilder.DropIndex(
            name: "IX_Routes_HaulierVehicleId",
            table: "Routes");

        migrationBuilder.DropColumn(
            name: "HaulierDriverUserId",
            table: "Routes");

        migrationBuilder.DropColumn(
            name: "HaulierId",
            table: "Routes");

        migrationBuilder.DropColumn(
            name: "HaulierVehicleId",
            table: "Routes");
    }
}
