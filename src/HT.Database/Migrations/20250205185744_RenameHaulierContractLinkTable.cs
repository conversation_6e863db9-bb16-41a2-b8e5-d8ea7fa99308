﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class RenameHaulierContractLinkTable : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "HaulierContractProductLinks");

        migrationBuilder.CreateTable(
            name: "HaulierCustomerProductLinks",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                HaulierCustomerLinkId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_HaulierCustomerProductLinks", x => x.Id);
                table.ForeignKey(
                    name: "FK_HaulierCustomerProductLinks_HaulierCustomerLinks_HaulierCustomerLinkId",
                    column: x => x.HaulierCustomerLinkId,
                    principalTable: "HaulierCustomerLinks",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_HaulierCustomerProductLinks_Products_ProductId",
                    column: x => x.ProductId,
                    principalTable: "Products",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_HaulierCustomerProductLinks_Tenants_TenantId",
                    column: x => x.TenantId,
                    principalTable: "Tenants",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateIndex(
            name: "IX_HaulierCustomerProductLinks_HaulierCustomerLinkId",
            table: "HaulierCustomerProductLinks",
            column: "HaulierCustomerLinkId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierCustomerProductLinks_ProductId",
            table: "HaulierCustomerProductLinks",
            column: "ProductId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierCustomerProductLinks_TenantId",
            table: "HaulierCustomerProductLinks",
            column: "TenantId");

    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "HaulierCustomerProductLinks");

        migrationBuilder.CreateTable(
            name: "HaulierContractProductLinks",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                HaulierContractId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_HaulierContractProductLinks", x => x.Id);
                table.ForeignKey(
                    name: "FK_HaulierContractProductLinks_Contracts_HaulierContractId",
                    column: x => x.HaulierContractId,
                    principalTable: "Contracts",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_HaulierContractProductLinks_Products_ProductId",
                    column: x => x.ProductId,
                    principalTable: "Products",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_HaulierContractProductLinks_Tenants_TenantId",
                    column: x => x.TenantId,
                    principalTable: "Tenants",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateIndex(
            name: "IX_HaulierContractProductLinks_HaulierContractId",
            table: "HaulierContractProductLinks",
            column: "HaulierContractId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierContractProductLinks_ProductId",
            table: "HaulierContractProductLinks",
            column: "ProductId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierContractProductLinks_TenantId",
            table: "HaulierContractProductLinks",
            column: "TenantId");
    }
}
