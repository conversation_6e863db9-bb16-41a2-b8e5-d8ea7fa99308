﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class InternalTransfer : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "InternalTransferFromLocationId",
            table: "Jobs",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<Guid>(
            name: "InternalTransferToLocationId",
            table: "Jobs",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<bool>(
            name: "IsInternalTransfer",
            table: "Jobs",
            type: "bit",
            nullable: false,
            defaultValue: false);

        migrationBuilder.CreateTable(
            name: "InternalTransferLocations",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_InternalTransferLocations", x => x.Id);
                table.ForeignKey(
                    name: "FK_InternalTransferLocations_Tenants_TenantId",
                    column: x => x.TenantId,
                    principalTable: "Tenants",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateIndex(
            name: "IX_Jobs_InternalTransferFromLocationId",
            table: "Jobs",
            column: "InternalTransferFromLocationId");

        migrationBuilder.CreateIndex(
            name: "IX_Jobs_InternalTransferToLocationId",
            table: "Jobs",
            column: "InternalTransferToLocationId");

        migrationBuilder.CreateIndex(
            name: "IX_InternalTransferLocations_TenantId",
            table: "InternalTransferLocations",
            column: "TenantId");

        migrationBuilder.AddForeignKey(
            name: "FK_Jobs_InternalTransferLocations_InternalTransferFromLocationId",
            table: "Jobs",
            column: "InternalTransferFromLocationId",
            principalTable: "InternalTransferLocations",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            name: "FK_Jobs_InternalTransferLocations_InternalTransferToLocationId",
            table: "Jobs",
            column: "InternalTransferToLocationId",
            principalTable: "InternalTransferLocations",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_Jobs_InternalTransferLocations_InternalTransferFromLocationId",
            table: "Jobs");

        migrationBuilder.DropForeignKey(
            name: "FK_Jobs_InternalTransferLocations_InternalTransferToLocationId",
            table: "Jobs");

        migrationBuilder.DropTable(
            name: "InternalTransferLocations");

        migrationBuilder.DropIndex(
            name: "IX_Jobs_InternalTransferFromLocationId",
            table: "Jobs");

        migrationBuilder.DropIndex(
            name: "IX_Jobs_InternalTransferToLocationId",
            table: "Jobs");

        migrationBuilder.DropColumn(
            name: "InternalTransferFromLocationId",
            table: "Jobs");

        migrationBuilder.DropColumn(
            name: "InternalTransferToLocationId",
            table: "Jobs");

        migrationBuilder.DropColumn(
            name: "IsInternalTransfer",
            table: "Jobs");
    }
}
