﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class ProofOfDeliveryUpdates : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_ProofOfDeliveries_RouteLines_RouteLineId",
            table: "ProofOfDeliveries");

        migrationBuilder.DropForeignKey(
            name: "FK_ProofOfDeliveries_RouteLines_TipStopRouteLineId",
            table: "ProofOfDeliveries");

        migrationBuilder.DropIndex(
            name: "IX_ProofOfDeliveries_RouteLineId",
            table: "ProofOfDeliveries");

        migrationBuilder.DropIndex(
            name: "IX_ProofOfDeliveries_TipStopRouteLineId",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "RouteLineId",
            table: "ProofOfDeliveries");

        migrationBuilder.DropColumn(
            name: "TipStopRouteLineId",
            table: "ProofOfDeliveries");

        migrationBuilder.AddColumn<Guid>(
            name: "ProofOfDeliveryId",
            table: "HaulierStops",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_HaulierStops_ProofOfDeliveryId",
            table: "HaulierStops",
            column: "ProofOfDeliveryId");

        migrationBuilder.AddForeignKey(
            name: "FK_HaulierStops_ProofOfDeliveries_ProofOfDeliveryId",
            table: "HaulierStops",
            column: "ProofOfDeliveryId",
            principalTable: "ProofOfDeliveries",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_HaulierStops_ProofOfDeliveries_ProofOfDeliveryId",
            table: "HaulierStops");

        migrationBuilder.DropIndex(
            name: "IX_HaulierStops_ProofOfDeliveryId",
            table: "HaulierStops");

        migrationBuilder.DropColumn(
            name: "ProofOfDeliveryId",
            table: "HaulierStops");

        migrationBuilder.AddColumn<Guid>(
            name: "RouteLineId",
            table: "ProofOfDeliveries",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<Guid>(
            name: "TipStopRouteLineId",
            table: "ProofOfDeliveries",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_RouteLineId",
            table: "ProofOfDeliveries",
            column: "RouteLineId");

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_TipStopRouteLineId",
            table: "ProofOfDeliveries",
            column: "TipStopRouteLineId");

        migrationBuilder.AddForeignKey(
            name: "FK_ProofOfDeliveries_RouteLines_RouteLineId",
            table: "ProofOfDeliveries",
            column: "RouteLineId",
            principalTable: "RouteLines",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            name: "FK_ProofOfDeliveries_RouteLines_TipStopRouteLineId",
            table: "ProofOfDeliveries",
            column: "TipStopRouteLineId",
            principalTable: "RouteLines",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }
}
