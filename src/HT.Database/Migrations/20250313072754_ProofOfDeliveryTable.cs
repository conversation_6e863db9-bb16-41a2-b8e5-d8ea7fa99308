﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class ProofOfDeliveryTable : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<Guid>(
            name: "ProofOfDeliveryId",
            table: "HaulierVehicles",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.AddColumn<Guid>(
            name: "ProofOfDeliveryId",
            table: "HaulierCustomerLinks",
            type: "uniqueidentifier",
            nullable: true);

        migrationBuilder.CreateTable(
            name: "ProofOfDeliveries",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                RouteLineId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                TipStopRouteLineId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                OrderNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                ContractName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                CustomerName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                SignatureFileId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                PhotoFileId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                StopCompletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                TipStopCompletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                StopAddressId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                TipStopAddressId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                TareWeight = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                GrossWeight = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                VehicleRegistration = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                DriverName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                Status = table.Column<int>(type: "int", nullable: false),
                Code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                CarrierLicence = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                CarrierLicenceExpiryDate = table.Column<DateTime>(type: "datetime2", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ProofOfDeliveries", x => x.Id);
                table.ForeignKey(
                    name: "FK_ProofOfDeliveries_Addresses_StopAddressId",
                    column: x => x.StopAddressId,
                    principalTable: "Addresses",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_ProofOfDeliveries_Addresses_TipStopAddressId",
                    column: x => x.TipStopAddressId,
                    principalTable: "Addresses",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_ProofOfDeliveries_HaulierStopFiles_PhotoFileId",
                    column: x => x.PhotoFileId,
                    principalTable: "HaulierStopFiles",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_ProofOfDeliveries_HaulierStopFiles_SignatureFileId",
                    column: x => x.SignatureFileId,
                    principalTable: "HaulierStopFiles",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_ProofOfDeliveries_RouteLines_RouteLineId",
                    column: x => x.RouteLineId,
                    principalTable: "RouteLines",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_ProofOfDeliveries_RouteLines_TipStopRouteLineId",
                    column: x => x.TipStopRouteLineId,
                    principalTable: "RouteLines",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_ProofOfDeliveries_Tenants_TenantId",
                    column: x => x.TenantId,
                    principalTable: "Tenants",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateIndex(
            name: "IX_HaulierVehicles_ProofOfDeliveryId",
            table: "HaulierVehicles",
            column: "ProofOfDeliveryId");

        migrationBuilder.CreateIndex(
            name: "IX_HaulierCustomerLinks_ProofOfDeliveryId",
            table: "HaulierCustomerLinks",
            column: "ProofOfDeliveryId");

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_PhotoFileId",
            table: "ProofOfDeliveries",
            column: "PhotoFileId");

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_RouteLineId",
            table: "ProofOfDeliveries",
            column: "RouteLineId");

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_SignatureFileId",
            table: "ProofOfDeliveries",
            column: "SignatureFileId");

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_StopAddressId",
            table: "ProofOfDeliveries",
            column: "StopAddressId");

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_TenantId",
            table: "ProofOfDeliveries",
            column: "TenantId");

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_TipStopAddressId",
            table: "ProofOfDeliveries",
            column: "TipStopAddressId");

        migrationBuilder.CreateIndex(
            name: "IX_ProofOfDeliveries_TipStopRouteLineId",
            table: "ProofOfDeliveries",
            column: "TipStopRouteLineId");

        migrationBuilder.AddForeignKey(
            name: "FK_HaulierCustomerLinks_ProofOfDeliveries_ProofOfDeliveryId",
            table: "HaulierCustomerLinks",
            column: "ProofOfDeliveryId",
            principalTable: "ProofOfDeliveries",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            name: "FK_HaulierVehicles_ProofOfDeliveries_ProofOfDeliveryId",
            table: "HaulierVehicles",
            column: "ProofOfDeliveryId",
            principalTable: "ProofOfDeliveries",
            principalColumn: "Id",
            onDelete: ReferentialAction.Restrict);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_HaulierCustomerLinks_ProofOfDeliveries_ProofOfDeliveryId",
            table: "HaulierCustomerLinks");

        migrationBuilder.DropForeignKey(
            name: "FK_HaulierVehicles_ProofOfDeliveries_ProofOfDeliveryId",
            table: "HaulierVehicles");

        migrationBuilder.DropTable(
            name: "ProofOfDeliveries");

        migrationBuilder.DropIndex(
            name: "IX_HaulierVehicles_ProofOfDeliveryId",
            table: "HaulierVehicles");

        migrationBuilder.DropIndex(
            name: "IX_HaulierCustomerLinks_ProofOfDeliveryId",
            table: "HaulierCustomerLinks");

        migrationBuilder.DropColumn(
            name: "ProofOfDeliveryId",
            table: "HaulierVehicles");

        migrationBuilder.DropColumn(
            name: "ProofOfDeliveryId",
            table: "HaulierCustomerLinks");
    }
}
