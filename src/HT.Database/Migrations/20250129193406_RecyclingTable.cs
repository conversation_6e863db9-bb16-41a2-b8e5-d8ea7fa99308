﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class RecyclingTable : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "Recycling",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TotalTonnage = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                TonnageRecycled = table.Column<decimal>(type: "decimal(18,2)", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_Recycling", x => x.Id);
                table.ForeignKey(
                    name: "FK_Recycling_Tenants_TenantId",
                    column: x => x.TenantId,
                    principalTable: "Tenants",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateIndex(
            name: "IX_Recycling_TenantId",
            table: "Recycling",
            column: "TenantId");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "Recycling");
    }
}
