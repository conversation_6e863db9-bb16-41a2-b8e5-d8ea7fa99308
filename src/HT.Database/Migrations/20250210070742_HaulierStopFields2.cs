﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class HaulierStopFields2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PriceIsTonnes",
                table: "HaulierStops");

            migrationBuilder.RenameColumn(
                name: "Price",
                table: "HaulierStops",
                newName: "PricePerTonne");

            migrationBuilder.AlterColumn<int>(
                name: "Tonnes",
                table: "HaulierStops",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "PricePerLoad",
                table: "HaulierStops",
                type: "decimal(18,2)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PricePerLoad",
                table: "HaulierStops");

            migrationBuilder.RenameColumn(
                name: "PricePerTonne",
                table: "HaulierStops",
                newName: "Price");

            migrationBuilder.AlterColumn<int>(
                name: "Tonnes",
                table: "HaulierStops",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<bool>(
                name: "PriceIsTonnes",
                table: "HaulierStops",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
