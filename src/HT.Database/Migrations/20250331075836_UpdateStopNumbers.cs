﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class UpdateStopNumbers : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        string sql = @"WITH OrderedStops AS (
                        SELECT 
                            Id,
                            OrderNumber,
                            Type,
                            StopNumber,
                            ROW_NUMBER() OVER (ORDER BY CreatedDateTime) AS RowNum
                        FROM 
                            HaulierStops
                        WHERE 
                            Type != 3
                    )
                    UPDATE OrderedStops
                    SET StopNumber = RowNum
                    ";

        migrationBuilder.Sql(sql);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {

    }
}
