﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace HT.Database.Migrations;

/// <inheritdoc />
public partial class FavouriteAddressTable : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "FavouriteAddresses",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                AddressId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_FavouriteAddresses", x => x.Id);
                table.ForeignKey(
                    name: "FK_FavouriteAddresses_Addresses_AddressId",
                    column: x => x.AddressId,
                    principalTable: "Addresses",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
                table.ForeignKey(
                    name: "FK_FavouriteAddresses_Tenants_TenantId",
                    column: x => x.TenantId,
                    principalTable: "Tenants",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Restrict);
            });

        migrationBuilder.CreateIndex(
            name: "IX_FavouriteAddresses_AddressId",
            table: "FavouriteAddresses",
            column: "AddressId");

        migrationBuilder.CreateIndex(
            name: "IX_FavouriteAddresses_TenantId",
            table: "FavouriteAddresses",
            column: "TenantId");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "FavouriteAddresses");
    }
}
