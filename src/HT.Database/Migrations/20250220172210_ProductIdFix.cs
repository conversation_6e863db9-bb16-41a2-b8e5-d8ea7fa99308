﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HT.Database.Migrations
{
    /// <inheritdoc />
    public partial class ProductIdFix : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ProductId",
                table: "ETickets",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ETickets_ProductId",
                table: "ETickets",
                column: "ProductId");

            migrationBuilder.AddForeignKey(
                name: "FK_ETickets_Products_ProductId",
                table: "ETickets",
                column: "ProductId",
                principalTable: "Products",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ETickets_Products_ProductId",
                table: "ETickets");

            migrationBuilder.DropIndex(
                name: "IX_ETickets_ProductId",
                table: "ETickets");

            migrationBuilder.DropColumn(
                name: "ProductId",
                table: "ETickets");
        }
    }
}
