<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/25.0.2 Chrome/128.0.6613.186 Electron/32.2.5 Safari/537.36" version="25.0.2">
  <diagram name="Page-1" id="xxgHhTHQjW1FtW6O-X5U">
    <mxGraphModel dx="2474" dy="1197" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="0ltskOdEnOLIhQP9iIWm-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="quICpBoq2HOe1YFsXzf4-1" target="0ltskOdEnOLIhQP9iIWm-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="quICpBoq2HOe1YFsXzf4-1" target="0ltskOdEnOLIhQP9iIWm-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="quICpBoq2HOe1YFsXzf4-1" value="Route" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-200" y="270" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="0ltskOdEnOLIhQP9iIWm-2" target="0ltskOdEnOLIhQP9iIWm-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="0ltskOdEnOLIhQP9iIWm-2" target="0ltskOdEnOLIhQP9iIWm-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-2" value="UserLinks" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-3" value="User" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="720" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-4" value="Driver" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="20" y="360" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-9" value="Type = Driver" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="720" y="360" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-10" value="Has to have one" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="360" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="0ltskOdEnOLIhQP9iIWm-11" target="quICpBoq2HOe1YFsXzf4-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-11" value="RouteVisits" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-200" y="390" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="0ltskOdEnOLIhQP9iIWm-15" value="Vehicle" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="20" y="220" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
