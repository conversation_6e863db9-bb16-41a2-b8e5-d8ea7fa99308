# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Build and Run
- `dotnet build "src/Heavy Tech.sln"` - Build the main solution
- `dotnet run --project src/HT.Api` - Run API server (https://localhost:7044/)
- `dotnet run --project src/HT.Portal` - Run Portal (Blazor Server frontend)
- `dotnet run --project src/HT.Website/HT.Website` - Run public website

### Database Migrations
Use PowerShell from the solution root:
```powershell
Add-Migration -StartupProject HT.Api -Context "HT.Database.DatabaseContext" MIGRATION_NAME
```

### Development
- API and Portal run on different ports but are configured to work together
- Database is Azure SQL Database - connection string in appsettings.json
- Mobile app (HT.Drivers) is currently excluded from builds

## Architecture Overview

This is a **waste management and logistics platform** built with .NET 9.0, featuring:

### Core Projects
- **HT.Api** - REST API backend with JWT authentication
- **HT.Portal** - Blazor Server frontend with cookie authentication  
- **HT.Database** - Entity Framework Core data layer (50+ entities)
- **HT.Shared** - Common models, enums, and utilities
- **HT.Blazor** - Reusable Blazor component library
- **HT.Website** - Public-facing website
- **HT.Drivers** - MAUI mobile app (currently disabled)

### Business Domain
The platform manages:
- **Customer operations** - CRM, billing, service management
- **Fleet management** - Vehicles, drivers, routes, scheduling
- **Waste operations** - Weighbridge, transfers, product tracking
- **Financial operations** - Invoicing, payments, pricing
- **Content management** - Website CMS with Markdown support
- **Contractor management** - Haulier operations and coordination

### Key Architectural Patterns
- **Multi-layered architecture** with clear separation of concerns
- **Multi-tenancy** - Built-in tenant isolation throughout database
- **Service layer pattern** - Business logic encapsulated in service classes
- **Real-time communication** - SignalR hubs for live updates
- **Clean separation** - API serves data, Portal handles UI, shared models in HT.Shared

### Technology Stack
- **.NET 9.0** with ASP.NET Core and Blazor Server
- **Entity Framework Core** with SQL Server/Azure SQL Database
- **Authentication** - JWT for API, cookies for Portal
- **PDF generation** - DinkToPdf with wkhtmltox
- **Cloud storage** - Azure Blob Storage
- **AI integration** - OpenAI API
- **Email** - Postmark service
- **Real-time** - SignalR
- **Localization** - en-GB throughout

### Database Context
HT.Database.DatabaseContext contains 50+ entity sets representing the full business domain. The system has 100+ migrations indicating active development. Always use the PowerShell command above for new migrations.

### Development Notes
- Portal is configured to connect to API via HttpClient
- API includes comprehensive service registration in Program.cs
- Blazor components in HT.Blazor are designed for reuse across projects
- Mobile app development is currently on hold but architecture supports it
- System uses role-based security with multi-tenant data isolation